{"name": "qlik-sense-mashup-react", "version": "1.0.0", "description": "Mashup React moderno para Qlik Sense - compatível com Cloud e Enterprise", "main": "index.js", "scripts": {"dev": "vite --host 0.0.0.0 --port 3333", "build": "vite build", "build:silent": "vite build --logLevel warn", "build:mashup-enterprise": "npm run build && node scripts/build-mashup-enterprise.js", "build:mashup-cloud": "npm run build && node scripts/build-mashup-cloud.js", "build:mashup-enterprise-silent": "npm run build:silent && node scripts/build-mashup-enterprise.js", "build:mashup-cloud-silent": "npm run build:silent && node scripts/build-mashup-cloud.js", "build:enterprise": "cross-env VITE_TARGET=enterprise vite build && npm run copy:qext", "build:cloud": "cross-env VITE_TARGET=cloud vite build", "copy:qext": "cp src/qext/helpdesk.qext dist/", "preview": "vite preview", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "start": "vite --host 0.0.0.0 --port 3333", "test": "vitest", "eject": "echo 'Eject não disponível com Vite'"}, "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.11.6", "@mui/material": "^5.11.6", "axios": "^1.3.0", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-router-dom": "^6.8.0", "styled-components": "^5.3.6"}, "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-react": "^3.1.0", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "fs-extra": "^11.3.0", "vite": "^4.1.0"}, "keywords": ["qlik-sense", "mashup", "react", "qlik-cloud", "qlik-enterprise"], "author": "<PERSON><PERSON>", "license": "MIT"}