import React, { createContext, useContext, useState, useCallback, useRef } from 'react'

const QlikSelectionContext = createContext()

export const useQlikSelection = () => {
  const context = useContext(QlikSelectionContext)
  if (!context) {
    throw new Error('useQlikSelection must be used within a QlikSelectionProvider')
  }
  return context
}

export const QlikSelectionProvider = ({ children }) => {
  // Estado global das seleções por campo
  const [selections, setSelections] = useState(new Map())
  
  // Versão para forçar re-render de objetos
  const [selectionVersion, setSelectionVersion] = useState(0)
  
  // Cache de objetos que precisam ser atualizados
  const objectUpdateCallbacks = useRef(new Map())
  
  // Registrar callback de atualização de objeto
  const registerObjectUpdate = useCallback((objectId, callback) => {
    
    objectUpdateCallbacks.current.set(objectId, callback)
    
    // Retornar função de cleanup
    return () => {
      objectUpdateCallbacks.current.delete(objectId)
    }
  }, [])
  
  // Atualizar seleção de um campo específico
  const updateFieldSelection = useCallback((fieldName, selectedValues, sourceObjectId) => {
    
    setSelections(prev => {
      const newSelections = new Map(prev)
      
      if (selectedValues.size === 0) {
        newSelections.delete(fieldName)
      } else {
        newSelections.set(fieldName, new Set(selectedValues))
      }
      
      return newSelections
    })
    
    // Incrementar versão para forçar re-render
    setSelectionVersion(prev => prev + 1)
    
    // Notificar todos os objetos registrados (exceto o que originou a mudança)
    const notificationPromises = []
    objectUpdateCallbacks.current.forEach((callback, objectId) => {
      if (objectId !== sourceObjectId) {
        
        // Executar callback de forma assíncrona para não bloquear
        notificationPromises.push(
          Promise.resolve().then(() => callback(fieldName, selectedValues))
        )
      }
    })
    
    // Aguardar todas as notificações (opcional)
    Promise.all(notificationPromises).then(() => {
    })
    
  }, [])
  
  // Obter seleções de um campo específico
  const getFieldSelections = useCallback((fieldName) => {
    const fieldSelections = selections.get(fieldName) || new Set()
    return fieldSelections
  }, [selections])
  
  // Limpar todas as seleções
  const clearAllSelections = useCallback((sourceObjectId) => {
    
    setSelections(new Map())
    setSelectionVersion(prev => prev + 1)
    
    // Notificar todos os objetos
    objectUpdateCallbacks.current.forEach((callback, objectId) => {
      if (objectId !== sourceObjectId) {
        Promise.resolve().then(() => callback(null, new Set())) // null = clear all
      }
    })
  }, [selections])
  
  // Limpar seleções de um campo específico
  const clearFieldSelections = useCallback((fieldName, sourceObjectId) => {
    
    updateFieldSelection(fieldName, new Set(), sourceObjectId)
  }, [updateFieldSelection])
  
  const value = {
    selections,
    selectionVersion,
    updateFieldSelection,
    getFieldSelections,
    clearAllSelections,
    clearFieldSelections,
    registerObjectUpdate
  }
  
  return (
    <QlikSelectionContext.Provider value={value}>
      {children}
    </QlikSelectionContext.Provider>
  )
} 