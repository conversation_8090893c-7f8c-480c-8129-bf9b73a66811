const mongoose = require('mongoose');

const objetoSchema = new mongoose.Schema({
  empresaId: {
    type: String,
    required: true,
    trim: true,
    ref: 'Empresa'
  },
  appId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'App'
  },
  chave: {
    type: String,
    required: true,
    trim: true,
    lowercase: true
  },
  objectId: {
    type: String,
    required: true,
    trim: true
  },
  nome: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  descricao: {
    type: String,
    trim: true,
    maxlength: 500
  },
  tipo: {
    type: String,
    required: true,
    enum: [
      'kpi', 'multiKpi',
      'bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed',
      'table', 'filter', 'listbox', 'text', 'image', 'button', 
      'gauge', 'other'
    ],
    default: 'other'
  },
  categoria: {
    type: String,
    enum: ['dashboard', 'vendas', 'financeiro', 'helpdesk', 'rh', 'operacional', 'geral'],
    default: 'geral'
  },
  // Configurações específicas do objeto
  configuracao: {
    altura: {
      type: String,
      default: 'auto'
    },
    largura: {
      type: String,
      default: '100%'
    },
    // Configurações de exibição
    showTitle: {
      type: Boolean,
      default: true
    },
    showToolbar: {
      type: Boolean,
      default: true
    },
    // Configurações de interação
    allowInteraction: {
      type: Boolean,
      default: true
    },
    allowExport: {
      type: Boolean,
      default: false
    },
    // Configurações específicas para KPIs
    kpiConfig: {
      formato: {
        type: String,
        enum: ['numero', 'moeda', 'percentual', 'personalizado'],
        default: 'numero'
      },
      casasDecimais: {
        type: Number,
        default: 0,
        min: 0,
        max: 10
      },
      prefix: {
        type: String,
        default: ''
      },
      suffix: {
        type: String,
        default: ''
      },
      showTrend: {
        type: Boolean,
        default: false
      },
      // Configurações de cor e visual
      cor: {
        type: String,
        default: '#1976d2'
      },
      corFundo: {
        type: String,
        default: 'transparent'
      },
      corFonte: {
        type: String,
        default: '#ffffff'
      },
      gradiente: {
        type: Boolean,
        default: false
      },
      // Configurações de ícone
      icone: {
        type: String,
        enum: ['money', 'person', 'cart', 'timeline', 'assessment', 'star', 'heart', 'business', 'atm', 'bank', 'analytics'],
        default: 'analytics'
      },
      showIcon: {
        type: Boolean,
        default: true
      },
      // Configurações de texto (mantendo compatibilidade)
      color: {
        type: String,
        default: ''
      },
      backgroundColor: {
        type: String,
        default: 'transparent'
      },
      textAlign: {
        type: String,
        enum: ['left', 'center', 'right'],
        default: 'center'
      },
      fontSize: {
        type: String,
        default: 'auto'
      }
    },
    // Configurações específicas para gráficos
    chartConfig: {
      tipoGrafico: {
        type: String,
        enum: ['bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed', 'gauge'],
        default: 'bar'
      },
      showLegend: {
        type: Boolean,
        default: true
      },
      showDataLabels: {
        type: Boolean,
        default: false
      },
      color: {
        type: [String],
        default: []
      },
      backgroundColor: {
        type: String,
        default: 'transparent'
      },
      animacao: {
        type: Boolean,
        default: true
      },
      // Configurações específicas para gráfico misto (mixed)
      mixedConfig: {
        barSeries: {
          type: [Number],
          default: [0] // Índices das séries que devem ser barras
        },
        lineSeries: {
          type: [Number], 
          default: [1] // Índices das séries que devem ser linhas
        },
        // ✅ NOVO: Configurações de porcentagem
        usePercentageScale: {
          type: Boolean,
          default: false
        },
        percentageAxisMin: {
          type: Number,
          default: 0
        },
        percentageAxisMax: {
          type: Number,
          default: 100
        },
        autoDetectPercentage: {
          type: Boolean,
          default: true
        },
        valueAxisName: {
          type: String,
          default: 'Valores'
        },
        percentageAxisName: {
          type: String,
          default: 'Porcentagem (%)'
        }
      },
      // Configurações do DataZoom para ECharts
      dataZoom: {
        enabled: {
          type: Boolean,
          default: false
        },
        type: {
          type: String,
          enum: ['slider', 'inside', 'both'],
          default: 'slider'
        },
        start: {
          type: Number,
          default: 0,
          min: 0,
          max: 100
        },
        end: {
          type: Number,
          default: 100,
          min: 0,
          max: 100
        },
        showDetail: {
          type: Boolean,
          default: true
        },
        realtime: {
          type: Boolean,
          default: true
        }
      },
      grid: {
        top: {
          type: Number,
          default: 60
        },
        right: {
          type: Number,
          default: 30
        },
        bottom: {
          type: Number,
          default: 60
        },
        left: {
          type: Number,
          default: 60
        }
      },
      xAxis: {
        show: {
          type: Boolean,
          default: true
        },
        name: {
          type: String,
          default: ''
        }
      },
      yAxis: {
        show: {
          type: Boolean,
          default: true
        },
        name: {
          type: String,
          default: ''
        }
      }
    },
    // NOVO: Sistema de layout por página
    paginas: {
      type: [String],
      default: [],
      validate: {
        validator: function(arr) {
          if (!arr || arr.length === 0) return true;
          
          // Validação básica: deve ser um array de strings não vazias
          return arr.every(pagina => 
            typeof pagina === 'string' && 
            pagina.trim().length > 0 && 
            pagina.length <= 50 // Limite razoável para nome de página
          );
        },
        message: 'Páginas devem ser strings válidas e não vazias (máximo 50 caracteres cada).'
      }
    },
    layouts: {
      type: mongoose.Schema.Types.Mixed,
      default: {},
      validate: {
        validator: function(layouts) {
          // Validar que cada layout tem as propriedades corretas
          for (const [pagina, layout] of Object.entries(layouts)) {
            if (typeof layout !== 'object' || layout === null) return false;
            if (typeof layout.x !== 'number' || layout.x < 0) return false;
            if (typeof layout.y !== 'number' || layout.y < 0) return false;
            if (typeof layout.w !== 'number' || layout.w <= 0) return false;
            if (typeof layout.h !== 'number' || layout.h <= 0) return false;
          }
          return true;
        },
        message: 'Layout deve ter propriedades x, y, w, h numéricas válidas'
      }
    },
    // Configurações específicas para Multi KPI
    multiKpiConfig: {
      itemsPerRow: {
        type: Number,
        default: 3,
        min: 1,
        max: 6
      },
      spacing: {
        type: Number,
        default: 2,
        min: 0,
        max: 5
      },
      showTitle: {
        type: Boolean,
        default: true
      },
      showIcon: {
        type: Boolean,
        default: false
      },
      layout: {
        type: String,
        enum: ['grid', 'horizontal', 'unified'],
        default: 'unified'
      },
      elevation: {
        type: Number,
        default: 1,
        min: 0,
        max: 10
      },
      // Configurações visuais do Multi KPI
      cor: {
        type: String,
        default: '#1976d2'
      },
      corFonte: {
        type: String,
        default: '#ffffff'
      },
      // ✅ NOVO: Campo ícone para Multi KPI
      icone: {
        type: String,
        enum: ['money', 'person', 'cart', 'timeline', 'assessment', 'star', 'heart', 'business', 'atm', 'bank', 'analytics'],
        default: 'analytics'
      }
    },
    // Configurações específicas para filtros
    filterConfig: {
      showTitle: {
        type: Boolean,
        default: true
      },
      showSearchBox: {
        type: Boolean,
        default: true
      },
      showClearButton: {
        type: Boolean,
        default: true
      },
      allowMultiSelect: {
        type: Boolean,
        default: true
      },
      maxHeight: {
        type: String,
        default: '300px'
      },
      compact: {
        type: Boolean,
        default: false
      },
      backgroundColor: {
        type: String,
        default: 'transparent'
      },
      borderRadius: {
        type: String,
        default: '8px'
      },
      showSelectionCount: {
        type: Boolean,
        default: true
      },
      itemsPerRow: {
        type: Number,
        default: 1,
        min: 1,
        max: 6
      }
    }
  },
  // Ordem de exibição dentro do app
  ordem: {
    type: Number,
    default: 1
  },
  // Status
  ativo: {
    type: Boolean,
    default: true
  },
  // Metadados
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String,
    default: 'system'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices compostos para performance
objetoSchema.index({ empresaId: 1, appId: 1, ordem: 1 });
objetoSchema.index({ empresaId: 1, appId: 1, chave: 1 }, { unique: true });
objetoSchema.index({ empresaId: 1, tipo: 1 });
objetoSchema.index({ empresaId: 1, categoria: 1 });
objetoSchema.index({ ativo: 1 });

// Middleware para atualizar updatedAt
objetoSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Método para obter próxima ordem dentro do app
objetoSchema.statics.getProximaOrdem = async function(empresaId, appId) {
  const ultimoObjeto = await this.findOne({ empresaId, appId })
    .sort({ ordem: -1 })
    .select('ordem');
  
  return ultimoObjeto ? ultimoObjeto.ordem + 1 : 1;
};

// Método para reordenar objetos dentro de um app
objetoSchema.statics.reordenarObjetos = async function(empresaId, appId, objetosOrdenados) {
  const operations = objetosOrdenados.map((objeto, index) => ({
    updateOne: {
      filter: { _id: objeto._id, empresaId, appId },
      update: { ordem: index + 1 }
    }
  }));

  return this.bulkWrite(operations);
};

// Método para buscar objetos por app
objetoSchema.statics.findByApp = function(empresaId, appId, filtros = {}) {
  const query = { empresaId, appId };
  
  if (filtros.tipo) query.tipo = filtros.tipo;
  if (filtros.categoria) query.categoria = filtros.categoria;
  if (filtros.ativo !== undefined) query.ativo = filtros.ativo;
  
  return this.find(query).sort({ ordem: 1 });
};

// Método para buscar por chave única
objetoSchema.statics.findByChave = function(empresaId, appId, chave) {
  return this.findOne({ empresaId, appId, chave });
};

module.exports = mongoose.model('Objeto', objetoSchema); 