const mongoose = require('mongoose');

const paginaSchema = new mongoose.Schema({
  empresaId: {
    type: String,
    required: true,
    trim: true,
    ref: 'Empresa'
  },
  chave: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-_]+$/, 'Chave deve conter apenas letras minúsculas, números, hífens e underscores']
  },
  titulo: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  descricao: {
    type: String,
    trim: true,
    maxlength: 200
  },
  icone: {
    type: String,
    required: true,
    enum: [
      'dashboard', 'trending_up', 'support', 'account_balance', 
      'analytics', 'assignment', 'people', 'inventory',
      'shopping_cart', 'timeline', 'assessment', 'business',
      'local_atm', 'receipt', 'bar_chart', 'pie_chart'
    ],
    default: 'dashboard'
  },
  rota: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    match: [/^\/[a-z0-9-_\/]*$/, 'Rota deve começar com / e conter apenas letras minúsculas, números, hífens e underscores']
  },
  ordem: {
    type: Number,
    default: 1,
    min: 1
  },
  ativo: {
    type: Boolean,
    default: true
  },
  // Configurações de exibição
  configuracao: {
    showInSidebar: {
      type: Boolean,
      default: true
    },
    requiredRole: {
      type: String,
      default: null
    }
  },
  // Metadados
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String,
    default: 'system'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices compostos para performance
paginaSchema.index({ empresaId: 1, ordem: 1 });
paginaSchema.index({ empresaId: 1, ativo: 1 });
paginaSchema.index({ empresaId: 1, chave: 1 }, { unique: true });

// Middleware para atualizar updatedAt
paginaSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Método para obter próxima ordem
paginaSchema.statics.getProximaOrdem = async function(empresaId) {
  const ultimaPagina = await this.findOne({ empresaId })
    .sort({ ordem: -1 })
    .select('ordem');
  
  return ultimaPagina ? ultimaPagina.ordem + 1 : 1;
};

// Método para reordenar páginas
paginaSchema.statics.reordenarPaginas = async function(empresaId, paginasOrdenadas) {
  const operations = paginasOrdenadas.map((pagina, index) => ({
    updateOne: {
      filter: { _id: pagina._id, empresaId },
      update: { ordem: index + 1 }
    }
  }));

  return this.bulkWrite(operations);
};

// Método estático para buscar páginas ativas por empresa
paginaSchema.statics.findByEmpresa = function(empresaId, incluirInativas = false) {
  const query = { empresaId };
  if (!incluirInativas) {
    query.ativo = true;
  }
  return this.find(query).sort({ ordem: 1 });
};

module.exports = mongoose.model('Pagina', paginaSchema); 