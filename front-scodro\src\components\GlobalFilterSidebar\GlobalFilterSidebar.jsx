import React, { useState, useEffect } from 'react'
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip,
  Paper,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment
} from '@mui/material'
import {
  Close as CloseIcon,
  FilterList as FilterIcon,
  ExpandMore as ExpandMoreIcon,
  Clear as ClearIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon
} from '@mui/icons-material'
import { useQlikSelection } from '@contexts/QlikSelectionContext'
import { useEmpresa } from '@context/EmpresaContext'
import { empresaService, appService } from '@services/api'
import qlikService from '@services/qlik'

// ✅ NOVO: Componente de filtro simplificado para o sidebar
const SimpleFilter = ({ filterObj, empresaId }) => {
  const [filterData, setFilterData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [searchText, setSearchText] = useState('')
  const { getFieldSelections, updateFieldSelection } = useQlikSelection()

  useEffect(() => {
    const loadFilterData = async () => {
      try {

        // ✅ NOVO: Verificar se está em desenvolvimento
        const isDevelopment = process.env.NODE_ENV === 'development'
        
        // ✅ CORRIGIDO: Usar qlikAppId diretamente da API em vez de fazer segunda chamada
        let qlikAppId = filterObj.qlikAppId
        
        // ✅ NOVO: Em desenvolvimento, usar fallback se não tiver qlikAppId
        if (isDevelopment && !qlikAppId) {
          qlikAppId = 'desenvolvimento-mock.qvf'
        }
        
        if (!qlikAppId) {
          throw new Error(`App ID do Qlik não encontrado. Verifique se o app ${filterObj.appName || 'desconhecido'} está corretamente configurado.`)
        }

        // ✅ NOVO: Verificar se o qlikService está disponível
        if (!qlikService) {
          throw new Error('qlikService não está disponível')
        }


        const app = await qlikService.openApp(qlikAppId)
        if (!app) {
          throw new Error(`App ${qlikAppId} não foi encontrado no Qlik`)
        }

        // Tentar encontrar o campo baseado no nome do filtro
        const possibleFieldNames = [
          'Empreendimento',
          'Empreendimentos', 
          'Nome Empreendimento',
          'Desc Empreendimento',
          'Descrição Empreendimento',
          'EMPREENDIMENTO',
          'EMPREENDIMENTOS'
        ]

        app.getList('FieldList', (fieldListReply) => {

          let matchedField = null
          
          // Primeiro: busca exata
          for (const possibleName of possibleFieldNames) {
            matchedField = fieldListReply?.qFieldList?.qItems?.find(field => 
              field.qName.toLowerCase() === possibleName.toLowerCase()
            )
            if (matchedField) break
          }
          
          // Segundo: busca parcial
          if (!matchedField) {
            matchedField = fieldListReply?.qFieldList?.qItems?.find(field => 
              field.qName.toLowerCase().includes('empreendimento') ||
              field.qName.toLowerCase().includes('empreend')
            )
          }

          // Terceiro: pegar o primeiro campo disponível como fallback
          if (!matchedField && fieldListReply?.qFieldList?.qItems?.length > 0) {
            matchedField = fieldListReply.qFieldList.qItems[0]
          }

          if (matchedField) {

            // Criar listbox para o campo
            const listObjectDef = {
              qDef: {
                qFieldDefs: [matchedField.qName]
              },
              qInitialDataFetch: [{
                qTop: 0,
                qLeft: 0,
                qHeight: Math.min(matchedField.qCardinal, 100),
                qWidth: 1
              }]
            }

            app.createList(listObjectDef, (listData) => {

              if (listData?.qListObject?.qDataPages?.[0]?.qMatrix) {
                const items = listData.qListObject.qDataPages[0].qMatrix.map(row => ({
                  value: row[0].qText,
                  selected: row[0].qState === 'S'
                }))

                setFilterData({
                  fieldName: matchedField.qName,
                  items: items,
                  app: app
                })
                setError(null)
              } else {
                setError('Nenhum dado encontrado para o filtro')
              }
              setLoading(false)
            })
          } else {
            //console.log(`⚠️ DEBUG_SIMPLE_FILTER_NO_FIELD`)
            setError('Nenhum campo encontrado no app')
            setLoading(false)
          }
        })

      } catch (error) {
        console.error('❌ Erro ao carregar filtro:', error)
        
        setError(error.message)
        setLoading(false)
      }
    }

    loadFilterData()
  }, [filterObj.objectId, filterObj.appId, filterObj.qlikAppId, empresaId])

  const handleItemClick = async (item) => {
    if (!filterData?.app || !filterData?.fieldName) return

    try {
      const field = await filterData.app.field(filterData.fieldName)
      if (item.selected) {
        await field.clear()
      } else {
        await field.selectValues([{qText: item.value}], true, false)
      }

      // Atualizar Context
      const currentSelections = getFieldSelections(filterData.fieldName)
      const newSelections = new Set(currentSelections)
      
      if (item.selected) {
        newSelections.delete(item.value)
      } else {
        newSelections.add(item.value)
      }
      
      updateFieldSelection(filterData.fieldName, newSelections, filterObj.objectId)

    } catch (error) {
      console.error('Erro ao aplicar seleção:', error)
    }
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" p={2}>
        <CircularProgress size={24} />
        <Typography variant="body2" sx={{ ml: 1 }}>
          Carregando filtro...
        </Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 1 }}>
        <Typography variant="body2">
          <strong>Erro:</strong> {error}
        </Typography>
        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
          App ID: {filterObj.appId} | Object ID: {filterObj.objectId}
        </Typography>
      </Alert>
    )
  }

  if (!filterData) {
    return (
      <Box textAlign="center" p={2}>
        <Typography variant="body2" color="text.secondary">
          Filtro não disponível
        </Typography>
      </Box>
    )
  }

  const filteredItems = filterData.items.filter(item =>
    item.value.toLowerCase().includes(searchText.toLowerCase())
  )

  return (
    <Box>
      <TextField
        fullWidth
        size="small"
        placeholder="Buscar..."
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon fontSize="small" />
            </InputAdornment>
          )
        }}
        sx={{ mb: 2 }}
      />
      
      <Box sx={{ maxHeight: '300px', overflowY: 'auto' }}>
        {filteredItems.length === 0 ? (
          <Typography variant="body2" color="text.secondary" textAlign="center" p={2}>
            {searchText ? 'Nenhum item encontrado' : 'Nenhum item disponível'}
          </Typography>
        ) : (
          filteredItems.map((item, index) => (
            <Chip
              key={index}
              label={item.value}
              onClick={() => handleItemClick(item)}
              color={item.selected ? 'primary' : 'default'}
              variant={item.selected ? 'filled' : 'outlined'}
              sx={{ 
                m: 0.5,
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: item.selected ? 'primary.dark' : 'action.hover'
                }
              }}
            />
          ))
        )}
      </Box>
    </Box>
  )
}

const GlobalFilterSidebar = ({ open, onClose }) => {
  const { selections, clearAllSelections, clearFieldSelections } = useQlikSelection()
  const { empresaSelecionada, empresaAtual } = useEmpresa()
  const [filterObjects, setFilterObjects] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Buscar objetos de filtro da empresa selecionada
  useEffect(() => {
    const loadFilterObjects = async () => {

      // ✅ CORRIGIDO: Usar empresaAtual.id ou fallback para empresaSelecionada
      const empresaId = empresaAtual?.id || empresaSelecionada
      
      if (!empresaId) {
        //console.log(`⚠️ DEBUG_GLOBAL_FILTER_NO_EMPRESA: Nenhuma empresa selecionada`)
        setFilterObjects([])
        return
      }

      setLoading(true)
      setError(null)

      try {

        // ✅ CORRIGIDO: Usar empresaService em vez de fetch direto
        const result = await empresaService.obterObjetosEmpresa(empresaId, { tipo: 'filter' })
        const objects = result.data || []

        setFilterObjects(objects)

      } catch (error) {
        console.error('❌ Erro ao carregar objetos de filtro:', error)
        setError(`Erro ao carregar filtros: ${error.message}`)
      } finally {
        setLoading(false)
      }
    }

    if (open) {
      loadFilterObjects()
    }
  }, [open, empresaSelecionada, empresaAtual?.id])

  // Calcular estatísticas das seleções
  const selectionStats = {
    totalFields: selections.size,
    totalSelections: Array.from(selections.values()).reduce((acc, fieldSelections) => acc + fieldSelections.size, 0)
  }

  const handleClearAll = () => {
    clearAllSelections('global-sidebar')
  }

  const handleClearField = (fieldName) => {
    clearFieldSelections(fieldName, 'global-sidebar')
  }

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: 450,
          backgroundColor: '#f8f9fa'
        }
      }}
    >
      <Box sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FilterIcon color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Filtros Globais
            </Typography>
            {selectionStats.totalSelections > 0 && (
              <Badge badgeContent={selectionStats.totalSelections} color="primary" />
            )}
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Empresa Atual */}
        <Paper sx={{ p: 2, mb: 2, backgroundColor: 'white' }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Empresa Atual
          </Typography>
          <Typography variant="body1" fontWeight="medium">
            {empresaAtual?.nome || 'Nenhuma empresa selecionada'}
          </Typography>
        </Paper>

        {/* Estatísticas */}
        {selectionStats.totalSelections > 0 && (
          <Paper sx={{ p: 2, mb: 2, backgroundColor: 'white' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Seleções Ativas
              </Typography>
              <Button
                size="small"
                startIcon={<ClearIcon />}
                onClick={handleClearAll}
                color="error"
                variant="outlined"
              >
                Limpar Tudo
              </Button>
            </Box>
            <Typography variant="body2">
              {selectionStats.totalFields} campo(s) • {selectionStats.totalSelections} seleção(ões)
            </Typography>
          </Paper>
        )}

        <Divider sx={{ mb: 2 }} />

        {/* Lista de Filtros Renderizados */}
        <Box sx={{ flex: 1, overflowY: 'auto' }}>
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {!loading && !error && filterObjects.length === 0 && (
            <Paper sx={{ p: 3, textAlign: 'center', backgroundColor: 'white' }}>
              <FilterIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Nenhum filtro configurado
              </Typography>
              <Typography variant="caption" color="text.disabled">
                Configure filtros na tela de Configurações
              </Typography>
            </Paper>
          )}

          {!loading && !error && filterObjects.length > 0 && (
            <List sx={{ p: 0 }}>
              {filterObjects.map((filterObj, index) => (
                <Accordion key={filterObj.id || index} defaultExpanded sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%', mr: 1 }}>
                      <Typography variant="subtitle2" fontWeight="medium">
                        {filterObj.nome || `Filtro ${index + 1}`}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ID: {filterObj.objectId}
                      </Typography>
                    </Box>
                  </AccordionSummary>
                  <AccordionDetails sx={{ p: 1 }}>
                    <Box sx={{ 
                      minHeight: '200px',
                      maxHeight: '400px',
                      overflow: 'hidden'
                    }}>
                      <SimpleFilter filterObj={filterObj} empresaId={empresaAtual?.id || empresaSelecionada} />
                    </Box>
                  </AccordionDetails>
                </Accordion>
              ))}
            </List>
          )}
        </Box>

        {/* Footer com Resumo das Seleções Ativas */}
        {selections.size > 0 && (
          <>
            <Divider sx={{ my: 2 }} />
            <Paper sx={{ p: 2, backgroundColor: 'white', maxHeight: '200px', overflowY: 'auto' }}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Resumo das Seleções
              </Typography>
              <List dense sx={{ p: 0 }}>
                {Array.from(selections.entries()).map(([fieldName, fieldSelections]) => (
                  <ListItem key={fieldName} sx={{ px: 0, py: 0.5 }}>
                    <ListItemText
                      primary={`${fieldName}: ${fieldSelections.size} seleção(ões)`}
                      secondary={Array.from(fieldSelections).slice(0, 3).join(', ') + 
                        (fieldSelections.size > 3 ? ` +${fieldSelections.size - 3} mais...` : '')}
                      primaryTypographyProps={{ variant: 'body2', fontWeight: 'medium' }}
                      secondaryTypographyProps={{ variant: 'caption' }}
                    />
                    <IconButton
                      size="small"
                      onClick={() => handleClearField(fieldName)}
                      color="error"
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </ListItem>
                ))}
              </List>
            </Paper>
          </>
        )}
      </Box>
    </Drawer>
  )
}

export default GlobalFilterSidebar 