import React, { useEffect, useState, useRef } from 'react'
import { Box, Paper, Typography, CircularProgress, Alert, IconButton, Tooltip } from '@mui/material'
import { Edit as EditIcon, Delete as DeleteIcon, Refresh as RefreshIcon } from '@mui/icons-material'
import qlikService from '@services/qlik'
import CustomKPI from '../CustomKPI/CustomKPI'
import CustomChart from '../CustomChart/CustomChart'
import CustomMultiKPI from '../CustomMultiKPI/CustomMultiKPI'
import CustomTable from '../CustomTable/CustomTable'
import CustomFilter from '../CustomFilter/CustomFilter'
import { useQlikSelection } from '@contexts/QlikSelectionContext'
import { useTheme } from '@mui/material/styles'
import { useEmpresa } from '@context/EmpresaContext'

const QlikObject = ({ objeto, objectId, appId, config, onEdit, onDelete, onError }) => {
  // ✅ NOVO: Suporte para props individuais ou objeto completo
  let objetoFinal
  
  if (objeto) {
    // Formato antigo: objeto completo passado como prop
    objetoFinal = objeto
  } else if (objectId && appId && config) {
    // Formato novo: props individuais (usado pelo GridLayout)
    objetoFinal = {
      id: objectId,
      appId: appId,
      config: config,
      tipo: config.tipo,
      nome: config.nome
    }
  } else {
    // Nenhum formato válido fornecido
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height="100%"
        minHeight="150px"
      >
        <Typography variant="body2" color="textSecondary">
          Configuração inválida do objeto
        </Typography>
      </Box>
    )
  }

  // ✅ VERIFICAÇÃO MÍNIMA: Apenas verificar se objetoFinal existe
  if (!objetoFinal) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height="100%"
        minHeight="150px"
      >
        <Typography variant="body2" color="textSecondary">
          Objeto não encontrado
        </Typography>
      </Box>
    )
  }

  // ✅ NOVO: Não renderizar objetos do tipo 'filter' nas páginas
  if (objetoFinal.tipo === 'filter') {
    return null
  }

  const theme = useTheme()
  const { empresaSelecionada } = useEmpresa()
  const { registerObjectUpdate, selectionVersion } = useQlikSelection()

  const [objectData, setObjectData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const containerRef = useRef(null)

  // Detectar se está em modo de desenvolvimento
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Dados simulados para desenvolvimento
  const getSimulatedData = () => {
    const tipo = objetoFinal.config?.tipo || 'kpi'
    
    if (tipo === 'kpi') {
      return {
        tipo: 'kpi',
        titulo: `KPI Simulado - ${objetoFinal.id || 'Exemplo'}`,
        valor: Math.floor(Math.random() * 100000) + 1000,
        valorFormatado: new Intl.NumberFormat('pt-BR', { 
          style: 'currency', 
          currency: 'BRL' 
        }).format(Math.floor(Math.random() * 100000) + 1000),
        medida: 'Vendas Total'
      }
    }
    
    if (tipo === 'multiKpi') {
      // Usar configurações do objeto se disponíveis
      const multiKpiConfig = objetoFinal.config?.multiKpiConfig || {}
      const kpiConfig = objetoFinal.config?.kpiConfig || {}
      
      // Quantidade de KPIs baseada na configuração do banco de dados
      const quantidadeKPIs = multiKpiConfig.itemsPerRow || 3
      
      // Cores e configurações baseadas no config
      const corPadrao = multiKpiConfig.cor || kpiConfig.cor || '#1976d2'
      const corFontePadrao = multiKpiConfig.corFonte || kpiConfig.corFonte || '#ffffff'
      
      // Títulos possíveis para os KPIs
      const titulosPossiveis = [
        'Com Venda', 'Inadimplente', 'No Jurídico', 'Ativo', 'Suspenso', 
        'Bloqueado', 'Pendente', 'Aprovado', 'Cancelado', 'Em Análise',
        'Processando', 'Finalizado', 'Em Andamento', 'Aguardando'
      ]
      
      // Ícones possíveis para os KPIs  
      const iconesPossiveis = [
        'people', 'person', 'assignment', 'business', 'money', 'analytics',
        'timeline', 'assessment', 'star', 'cart', 'atm', 'bank'
      ]
      
      // Gerar KPIs dinamicamente baseado na quantidade configurada
      const kpis = []
      for (let i = 0; i < quantidadeKPIs; i++) {
        kpis.push({
          titulo: titulosPossiveis[i] || `KPI ${i + 1}`,
          valor: Math.floor(Math.random() * 5000) + 500,
          cor: corPadrao,
          corFonte: corFontePadrao,
          icone: iconesPossiveis[i % iconesPossiveis.length],
          formato: 'numero',
          casasDecimais: 0,
          showIcon: multiKpiConfig.showIcon !== false,
          gradiente: kpiConfig.gradiente || false
        })
      }
      
      return {
        tipo: 'multiKpi',
        titulo: `MultiKPI - ${objetoFinal.id || 'Exemplo'}`,
        kpis: kpis
      }
    }

    // ✅ NOVO: Dados simulados para tabela
    if (tipo === 'table') {
      //console.log(`📋 Gerando dados simulados para tabela: ${objetoFinal.id}`)
      
      // Colunas da tabela
      const columns = [
        { id: 'id', label: 'ID', type: 'number', width: 80 },
        { id: 'nome', label: 'Nome', type: 'text', minWidth: 150 },
        { id: 'categoria', label: 'Categoria', type: 'text', minWidth: 120 },
        { id: 'valor', label: 'Valor', type: 'currency', align: 'right', minWidth: 100 },
        { id: 'percentual', label: 'Percentual', type: 'percentage', align: 'right', minWidth: 100 },
        { id: 'status', label: 'Status', type: 'status', minWidth: 120 },
        { id: 'responsavel', label: 'Responsável', type: 'avatar', minWidth: 180 },
        { id: 'tendencia', label: 'Tendência', type: 'trend', align: 'center', minWidth: 100 },
        { id: 'data', label: 'Data', type: 'date', minWidth: 100 }
      ]

      // Dados de exemplo
      const nomes = ['João Silva', 'Maria Santos', 'Pedro Oliveira', 'Ana Costa', 'Carlos Souza', 'Lucia Ferreira', 'Ricardo Lima', 'Fernanda Alves']
      const categorias = ['Vendas', 'Marketing', 'Suporte', 'Financeiro', 'Operações', 'RH']
      const statusOptions = ['active', 'pending', 'approved', 'processing', 'inactive', 'success', 'warning']
      
      const rows = []
      for (let i = 0; i < 25; i++) {
        const isPositiveTrend = Math.random() > 0.4
        rows.push({
          id: i + 1,
          nome: nomes[Math.floor(Math.random() * nomes.length)],
          categoria: categorias[Math.floor(Math.random() * categorias.length)],
          valor: Math.floor(Math.random() * 50000) + 5000,
          percentual: (Math.random() * 100).toFixed(1),
          status: statusOptions[Math.floor(Math.random() * statusOptions.length)],
          responsavel: nomes[Math.floor(Math.random() * nomes.length)],
          tendencia: isPositiveTrend ? `+${(Math.random() * 20 + 5).toFixed(1)}` : `-${(Math.random() * 15 + 2).toFixed(1)}`,
          data: new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString()
        })
      }

      return {
        tipo: 'table',
        titulo: `Tabela Simulada - ${objetoFinal.id || 'Dados'}`,
        columns: columns,
        rows: rows
      }
    }
    
    // ✅ NOVO: Dados simulados para filtro
    if (tipo === 'filter') {
      //console.log(`🔍 Gerando dados simulados para filtro: ${objetoFinal.id}`)
      
      const sampleItems = [
        'São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Brasília',
        'Fortaleza', 'Curitiba', 'Recife', 'Porto Alegre', 'Manaus',
        'Belém', 'Goiânia', 'Guarulhos', 'Campinas', 'São Luís',
        'Nova Iguaçu', 'Duque de Caxias', 'Teresina', 'Natal', 'Campo Grande'
      ]

      return {
        tipo: 'filter',
        titulo: `Filtro Simulado - ${objetoFinal.id || 'Cidades'}`,
        items: sampleItems.map((item, index) => ({
          id: index,
          value: item,
          selected: Math.random() > 0.8, // 20% chance de estar selecionado
          excluded: Math.random() > 0.95, // 5% chance de estar excluído
          possible: true
        })),
        selectedItems: [],
        searchText: ''
      }
    }
    
    // ✅ NOVO: Geração inteligente de dados para gráficos baseada no tipo configurado
    // ✅ CORREÇÃO TEMPORÁRIA: Se tipo principal for mixed mas chartConfig.tipoGrafico for diferente, usar o tipo principal
    let chartType = objetoFinal.config?.chartConfig?.tipoGrafico || objetoFinal.config?.tipo || tipo
    
    // ✅ MIGRAÇÃO AUTOMÁTICA: Corrigir inconsistência entre tipo principal e chartConfig.tipoGrafico
    if (objetoFinal.config?.tipo && ['bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed', 'gauge'].includes(objetoFinal.config.tipo)) {
      if (objetoFinal.config.chartConfig?.tipoGrafico && objetoFinal.config.chartConfig.tipoGrafico !== objetoFinal.config.tipo) {
        //console.log(`🔄 CORREÇÃO_AUTOMÁTICA: tipo="${objetoFinal.config.tipo}" != chartConfig.tipoGrafico="${objetoFinal.config.chartConfig.tipoGrafico}" → usando tipo principal`)
        chartType = objetoFinal.config.tipo // Usar o tipo principal como prioridade
      }
    }
    
    //console.log(`🎲 Gerando dados simulados para gráfico tipo: ${chartType}`)
    
    const categorias = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun']
    let series = []
    
    // ✅ NOVO: Gerar séries baseadas no tipo do gráfico
    if (chartType === 'mixed' || chartType === 'barLine' || chartType === 'lineBar') {
      // ✅ APENAS PARA MIXED: gerar 3 séries (2 barras + 1 linha)
      series = [
        {
          name: 'Vendas (Barras)',
          data: categorias.map(() => Math.floor(Math.random() * 50) + 10)
        },
        {
          name: 'Meta (Linha)',
          data: categorias.map(() => Math.floor(Math.random() * 40) + 20)
        },
        {
          name: 'Tendência (Linha)',
          data: categorias.map(() => Math.floor(Math.random() * 35) + 15)
        }
      ]
    } else if (chartType === 'kpi' || chartType === 'multiKpi') {
      // Para KPIs, já tratado acima
      return getSimulatedData()
    } else {
      // ✅ PARA OUTROS TIPOS: gerar apenas 1 série (como era antes)
      series = [
        {
          name: chartType === 'pie' || chartType === 'donut' ? 'Distribuição' : 'Vendas',
          data: categorias.map(() => Math.floor(Math.random() * 50) + 10)
        }
      ]
      
      // ✅ REMOVIDO: Não adicionar segunda série automaticamente
      // A segunda série só deve existir se vier do Qlik ou for configurada manualmente
    }
    
    return {
      tipo: 'chart',
      titulo: `${chartType === 'mixed' ? 'Gráfico Misto' : 'Gráfico'} Simulado - ${objetoFinal.id || 'Exemplo'}`,
      dados: {
        categories: categorias,
        series: series,
        dimensoes: ['Período'],
        medidas: series.map(s => s.name)
      }
    }
  }

  useEffect(() => {
    // ✅ OTIMIZADO: useEffect principal simplificado
    const shouldUseRealData = objetoFinal.config?.tipo === 'filter' || !isDevelopment
    
    if (!shouldUseRealData && isDevelopment) {
      // Em desenvolvimento, simular dados (exceto para filtros)
      //console.log(`🔧 Modo DEV: Simulando dados para objeto ${objetoFinal.id}`)
      //console.log(`📋 Configuração recebida:`, objetoFinal.config)
      
      setTimeout(() => {
        const simulatedData = getSimulatedData()
        //console.log(`🎭 Dados simulados gerados:`, simulatedData)
        setObjectData(simulatedData)
        setLoading(false)
      }, 100) // ✅ OTIMIZADO: Reduzido de 500ms para 100ms
      return
    }

    // ✅ NOVO: Log para filtros que vão usar dados reais
    if (objetoFinal.config?.tipo === 'filter') {
    }

    // Carregar dados reais do Qlik (produção ou filtros em desenvolvimento)
    if (objetoFinal.id && objetoFinal.appId) {
      // ✅ DEBUG: Log específico para filtros em produção
      if (objetoFinal.config?.tipo === 'filter') {
      }
      
      loadQlikData()
    } else {
      // Se não tem objectId ou appId, simular dados básicos
      //console.log(`⚠️ Sem objectId ou appId, simulando dados`)
      
      // ✅ DEBUG: Log específico para filtros sem IDs
      if (objetoFinal.config?.tipo === 'filter') {
      }
      
      setTimeout(() => {
        const simulatedData = getSimulatedData()
        //console.log(`🎭 Dados simulados para produção:`, simulatedData)
        setObjectData(simulatedData)
        setLoading(false)
      }, 50) // ✅ OTIMIZADO: Reduzido de 300ms para 50ms
    }
  }, [objetoFinal.id, objetoFinal.appId, isDevelopment, objetoFinal.config?.tipo, selectionVersion]) // ✅ NOVO: Adicionar selectionVersion como dependência

  // ✅ NOVO: Registrar no Context para atualizações instantâneas
  useEffect(() => {
    if (objetoFinal.config?.tipo !== 'filter' && objetoFinal.id && objetoFinal.appId) {
      
      const unregister = registerObjectUpdate(objetoFinal.id, (fieldName, selectedValues) => {
        
        // Re-carregar dados imediatamente 
        loadQlikData()
      })
      
      // Cleanup ao desmontar
      return unregister
    }
  }, [objetoFinal.id, objetoFinal.appId, objetoFinal.config?.tipo, registerObjectUpdate])

  // ✅ OTIMIZADO: useEffect principal simplificado
  useEffect(() => {
    // ✅ NOVO: Para filtros, sempre usar dados reais (mesmo em desenvolvimento)
    const shouldUseRealData = objetoFinal.config?.tipo === 'filter' || !isDevelopment
    
    const handleSelectionChange = (event) => {
      
      // Re-carregar dados apenas se não for o próprio filtro que disparou o evento
      if (objetoFinal.config?.tipo !== 'filter' && objetoFinal.id && objetoFinal.appId) {
        loadQlikData()
      }
    }
    
    // Adicionar listener para mudanças de seleção
    window.addEventListener('qlik-selection-changed', handleSelectionChange)
    
    // Cleanup
    return () => {
      window.removeEventListener('qlik-selection-changed', handleSelectionChange)
    }
  }, [objetoFinal.id, objetoFinal.appId, objetoFinal.config?.tipo])

  const loadQlikData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      //console.log(`🔍 Carregando dados do objeto: ${objetoFinal.id}`)
      
      // ✅ DEBUG: Log específico para filtros
      if (objetoFinal.config?.tipo === 'filter') {
      }
      
      const app = await qlikService.openApp(objetoFinal.appId)
      if (!app) {
        // ✅ DEBUG: Log específico para filtros quando app não encontrado
        if (objetoFinal.config?.tipo === 'filter') {
        }
        throw new Error('App Qlik não encontrado')
      }

      //console.log(`📱 App carregado: ${objetoFinal.appId}`)
      
      // ✅ DEBUG: Log específico para filtros quando app carregado
      if (objetoFinal.config?.tipo === 'filter') {
      }
      
      // Usar a abordagem correta para extrair dados do hypercube
      const objectData = await extractQlikData(app, objetoFinal.id, objetoFinal.config)
      
      //console.log(`📊 Dados extraídos:`, objectData)
      
      // ✅ DEBUG: Log específico para filtros após extração
      if (objetoFinal.config?.tipo === 'filter') {
      }
      
      setObjectData(objectData)
      
    } catch (error) {
      console.error(`❌ Erro ao carregar objeto Qlik:`, error)
      
      // ✅ DEBUG: Log específico para filtros quando há erro
      if (objetoFinal.config?.tipo === 'filter') {
      }
      
      setError(error.message)
      onError && onError(error)
    } finally {
      setLoading(false)
    }
  }

  const extractQlikData = async (app, objectId, config) => {
    //console.log(`🔍 DEBUG_CONFIG_NOME:`, config)
    
    return new Promise((resolve, reject) => {
      // Usar a mesma abordagem do código fornecido
      app.visualization.get(objectId).then(visualizationModel => {
        
        visualizationModel.model.getProperties().then(properties => {
          //console.log(`📊 Propriedades do objeto:`, properties)
          
          // Tentar extrair o título de múltiplas fontes possíveis
          let title = null
          
          // 1. Verificar title com expressão
          if (properties.title?.qStringExpression?.qExpr) {
            title = properties.title.qStringExpression.qExpr
          }
          // 2. Verificar title simples
          else if (properties.title) {
            title = properties.title
          }
          // 3. Verificar qMeasures[0].qDef.qLabel (comum em KPIs) - PROTEGER CONTRA UNDEFINED
          else if (properties.qHyperCubeDef?.qMeasures && properties.qHyperCubeDef.qMeasures.length > 0 && properties.qHyperCubeDef.qMeasures[0].qDef?.qLabel) {
            title = properties.qHyperCubeDef.qMeasures[0].qDef.qLabel
            //console.log(`📊 Título extraído de qMeasures[0].qDef.qLabel: "${title}"`)
          }
          // 4. Verificar qDef.title se existir
          else if (properties.qDef?.title) {
            title = properties.qDef.title
          }
          // 5. Verificar metadata.description
          else if (properties.metadata?.description) {
            title = properties.metadata.description
          }
          // 6. Verificar nome cadastrado no banco de dados (via config)          
          else if (config?.nome) {
            title = config.nome
            //console.log(`📊 Título extraído da configuração: "${title}"`)
          }
          // 7. Fallback para o nome genérico
          else {
            title = 'Objeto Qlik'
            //console.log(`⚠️ Usando fallback para título: "${title}"`)
          }
          
          //console.log(`📋 Título final extraído: "${title}"`)

          
          // ✅ NOVO: Para objetos de filtro, verificar se é filterpane com childRefId
          if (config?.tipo === 'filter') {

            
            // ✅ NOVO: Se é filterpane com childRefId, tentar acessar o filho
            if (properties?.qInfo?.qType === 'filterpane' && properties?.childRefId) {
 
              // ✅ ABORDAGEM MELHORADA: Usar as dimensões definidas no próprio objeto
              let fieldName = null
              
              // 1. Tentar extrair campo das dimensões do hypercube se existir
              if (properties.qHyperCubeDef?.qDimensions && properties.qHyperCubeDef.qDimensions.length > 0) {
                const firstDimension = properties.qHyperCubeDef.qDimensions[0]
                if (firstDimension.qDef?.qFieldDefs && firstDimension.qDef.qFieldDefs.length > 0) {
                  fieldName = firstDimension.qDef.qFieldDefs[0]
                }
              }
              
              // 2. Se não encontrou nas dimensões, tentar no título/label
              if (!fieldName && title && title !== 'Objeto Qlik') {
                fieldName = title
              }
              
              // 3. Se ainda não encontrou, buscar no childRefId se possível
              if (!fieldName && properties.childRefId) {
                // Como fallback, usar um campo genérico ou buscar dinamicamente
                fieldName = 'Campo_Dinamico'
              }
              
              if (fieldName) {
                
                // Verificar se o campo existe na lista de campos
                app.getList('FieldList', (fieldListReply) => {
                  
                  // Buscar correspondência exata primeiro
                  let matchedField = fieldListReply?.qFieldList?.qItems?.find(field => 
                    field.qName.toLowerCase() === fieldName.toLowerCase()
                  )
                  
                  // Se não encontrou correspondência exata, buscar similaridade
                  if (!matchedField && fieldName.length > 3) {
                    matchedField = fieldListReply?.qFieldList?.qItems?.find(field => 
                      field.qName.toLowerCase().includes(fieldName.toLowerCase()) ||
                      fieldName.toLowerCase().includes(field.qName.toLowerCase())
                    )
                  }
                  
                  // Se ainda não encontrou, pegar o primeiro campo disponível como fallback
                  if (!matchedField && fieldListReply?.qFieldList?.qItems?.length > 0) {
                    // Tentar encontrar um campo que pareça ser de filtro (não técnico)
                    matchedField = fieldListReply.qFieldList.qItems.find(field => 
                      !field.qName.startsWith('$') && 
                      !field.qName.startsWith('_') && 
                      field.qName.length > 2 &&
                      field.qCardinal > 1 && field.qCardinal < 1000 // Campo com cardinalidade razoável para filtro
                    ) || fieldListReply.qFieldList.qItems[0] // Fallback para o primeiro campo
       
                  }
                  
                  if (matchedField) {
                    
                    // Criar listbox para o campo encontrado
                    const listObjectDef = {
                      qDef: {
                        qFieldDefs: [matchedField.qName]
                      },
                      qInitialDataFetch: [{
                        qTop: 0,
                        qLeft: 0,
                        qHeight: Math.min(matchedField.qCardinal, 1000), // Limitar a 1000 itens
                        qWidth: 1
                      }]
                    }
                    
                    function processFieldFilter(filterData) {
                      
                      if (title.indexOf('=') === 0) {
                        app.model.evaluateExpression(title).then(result => {
                          resolve(processQlikData(filterData, result))
                        }).catch(() => {
                          resolve(processQlikData(filterData, title))
                        })
                      } else {
                        resolve(processQlikData(filterData, title))
                      }
                    }
                    
                    app.createList(listObjectDef, processFieldFilter)
                    
                  } else {
                    
                    // Se não encontrou nenhum campo, retornar filtro vazio
                    resolve(processQlikData({
                      qListObject: {
                        qDataPages: [],
                        qSize: { qcy: 0, qcx: 1 }
                      }
                    }, title))
                  }
                  
                }).catch(fieldListError => {
                  console.error(`❌ Erro ao obter lista de campos:`, fieldListError)
                  
                  resolve(processQlikData({
                    qListObject: {
                      qDataPages: [],
                      qSize: { qcy: 0, qcx: 1 }
                    }
                  }, title))
                })
              } else {
                
                // Fallback final: filtro vazio
                resolve(processQlikData({
                  qListObject: {
                    qDataPages: [],
                    qSize: { qcy: 0, qcx: 1 }
                  }
                }, title))
              }
              
              return // ✅ IMPORTANTE: Retornar aqui para não executar código adicional
            }
            
            // Para filtros diretos, não configurar hypercube, usar processamento direto
            function processFilterObject(filterData) {
              
              if (title.indexOf('=') === 0) {
                app.model.evaluateExpression(title).then(result => {
                  resolve(processQlikData(filterData, result))
                }).catch(() => {
                  resolve(processQlikData(filterData, title))
                })
              } else {
                resolve(processQlikData(filterData, title))
              }
            }
            
            // ✅ NOVO: Tentar diferentes abordagens para obter dados do filtro
            if (properties.qListObjectDef) {
              
              // Criar objeto de lista
              app.createList(properties.qListObjectDef, processFilterObject)
            } else if (properties.qHyperCubeDef) {
              // Usar hypercube se disponível (alguns filtros podem ter)
              app.createCube(properties.qHyperCubeDef, processFilterObject)
            } else {
              
              // Fallback: processar com dados vazios
              processFilterObject({
                qListObject: {
                  qDataPages: [],
                  qSize: { qcy: 0, qcx: 1 }
                }
              })
            }
            
            return // ✅ IMPORTANTE: Retornar aqui para não executar o código de hypercube padrão
          }
          
          // ✅ CODIGO ORIGINAL: Para objetos não-filtro, continuar com hypercube
          // Configurar o hypercube para buscar mais dados
          if (properties.qHyperCubeDef?.qInitialDataFetch && properties.qHyperCubeDef.qInitialDataFetch[0]) {
            const columnsNum = properties.qHyperCubeDef.qInterColumnSortOrder?.length || 1
            properties.qHyperCubeDef.qInitialDataFetch[0].qWidth = columnsNum
            properties.qHyperCubeDef.qInitialDataFetch[0].qHeight = Math.floor(10000 / columnsNum)
          }
          
          // ✅ NOVO: Configuração especial para Pivot Tables
          if (properties.qHyperCubeDef?.qMode === 'P') {
            //console.log(`🔄 TABLE_DEBUG_PIVOT_CONFIGURATION: Configurando pivot table`)
            
            // Para pivot tables, ajustar a área de dados solicitada
            const qSize = properties.qHyperCubeDef.qSize || { qcx: 12, qcy: 1 }
            const totalColumns = qSize.qcx || 12
            const totalRows = qSize.qcy || 1
            
            // Configurar o fetch inicial para pegar todos os dados
            if (!properties.qHyperCubeDef.qInitialDataFetch) {
              properties.qHyperCubeDef.qInitialDataFetch = []
            }
            
            // Garantir que temos uma configuração de fetch
            if (properties.qHyperCubeDef.qInitialDataFetch.length === 0) {
              properties.qHyperCubeDef.qInitialDataFetch.push({
                qLeft: 0,
                qTop: 0,
                qWidth: totalColumns,
                qHeight: totalRows
              })
            } else {
              // Ajustar a configuração existente para pegar todos os dados
              properties.qHyperCubeDef.qInitialDataFetch[0].qWidth = totalColumns
              properties.qHyperCubeDef.qInitialDataFetch[0].qHeight = totalRows
            }
          
          }
          
          properties.qHyperCubeDef.qShowTotalsAbove = true
          
          function processHyperCube(hcData) {
            //console.log(`📈 Layout do objeto:`, hcData)
            
            if (title.indexOf('=') === 0) {
              app.model.evaluateExpression(title).then(result => {
                resolve(processQlikData(hcData, result))
              }).catch(() => {
                resolve(processQlikData(hcData, title))
              })
            } else {
              resolve(processQlikData(hcData, title))
            }
          }
          
          // Criar o cubo
          app.createCube(properties.qHyperCubeDef, processHyperCube)
        }).catch(reject)
      }).catch(reject)
    })
  }

  const processQlikData = (hcData, title) => {
    try {
      //console.log(`🔍 DEBUG_HYPERCUBE_RAW:`, JSON.stringify(hcData, null, 2))
      
      // ✅ DEBUG: Log específico para filtros
      if (config?.tipo === 'filter') {
        
        // ✅ NOVO: Para filtros, verificar se temos qListObject em vez de qHyperCube
        if (hcData.qListObject) {
          
          // Processar dados de lista (filtro)
          const listObject = hcData.qListObject
          const dataPages = listObject.qDataPages || []
          
          if (dataPages.length > 0 && dataPages[0].qMatrix) {
            const matrix = dataPages[0].qMatrix
            
            // Processar itens do filtro
            const filterItems = matrix.map((row, index) => {
              const cell = row[0] // Para filtros, geralmente só há uma coluna
              const item = {
                id: index,
                value: cell?.qText || cell?.qNum?.toString() || `Item ${index + 1}`,
                text: cell?.qText || cell?.qNum?.toString() || '',
                selected: cell?.qState === 'S',
                excluded: cell?.qState === 'X',
                possible: cell?.qState === 'O' || cell?.qState === undefined,
                qState: cell?.qState,
                qNum: cell?.qNum,
                qText: cell?.qText
              }
              
              return item
            })
            
            const selectedItems = filterItems.filter(item => item.selected)
            
            const filterResult = {
              tipo: 'filter',
              titulo: title || 'Filtro',
              items: filterItems,
              selectedItems: selectedItems,
              searchText: '',
              debug: {
                source: 'qListObject',
                originalMatrix: matrix.slice(0, 2),
                processedItemsCount: filterItems.length,
                selectedCount: selectedItems.length
              }
            }
            
            return filterResult
          } else {
            
            return {
              tipo: 'filter',
              titulo: title || 'Filtro',
              items: [],
              selectedItems: [],
              searchText: '',
              debug: 'Lista vazia ou sem dados'
            }
          }
        }
      }
      
      
      const hypercube = hcData.qHyperCube
      
      if (!hypercube) {
        //console.log(`⚠️ HyperCube não encontrado`)
        return { 
          tipo: 'error', 
          titulo: title,
          erro: 'HyperCube não encontrado' 
        }
      }

      // Determinar onde estão os dados
      let matrix = null
      
      if (hypercube.qDataPages?.length > 0 && hypercube.qDataPages[0].qMatrix?.length > 0) {
        matrix = hypercube.qDataPages[0].qMatrix
        
      } else if (hypercube.qStackedDataPages?.length > 0 && hypercube.qStackedDataPages[0].qData?.length > 0) {
        // Para dados empilhados, precisamos processar de forma diferente
        const stackedData = hypercube.qStackedDataPages[0].qData[0]
        //console.log(`📚 DEBUG_STACKED_RAW:`, JSON.stringify(stackedData, null, 2))
   
        if (stackedData && stackedData.qSubNodes) {
          matrix = extractFromStackedData(stackedData.qSubNodes)
        }
      } else if (hypercube.qPivotDataPages?.length > 0) {
        // ✅ NOVO: Para dados pivot, processar de forma específica
        const pivotPage = hypercube.qPivotDataPages[0]
        
        matrix = extractFromPivotData(pivotPage, hypercube)
      }

      if (!matrix || matrix.length === 0) {
        //console.log(`⚠️ DEBUG_NO_MATRIX: Sem dados disponíveis`)
        
        // ✅ DEBUG: Log específico para filtros sem matrix
        if (config?.tipo === 'filter') {
          
          return {
            tipo: 'filter',
            titulo: title,
            items: [],
            selectedItems: [],
            searchText: '',
            debug: 'Matrix vazia ou não encontrada'
          }
        }
        
        if (config?.tipo === 'table') {
          //console.log(`❌ DEBUG_TABLE_EMPTY_MATRIX: Retornando dados vazios para tabela`)
          return {
            tipo: 'table',
            titulo: title,
            columns: [],
            rows: [],
            debug: 'Matriz vazia ou não encontrada'
          }
        }
        
        return { 
          tipo: 'empty', 
          titulo: title,
          dados: { dimensoes: [], series: [] }
        }
      }

      // Processar os dados baseado no tipo de objeto
      const processedData = processMatrixData(matrix, hypercube, title)
      //console.log(`✅ DEBUG_PROCESSED_DATA:`, JSON.stringify(processedData, null, 2))
 
      return processedData

    } catch (error) {
      console.error(`❌ Erro ao processar dados:`, error)
      
      // ✅ DEBUG: Log específico para filtros com erro
      if (config?.tipo === 'filter') {
        
        return {
          tipo: 'filter',
          titulo: title,
          items: [],
          selectedItems: [],
          searchText: '',
          debug: `Erro: ${error.message}`
        }
      }
      
      if (config?.tipo === 'table') {
        //console.log(`❌ DEBUG_TABLE_ERROR: Erro durante processamento da tabela`)
        return {
          tipo: 'table',
          titulo: title,
          columns: [],
          rows: [],
          debug: `Erro: ${error.message}`
        }
      }
      
      return { 
        tipo: 'error', 
        titulo: title,
        erro: error.message 
      }
    }
  }

  const extractFromStackedData = (subNodes) => {
    const matrix = []
    
    const processNode = (node, parentDimensions = []) => {
      if (node.qSubNodes && node.qSubNodes.length > 0) {
        // Tem subnós - adicionar esta dimensão e continuar
        const currentDimensions = [...parentDimensions, node.qText]
        node.qSubNodes.forEach(subNode => {
          processNode(subNode, currentDimensions)
        })
      } else {
        // Nó folha - criar linha com as dimensões e valor
        // Para nós folha, parentDimensions contém as dimensões
        // e node.qValue contém o valor da medida
        const dimensionValues = [...parentDimensions]
        const measureValue = node.qValue
        
        // Construir linha: [dim1, dim2, ..., medida]
        const row = []
        
        // Adicionar dimensões formatadas
        dimensionValues.forEach(dimValue => {
          row.push({
            qText: String(dimValue),
            qNum: isNaN(parseFloat(dimValue)) ? NaN : parseFloat(dimValue)
          })
        })
        
        // Adicionar valor da medida
        row.push({
          qText: String(measureValue),
          qNum: typeof measureValue === 'number' ? measureValue : parseFloat(measureValue) || 0
        })
        
        matrix.push(row)
      }
    }
    
    subNodes.forEach(node => processNode(node))
    return matrix
  }

  // ✅ NOVO: Função para extrair dados de qPivotDataPages
  const extractFromPivotData = (pivotPage, hypercube) => {

    const matrix = []
    
    if (!pivotPage || !pivotPage.qData || !Array.isArray(pivotPage.qData)) {
      console.warn(`⚠️ TABLE_DEBUG_PIVOT_INVALID_STRUCTURE:`, JSON.stringify({
        reason: 'qData não encontrado ou não é array',
        pivotPage: pivotPage
      }, null, 2))
      return matrix
    }

    // ✅ NOVO: Verificar se estamos recebendo dados incompletos
    const expectedColumns = hypercube.qSize?.qcx || 12
    const receivedColumns = pivotPage.qTop?.length || 0
    
    if (receivedColumns < expectedColumns) {
      console.warn(`⚠️ TABLE_DEBUG_PIVOT_INCOMPLETE_DATA:`, JSON.stringify({
        expectedColumns,
        receivedColumns,
        difference: expectedColumns - receivedColumns,
        qArea: pivotPage.qArea,
        message: 'Dados incompletos detectados - pode ser necessário ajustar qInitialDataFetch'
      }, null, 2))
    }

    // Estrutura dos dados pivot:
    // qLeft = dimensões das linhas (lado esquerdo)
    // qTop = dimensões das colunas (topo) 
    // qData = matriz de valores [linha][coluna]
    
    const qLeft = pivotPage.qLeft || []
    const qTop = pivotPage.qTop || []
    const qData = pivotPage.qData
    
    // ✅ MELHORADO: Processamento mais robusto dos dados pivot
    if (qData.length > 0 && qTop.length > 0) {
      // Caso 1: Dados estruturados normalmente (qData como matriz)
      qData.forEach((dataRow, rowIndex) => {

        if (!Array.isArray(dataRow)) {
          console.warn(`⚠️ TABLE_DEBUG_PIVOT_ROW_NOT_ARRAY_${rowIndex}:`, JSON.stringify({
            dataRow,
            type: typeof dataRow
          }, null, 2))
          return
        }

        // Construir linha da matriz
        const matrixRow = []
        
        // 1. Adicionar dimensões do lado esquerdo (qLeft)
        if (qLeft.length > 0 && rowIndex < qLeft.length) {
          const leftDimension = qLeft[rowIndex]
          matrixRow.push({
            qText: leftDimension?.qText || '',
            qNum: leftDimension?.qNum || (isNaN(parseFloat(leftDimension?.qValue)) ? NaN : parseFloat(leftDimension?.qValue))
          })
        }
        
        // 2. Adicionar valores das medidas para cada coluna (qData)
        dataRow.forEach((cell, colIndex) => {
          const processedCell = {
            qText: cell?.qText || String(cell?.qNum || 0),
            qNum: cell?.qNum || 0,
            qType: cell?.qType || 'V'
          }
          
          matrixRow.push(processedCell)
        
        })
        
        if (matrixRow.length > 0) {
          matrix.push(matrixRow)
        }
      })
    }
    
    // ✅ MELHORADO: Caso alternativo - se não há dados em qData mas há dados em qLeft e qTop 
    if (matrix.length === 0 && qLeft.length > 0 && qTop.length > 0) {

      // ✅ NOVO: Tentar criar linha combinando qLeft e valores de qTop
      const alternativeRow = []
      
      // Se qLeft tem apenas uma entrada (linha de título), usar como primeira coluna
      if (qLeft[0]) {
        alternativeRow.push({
          qText: qLeft[0].qText || '',
          qNum: qLeft[0].qNum || NaN
        })
      }
      
      // ✅ NOVO: Se qTop contém os dados (caso especial), usá-los como valores das colunas
      qTop.forEach((topCell, index) => {
        // Se topCell tem qValue, usar como número; senão usar qNum
        const cellValue = topCell?.qValue || topCell?.qNum || 0
        const cellText = topCell?.qText || String(cellValue)
        
        alternativeRow.push({
          qText: cellText,
          qNum: typeof cellValue === 'number' ? cellValue : parseFloat(cellValue) || 0,
          qType: 'V'
        })
        
      })
      
      if (alternativeRow.length > 0) {
        matrix.push(alternativeRow)
      }
    }
    
    return matrix
  }

  const processMatrixData = (matrix, hypercube, title) => {
    const dimensionCount = hypercube.qDimensionInfo?.length || 0
    const measureCount = hypercube.qMeasureInfo?.length || 0

    // Usar o título configurado se disponível
    const tituloFinal = objetoFinal.config.nome || title

    // ✅ DEBUG: Log específico para filtros no processamento de matrix
    if (objetoFinal.config?.tipo === 'filter') {

      // Verificar se há dados suficientes para criar um filtro
      if (matrix.length === 0) {
        
        return {
          tipo: 'filter',
          titulo: tituloFinal,
          items: [],
          selectedItems: [],
          searchText: '',
          debug: 'Matrix vazia'
        }
      }

      // Processar matrix para criar dados de filtro
      const filterItems = []
      
      matrix.forEach((row, rowIndex) => {
        
        // Para filtros, normalmente usamos a primeira coluna
        const cell = row[0]
        if (cell) {
          const filterItem = {
            id: rowIndex,
            value: cell.qText || cell.qNum?.toString() || `Item ${rowIndex + 1}`,
            text: cell.qText || cell.qNum?.toString() || '',
            selected: cell.qState === 'S',
            excluded: cell.qState === 'X',
            possible: cell.qState === 'O' || cell.qState === undefined,
            qState: cell.qState,
            qNum: cell.qNum,
            qText: cell.qText
          }
          
          filterItems.push(filterItem)
        }
      })

      const selectedItems = filterItems.filter(item => item.selected)
      
      const filterData = {
        tipo: 'filter',
        titulo: tituloFinal,
        items: filterItems,
        selectedItems: selectedItems,
        searchText: '',
        debug: {
          originalMatrix: matrix.slice(0, 2),
          processedItemsCount: filterItems.length,
          selectedCount: selectedItems.length,
          hypercubeInfo: {
            dimensions: dimensionCount,
            measures: measureCount
          }
        }
      }

      return filterData
    }

    // ✅ NOVO: DEBUG ESPECÍFICO PARA TABELAS
    if (objetoFinal.config?.tipo === 'table') {

      // Verificar se há dados suficientes para criar uma tabela
      if (matrix.length === 0) {
        //console.log(`❌ DEBUG_TABLE_ERROR: Matriz vazia para tabela`)
        return {
          tipo: 'table',
          titulo: tituloFinal,
          columns: [],
          rows: [],
          debug: 'Matriz vazia'
        }
      }

      // ✅ NOVO: Processamento especial para Pivot Tables
      if (hypercube.qMode === 'P' && hypercube.qPivotDataPages?.length > 0) {
        //console.log(`🔄 TABLE_DEBUG_PIVOT_TABLE_PROCESSING: Processando pivot table`)
        
        const pivotPage = hypercube.qPivotDataPages[0]
        const qLeft = pivotPage.qLeft || []
        const qTop = pivotPage.qTop || []
        
        // Criar estrutura de tabela baseada na estrutura pivot
        const columns = []
        const rows = []

        // ✅ CORRIGIDO: Não incluir primeira coluna se for vazia ou espaço em branco
        const shouldIncludeFirstColumn = qLeft.length > 0 && 
          qLeft[0].qText && 
          qLeft[0].qText.trim() !== '' && 
          qLeft[0].qText !== ' '

        if (shouldIncludeFirstColumn) {
          columns.push({
            id: 'row_dimension',
            label: qLeft[0].qText || 'Dimensão',
            type: 'text',
            sortable: true,
            filterable: true,
            minWidth: 150,
            align: 'left'
          })
        }

        // 2. Colunas baseadas em qTop (dimensões de coluna - os meses)
        qTop.forEach((topHeader, index) => {
          columns.push({
            id: `col_${index}`,
            label: topHeader.qText || `Coluna ${index + 1}`,
            type: 'qlik_formatted', // ✅ NOVO: Tipo especial para preservar formatação do Qlik
            sortable: true,
            filterable: false,
            minWidth: 100, // ✅ OTIMIZADO: Reduzido para caber melhor na tela
            width: 110, // ✅ NOVO: Largura fixa otimizada
            align: 'right'
          })
        })

        //console.log(`📋 TABLE_DEBUG_PIVOT_COLUMNS:`, JSON.stringify(columns, null, 2))

        // 3. Construir linhas baseado na matriz processada
        matrix.forEach((row, rowIndex) => {
          const rowData = { id: rowIndex }
          
          let startIndex = 0
          
          // Primeira coluna: dimensão de linha (só se não for vazia)
          if (shouldIncludeFirstColumn && row.length > 0) {
            rowData['row_dimension'] = row[0]?.qText || '-'
            startIndex = 1
          } else {
            // Se não incluir primeira coluna, começar do índice 1 (pular a coluna vazia)
            startIndex = 1
          }
          
          // Demais colunas: valores das medidas
          for (let colIndex = startIndex; colIndex < row.length; colIndex++) {
            const cell = row[colIndex]
            const columnId = `col_${colIndex - startIndex}`
            
            // ✅ PRESERVAR formatação original do Qlik
            rowData[columnId] = cell?.qText || String(cell?.qNum || 0)

          }
          
          rows.push(rowData)
        })

        const pivotTableData = {
          tipo: 'table',
          titulo: tituloFinal,
          columns: columns,
          rows: rows,
          debug: {
            pivotMode: true,
            originalMatrix: matrix.slice(0, 2),
            qLeftHeaders: qLeft.map(l => l.qText),
            qTopHeaders: qTop.map(t => t.qText),
            processedRowsCount: rows.length,
            columnsCount: columns.length,
            shouldIncludeFirstColumn,
            hypercubeInfo: {
              dimensions: dimensionCount,
              measures: measureCount,
              mode: hypercube.qMode
            }
          }
        }

        return pivotTableData
      }

      // ✅ EXISTENTE: Processamento padrão para tabelas não-pivot
      // Criar estrutura de tabela baseada nos dados do Qlik
      const columns = []
      const rows = []

      // Construir colunas baseado nas dimensões e medidas
      if (hypercube.qDimensionInfo) {
        hypercube.qDimensionInfo.forEach((dim, index) => {
          columns.push({
            id: `dim_${index}`,
            label: dim.qFallbackTitle || dim.qGroupFieldDefs?.[0]?.qName || `Dimensão ${index + 1}`,
            type: 'text',
            sortable: true,
            filterable: true,
            minWidth: 150,
            align: 'left'
          })
        })
      }

      if (hypercube.qMeasureInfo) {
        hypercube.qMeasureInfo.forEach((measure, index) => {
          columns.push({
            id: `measure_${index}`,
            label: measure.qFallbackTitle || `Medida ${index + 1}`,
            type: 'number', // Pode ser currency, percentage, etc.
            sortable: true,
            filterable: true,
            minWidth: 120,
            align: 'right'
          })
        })
      }

      //console.log(`📋 DEBUG_TABLE_COLUMNS:`, JSON.stringify(columns, null, 2))

      // Construir linhas baseado na matriz
      matrix.forEach((row, rowIndex) => {
        const rowData = { id: rowIndex }
        
        // Processar dimensões
        if (hypercube.qDimensionInfo) {
          hypercube.qDimensionInfo.forEach((dim, dimIndex) => {
            const cell = row[dimIndex]
            rowData[`dim_${dimIndex}`] = cell?.qText || cell?.qNum || '-'
          })
        }

        // Processar medidas
        if (hypercube.qMeasureInfo) {
          hypercube.qMeasureInfo.forEach((measure, measureIndex) => {
            const cellIndex = dimensionCount + measureIndex
            const cell = row[cellIndex]
            
            // Determinar tipo de dados e formatação
            let value = cell?.qNum || 0
            let formattedValue = cell?.qText || String(value)
            
            // Se tem formato especial (como DUAL), processar
            if (formattedValue.includes('\n') && formattedValue.includes('|')) {
              const lines = formattedValue.split('\n').map(l => l.trim())
              formattedValue = lines[0] || String(value) // Usar primeira linha como valor exibido
            }
            
            rowData[`measure_${measureIndex}`] = formattedValue
    
          })
        }

        rows.push(rowData)
      })

      const tableData = {
        tipo: 'table',
        titulo: tituloFinal,
        columns: columns,
        rows: rows,
        debug: {
          originalMatrix: matrix.slice(0, 2),
          processedRowsCount: rows.length,
          columnsCount: columns.length,
          hypercubeInfo: {
            dimensions: dimensionCount,
            measures: measureCount
          }
        }
      }

      return tableData
    }

    // Para Multi KPI - CORRIGIDO: pode ser 0 dimensões + múltiplas medidas
    if (objetoFinal.config?.tipo === 'multiKpi') {
      const kpis = []
      
      // Caso 1: 1 dimensão + 1 medida (formato anterior)
      if (dimensionCount === 1 && measureCount === 1) {
        // Processar TODAS as linhas da matriz para Multi KPI
        matrix.forEach((row, index) => {
          const label = row[0]?.qText || `KPI ${index + 1}`
          const valorCompleto = row[1]?.qText || '0'
          const valorNumerico = row[1]?.qNum || 0
          
          // Processar dados formatados...
          let valorProcessado = valorCompleto
          let valorFinal = valorNumerico
          
          if (valorCompleto.includes('\n') && valorCompleto.includes('|')) {
            const partes = valorCompleto.split('\n').map(p => p.replace(/^\s*\|\s*/, '').trim())
            if (partes.length >= 3) {
              const quantidade = partes[1]
              valorProcessado = quantidade
              valorFinal = parseFloat(quantidade.replace(/[^\d,.-]/g, '').replace(',', '.')) || valorNumerico
            }
          }
          
          const kpiConfig = objetoFinal.config.kpiConfig || {}
          const coresKPI = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336', '#00BCD4', '#795548', '#607D8B', '#E91E63', '#FF5722']
          const iconesKPI = ['analytics', 'money', 'person', 'business', 'cart', 'timeline', 'assessment', 'star', 'bank', 'atm']
          
          kpis.push({
            titulo: label,
            valor: valorFinal,
            valorFormatado: valorProcessado,
            cor: kpiConfig.cor || coresKPI[index % coresKPI.length],
            corFonte: kpiConfig.corFonte || '#ffffff',
            icone: kpiConfig.icone || iconesKPI[index % iconesKPI.length],
            formato: kpiConfig.formato || 'numero',
            prefix: kpiConfig.prefix || '',
            suffix: kpiConfig.suffix || '',
            casasDecimais: kpiConfig.casasDecimais || 0,
            showIcon: kpiConfig.showIcon !== false,
            gradiente: kpiConfig.gradiente || false,
            descricao: kpiConfig.descricao || ''
          })
        })
      }
      // Caso 2: 0 dimensões + múltiplas medidas (formato atual)
      else if (dimensionCount === 0 && measureCount > 1 && matrix.length > 0) {
        const firstRow = matrix[0]
        
        // Processar cada medida como um KPI separado
        firstRow.forEach((cell, index) => {
          const textoCompleto = cell?.qText || ''
          const valorNumerico = cell?.qNum || 0
          
          // Extrair label e valor do texto
          let label = `KPI ${index + 1}`
          let valorProcessado = textoCompleto
          let valorFinal = 0
          
          // Formato esperado: "Label: valor" ou "Label:  valor\n"
          if (textoCompleto.includes(':')) {
            const partes = textoCompleto.split(':')
            if (partes.length >= 2) {
              label = partes[0].trim()
              const valorParte = partes[1].replace('\n', '').trim()
              valorProcessado = valorParte
              
              // Extrair número do valor
              const numeroMatch = valorParte.match(/[\d.,]+/)
              if (numeroMatch) {
                valorFinal = parseFloat(numeroMatch[0].replace('.', '').replace(',', '.')) || 0
              }
            }
          }
          
          const kpiConfig = objetoFinal.config?.kpiConfig || {}
          const coresKPI = ['#4CAF50', '#FF5722', '#FF9800', '#9C27B0', '#F44336', '#00BCD4', '#795548', '#607D8B', '#E91E63', '#2196F3']
          const iconesKPI = ['analytics', 'money', 'person', 'business', 'cart', 'timeline', 'assessment', 'star', 'bank', 'atm']
          
          kpis.push({
            titulo: label,
            valor: valorFinal,
            valorFormatado: valorProcessado,
            cor: kpiConfig.cor || coresKPI[index % coresKPI.length],
            corFonte: kpiConfig.corFonte || '#ffffff',
            icone: kpiConfig.icone || iconesKPI[index % iconesKPI.length],
            formato: kpiConfig.formato || 'numero',
            prefix: kpiConfig.prefix || '',
            suffix: kpiConfig.suffix || '',
            casasDecimais: kpiConfig.casasDecimais || 0,
            showIcon: kpiConfig.showIcon !== false,
            gradiente: kpiConfig.gradiente || false,
            descricao: kpiConfig.descricao || ''
          })
        })
      }
      
      const multiKpiData = {
        tipo: 'multiKpi',
        titulo: tituloFinal,
        kpis: kpis
      }
      
      //console.log(`📊 DEBUG_MULTI_KPI_DATA_FINAL:`, JSON.stringify(multiKpiData, null, 2))
      return multiKpiData
    }

    // Para KPIs simples (sem dimensões, apenas 1 medida)
    if (dimensionCount === 0 && measureCount === 1) {
      const firstRow = matrix[0] || []
      const value = firstRow[0]?.qNum || 0
      const formatted = firstRow[0]?.qText || '0'
      
      const kpiData = {
        tipo: 'kpi',
        titulo: tituloFinal,
        valor: value,
        valorFormatado: formatted,
        medida: hypercube.qMeasureInfo[0]?.qFallbackTitle || 'Valor'
      }
      
      //console.log(`📊 DEBUG_KPI_DATA:`, JSON.stringify(kpiData, null, 2))
      return kpiData
    }

    // Para gráficos (com dimensões e medidas) - BASEADO NO SEU EXEMPLO
    if (dimensionCount > 0) {
      // Estrutura baseada no seu código de exemplo
      const categories = []
      const timePoints = []
      const rawData = []
      
      // ✅ NOVO: Função para extrair dados formatados do DUAL()
      const extractFormattedData = (cell) => {
        const qText = cell?.qText || ''
        const qNum = cell?.qNum || 0
        
        // Se tem formato DUAL com chr(10) e |
        if (qText.includes('\n') && qText.includes('|')) {
          // Formato: "69,51%\n |1.785\n |R$ 484.890.702,44"
          const linhas = qText.split('\n').map(l => l.trim())
          
          const percentual = linhas[0] || '' // 69,51%
          const quantidade = linhas[1]?.replace(/^\|?\s*/, '') || '' // 1.785
          const valor = linhas[2]?.replace(/^\|?\s*/, '') || '' // R$ 484.890.702,44
          
          // Usar a quantidade como valor principal para ordenação
          let valorParaOrdenacao = qNum
          if (quantidade && quantidade !== '') {
            const numeroExtraido = parseFloat(quantidade.replace(/[^\d,.-]/g, '').replace(/\./g, '').replace(',', '.'))
            if (!isNaN(numeroExtraido)) {
              valorParaOrdenacao = numeroExtraido
            }
          }
          
          // Retornar dados estruturados
          return {
            valorNumerico: valorParaOrdenacao,
            valorFormatado: percentual, // Mostrar percentual no tooltip/label
            detalhes: {
              percentual,
              quantidade,
              valor
            },
            textoCompleto: qText
          }
        }
        
        // Formato simples
        return {
          valorNumerico: qNum,
          valorFormatado: qText || String(qNum),
          textoCompleto: qText
        }
      }
      
      // Extrair dados da matriz - seguindo sua lógica
      matrix.forEach(row => {
        if (dimensionCount === 1) {
          // Gráfico simples: 1 dimensão + medidas
          const categoria = row[0]?.qText || row[0]?.qNum || 'N/A'
          
          // Processar todas as medidas
          for (let i = 0; i < measureCount; i++) {
            const measureIndex = dimensionCount + i
            const medidaInfo = hypercube.qMeasureInfo[i]
            
            // ✅ NOVO: Extrair nome limpo da medida
            let nomeMedida = `Medida ${i + 1}`
            if (medidaInfo?.qFallbackTitle) {
              const titulo = medidaInfo.qFallbackTitle
              
              // Se contém fórmula DUAL, tentar extrair nome mais limpo
              if (titulo.includes('=DUAL(') || titulo.includes('=')) {
                // Para este caso específico, usar um nome genérico baseado nos dados
                nomeMedida = 'Valores'
              } else {
                nomeMedida = titulo
              }
            }
            
            // ✅ NOVO: Processar dados formatados
            const dadosFormatados = extractFormattedData(row[measureIndex])
            
            rawData.push({
              categoria: categoria,
              medida: nomeMedida,
              valorNumerico: dadosFormatados.valorNumerico,
              valorFormatado: dadosFormatados.valorFormatado,
              detalhes: dadosFormatados.detalhes,
              textoCompleto: dadosFormatados.textoCompleto
            })
          }
          
          if (!categories.includes(categoria)) {
            categories.push(categoria)
          }
          
        } else if (dimensionCount === 2) {
          // Gráfico de linha temporal: Ano + Categoria (igual ao seu exemplo)
          const ano = row[0]?.qText || row[0]?.qNum // Primeira dimensão (Ano/Tempo)
          const categoria = row[1]?.qText || row[1]?.qNum // Segunda dimensão (Categoria)
          
          // ✅ NOVO: Processar dados formatados para múltiplas dimensões
          const dadosFormatados = extractFormattedData(row[2])
          
          if (!timePoints.includes(ano)) {
            timePoints.push(ano)
          }
          
          if (!categories.includes(categoria)) {
            categories.push(categoria)
          }
          
          rawData.push({
            ano: ano,
            categoria: categoria,
            valorNumerico: dadosFormatados.valorNumerico,
            valorFormatado: dadosFormatados.valorFormatado,
            detalhes: dadosFormatados.detalhes,
            textoCompleto: dadosFormatados.textoCompleto,
            medida: hypercube.qMeasureInfo[0]?.qFallbackTitle || 'Valor'
          })
        }
      })
      
      // Processar dados baseado no número de dimensões
      let chartData
      
      if (dimensionCount === 1) {
        // Extrair categorias na ordem original (ordenação será feita no CustomChart)
        const categoriasOriginais = [...new Set(rawData.map(d => d.categoria))]
        const medidasUnicas = [...new Set(rawData.map(d => d.medida))]
        
        const series = []
        
        // Agrupar por medida
        medidasUnicas.forEach(medidaNome => {
          const dadosDaMedida = rawData.filter(d => d.medida === medidaNome)
          const serieData = categoriasOriginais.map(categoria => {
            const item = dadosDaMedida.find(d => d.categoria === categoria)
            if (item) {
              return {
                value: item.valorNumerico,
                formattedValue: item.valorFormatado,
                details: item.detalhes,
                originalText: item.textoCompleto,
                name: categoria
              }
            }
            return { value: 0, formattedValue: '0', originalText: '0' }
          })
          
          series.push({
            name: medidaNome,
            data: serieData
          })
        })
        
        chartData = {
          categories: categoriasOriginais,
          series: series,
          dimensoes: [hypercube.qDimensionInfo[0]?.qFallbackTitle || 'Categoria'],
          medidas: hypercube.qMeasureInfo?.map(m => m.qFallbackTitle) || []
        }
        
      } else if (dimensionCount === 2) {
        // Gráfico temporal: igual ao seu exemplo
        timePoints.sort((a, b) => {
          // Tentar ordenar numericamente se possível
          const aNum = parseFloat(a)
          const bNum = parseFloat(b)
          if (!isNaN(aNum) && !isNaN(bNum)) {
            return aNum - bNum
          }
          return a.localeCompare(b)
        })
        
        // Filtrar categorias válidas
        const filteredCategories = categories.filter(cat => cat !== "-" && String(cat).trim() !== "")
        
        // Configurar data para cada categoria - IGUAL AO SEU EXEMPLO
        const seriesData = {}
        filteredCategories.forEach(categoria => {
          seriesData[categoria] = {}
          timePoints.forEach(ano => {
            seriesData[categoria][ano] = {
              value: 0,
              formattedValue: '0',
              details: null,
              originalText: '0'
            }
          })
        })
        
        // ✅ NOVO: Preencher dados formatados para cada categoria por ano
        rawData.forEach(item => {
          if (seriesData[item.categoria] && seriesData[item.categoria][item.ano] !== undefined) {
            seriesData[item.categoria][item.ano] = {
              value: item.valorNumerico,
              formattedValue: item.valorFormatado,
              details: item.detalhes,
              originalText: item.textoCompleto
            }
          }
        })
        
        // Criar séries para ECharts no formato correto
        const series = filteredCategories.map(categoria => ({
          name: categoria,
          data: timePoints.map(ano => seriesData[categoria][ano] || { value: 0, formattedValue: '0' })
        }))
        
        chartData = {
          categories: timePoints, // No caso temporal, timePoints são as categorias do eixo X
          series: series,
          dimensoes: hypercube.qDimensionInfo?.map(d => d.qFallbackTitle) || [],
          medidas: hypercube.qMeasureInfo?.map(m => m.qFallbackTitle) || []
        }
      }
      
      const finalChartData = {
        tipo: 'chart',
        titulo: tituloFinal,
        dados: chartData
      }
      
      //console.log(`📈 DEBUG_CHART_DATA_FINAL:`, JSON.stringify(finalChartData, null, 2))
      return finalChartData
    }

    // Se chegou aqui, não conseguiu processar - mas pode ter dados
    // Vamos tentar forçar como gráfico se há dados
    if (matrix.length > 0 && matrix[0].length > 0) {
      
      const categories = []
      const series = [{
        name: 'Valores',
        data: []
      }]

      matrix.forEach((row, index) => {
        categories.push(`Item ${index + 1}`)
        series[0].data.push(row[0]?.qNum || 0)
      })

      const fallbackData = {
        tipo: 'chart',
        titulo: tituloFinal,
        dados: {
          categories,
          series,
          dimensoes: ['Categoria'],
          medidas: ['Valor']
        }
      }
      
      //console.log(`🔄 DEBUG_FALLBACK_DATA:`, JSON.stringify(fallbackData, null, 2))
      return fallbackData
    }
    
    return {
      tipo: 'empty',
      titulo: tituloFinal,
      dados: { dimensoes: [], series: [] }
    }
  }

  const renderContent = () => {
    // Debug do estado de renderização - REMOVIDO PARA REDUZIR LOGS
    // console.log(`🎯 DEBUG_RENDER_STATE:`, JSON.stringify({
    //   loading,
    //   error: error?.message || null,
    //   hasObjectData: !!objectData,
    //   objectDataType: objectData?.tipo,
    //   objectDataTitulo: objectData?.titulo
    // }, null, 2))

    if (loading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" height="100%">
          <CircularProgress size={40} />
          <Typography variant="body2" sx={{ ml: 2 }}>
            Carregando dados...
          </Typography>
        </Box>
      )
    }

    if (error) {
      // Em caso de erro, mostrar um gráfico simulado simples
      const errorSimulation = {
        tipo: 'chart',
        titulo: 'Dados Indisponíveis',
        dados: {
          categories: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'],
          series: [{
            name: 'Exemplo',
            data: [15, 25, 30, 20, 35, 28]
          }],
          dimensoes: ['Mês'],
          medidas: ['Valor']
        }
      }
      
      return (
        <CustomChart 
          data={errorSimulation}
          config={{
            tipoGrafico: 'line',
            showTitle: true,
            showLegend: false,
            ...objetoFinal.config.chartConfig
          }}
          onRefresh={() => {
            // Recarregar dados após erro de conexão
            //console.log('🔄 Tentando reconectar após erro')
          }}
        />
      )
    }

    if (!objectData) {
      //console.log(`⚠️ DEBUG_NULL_OBJECT_DATA: objectData é null/undefined`)
      // Em caso de dados nulos, mostrar um gráfico simulado simples
      const nullDataSimulation = {
        tipo: 'chart',
        titulo: 'Dados Não Disponíveis',
        dados: {
          categories: ['A', 'B', 'C', 'D', 'E', 'F'],
          series: [{
            name: 'Simulação',
            data: [12, 19, 23, 17, 25, 21]
          }],
          dimensoes: ['Categoria'],
          medidas: ['Valor']
        }
      }
      
      return (
        <CustomChart 
          data={nullDataSimulation}
          config={{
            tipoGrafico: 'line',
            showTitle: true,
            showLegend: false,
            ...objetoFinal.config.chartConfig
          }}
          onRefresh={() => {
            // Recarregar dados quando são nulos
            //console.log('🔄 Tentando recarregar dados nulos')
          }}
        />
      )
    }

    // console.log(`🎯 DEBUG_OBJECT_DATA_FULL:`, JSON.stringify(objectData, null, 2))

    // Renderizar baseado no tipo de dados
    if (objectData.tipo === 'kpi') {
      const kpiConfig = objetoFinal.config?.kpiConfig || {}
      // console.log(`📊 DEBUG_RENDER_KPI:`, JSON.stringify({
      //   objectData,
      //   kpiConfig
      // }, null, 2))
      
      return (
        <CustomKPI 
          data={objectData}
          config={kpiConfig}
          objectId={objetoFinal.id}
        />
      )
    }

    if (objectData.tipo === 'multiKpi') {
      const multiKpiConfig = objetoFinal.config?.multiKpiConfig || {}
      // console.log(`📊 DEBUG_RENDER_MULTI_KPI:`, JSON.stringify({
      //   objectData,
      //   multiKpiConfig
      // }, null, 2))
      
      return (
        <CustomMultiKPI 
          data={objectData}
          config={multiKpiConfig}
        />
      )
    }

    if (objectData.tipo === 'table') {
      
      return (
        <CustomTable 
          data={objectData}
          config={objetoFinal.config}
          objectId={objetoFinal.id}
        />
      )
    }

    if (objectData.tipo === 'filter') {
      
      return (
        <CustomFilter 
          data={objectData}
          config={{...objetoFinal.config, appId: objetoFinal.appId}}
          objectId={objetoFinal.id}
        />
      )
    }

    if (objectData.tipo === 'native_filter') {
      
      // Para filtros nativos, apenas mostrar uma mensagem ou container vazio
      // O filtro já foi renderizado diretamente no container
      return (
        <Box sx={{ 
          width: '100%', 
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'transparent'
        }}>
          <Typography variant="body2" color="text.secondary">
            Filtro Nativo Renderizado
          </Typography>
        </Box>
      )
    }

    if (objectData.tipo === 'chart') {
      // ✅ SIMPLIFICADO: Apenas garantir que o tipo "mixed" seja preservado
      const chartConfig = {
        tipoGrafico: objetoFinal.config?.chartConfig?.tipoGrafico || objetoFinal.config?.tipo || 'line',
        showTitle: true,
        showLegend: true,
        showAxes: true,
        ...objetoFinal.config?.chartConfig
      }
      
      // ✅ CORRIGIDO: Só remover mixedConfig se o tipo NÃO for mixed
      if (chartConfig.tipoGrafico !== 'mixed' && objetoFinal.config?.tipo !== 'mixed') {
        delete chartConfig.mixedConfig
      }
      
      // ✅ NOVO: Se o tipo principal for mixed, garantir que chartConfig.tipoGrafico também seja mixed
      if (objetoFinal.config?.tipo === 'mixed') {
        chartConfig.tipoGrafico = 'mixed'
      }
      
      return (
        <CustomChart 
          data={objectData}
          config={chartConfig}
          onExpand={() => {
            // Funcionalidade personalizada de expansão para gráficos do Qlik
            //console.log('🔍 Expandindo gráfico:', objetoFinal.id)
          }}
          onRefresh={() => {
            // Funcionalidade personalizada de atualização
            //console.log('🔄 Atualizando gráfico:', objetoFinal.id)
            // Recarregar dados do Qlik
          }}
          onSettings={() => {
            // Funcionalidade de configurações se disponível
            if (onEdit) {
              onEdit(objetoFinal)
            }
          }}
        />
      )
    }

    if (objectData.tipo === 'empty') {
      //console.log(`📭 DEBUG_RENDER_EMPTY: Dados vazios detectados`)
      // Para dados vazios, também mostrar gráfico simulado
      const emptyDataSimulation = {
        tipo: 'chart',
        titulo: 'Sem Dados',
        dados: {
          categories: ['1', '2', '3', '4', '5'],
          series: [{
            name: 'Dados',
            data: [8, 12, 15, 10, 18]
          }],
          dimensoes: ['Item'],
          medidas: ['Quantidade']
        }
      }
      
      return (
        <CustomChart 
          data={emptyDataSimulation}
          config={{
            tipoGrafico: 'line',
            showTitle: true,
            showLegend: false,
            ...objetoFinal.config.chartConfig
          }}
          onRefresh={() => {
            // Recarregar dados quando vazios
            //console.log('🔄 Tentando recarregar dados vazios')
          }}
        />
      )
    }

    if (objectData.tipo === 'error') {
      
      // Para erros, também mostrar gráfico simulado
      const errorSimulation = {
        tipo: 'chart',
        titulo: 'Erro nos Dados',
        dados: {
          categories: ['T1', 'T2', 'T3', 'T4', 'T5'],
          series: [{
            name: 'Fallback',
            data: [5, 10, 8, 15, 12]
          }],
          dimensoes: ['Tempo'],
          medidas: ['Valor']
        }
      }
      
      return (
        <CustomChart 
          data={errorSimulation}
          config={{
            tipoGrafico: 'line',
            showTitle: true,
            showLegend: false,
            ...objetoFinal.config.chartConfig
          }}
          onRefresh={() => {
            // Recarregar dados em caso de erro
            //console.log('🔄 Tentando recarregar após erro nos dados')
          }}
        />
      )
    }
    
    // Fallback final - sempre mostrar um gráfico
    const unknownTypeSimulation = {
      tipo: 'chart',
      titulo: 'Tipo Desconhecido',
      dados: {
        categories: ['X', 'Y', 'Z'],
        series: [{
          name: 'Dados',
          data: [10, 15, 12]
        }],
        dimensoes: ['Eixo'],
        medidas: ['Valor']
      }
    }
    
    return (
      <CustomChart 
        data={unknownTypeSimulation}
        config={{
          tipoGrafico: 'line',
          showTitle: true,
          showLegend: false,
          ...objetoFinal.config.chartConfig
        }}
        onRefresh={() => {
          // Recarregar dados para tipo desconhecido
          //console.log('🔄 Tentando recarregar dados de tipo desconhecido')
        }}
      />
    )
  }

  return (
    <Box 
      ref={containerRef}
      sx={{ 
        width: '100%', 
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {renderContent()}
    </Box>
  )
}

export default QlikObject