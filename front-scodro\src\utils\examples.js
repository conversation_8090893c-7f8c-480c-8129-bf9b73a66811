// Exemplos de uso dos componentes QlikObject

import React from 'react'
import QlikObject from '@components/QlikObject'
import { APP_IDS, OBJECT_IDS } from '@config/apps'

// Exemplo 1: KPI simples
export const ExemploKPI = () => (
  <QlikObject
    objectId={OBJECT_IDS.kpi1}
    appId={APP_IDS.dashboard}
    height="150px"
    width="100%"
    onLoad={(obj) => console.log('KPI carregado:', obj)}
    onError={(err) => console.error('Erro no KPI:', err)}
  />
)

// Exemplo 2: Gráfico com callback personalizado
export const ExemploGrafico = () => (
  <QlikObject
    objectId={OBJECT_IDS.chart1}
    appId={APP_IDS.dashboard}
    height="400px"
    className="meu-grafico-customizado"
    onLoad={(obj) => {
      console.log('Gráfico carregado com sucesso!')
      // Aqui você pode adicionar lógica personalizada
      // Por exemplo, salvar referência do objeto para uso posterior
    }}
    onError={(err) => {
      console.error('Falha ao carregar gráfico:', err)
      // Aqui você pode implementar fallback ou notificação de erro
    }}
  />
)

// Exemplo 3: Tabela responsiva
export const ExemploTabela = () => (
  <div style={{ width: '100%', overflowX: 'auto' }}>
    <QlikObject
      objectId={OBJECT_IDS.table1}
      appId={APP_IDS.vendas}
      height="500px"
      width="100%"
    />
  </div>
)

// Exemplo 4: Grid de objetos
export const ExemploGrid = () => (
  <div style={{ 
    display: 'grid', 
    gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
    gap: '20px',
    padding: '20px'
  }}>
    <div style={{ background: 'white', padding: '15px', borderRadius: '8px' }}>
      <h3>Vendas Mensais</h3>
      <QlikObject
        objectId={OBJECT_IDS.chart1}
        appId={APP_IDS.vendas}
        height="250px"
      />
    </div>
    
    <div style={{ background: 'white', padding: '15px', borderRadius: '8px' }}>
      <h3>KPI Principal</h3>
      <QlikObject
        objectId={OBJECT_IDS.kpi1}
        appId={APP_IDS.dashboard}
        height="250px"
      />
    </div>
    
    <div style={{ background: 'white', padding: '15px', borderRadius: '8px' }}>
      <h3>Distribuição</h3>
      <QlikObject
        objectId={OBJECT_IDS.chart2}
        appId={APP_IDS.dashboard}
        height="250px"
      />
    </div>
  </div>
)

// Exemplo 5: Objeto com redimensionamento automático
export const ExemploResponsivo = () => {
  const [dimensions, setDimensions] = React.useState({ width: '100%', height: '400px' })
  
  React.useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth
      if (width < 768) {
        setDimensions({ width: '100%', height: '300px' })
      } else if (width < 1024) {
        setDimensions({ width: '100%', height: '400px' })
      } else {
        setDimensions({ width: '100%', height: '500px' })
      }
    }
    
    window.addEventListener('resize', handleResize)
    handleResize() // Executar na montagem
    
    return () => window.removeEventListener('resize', handleResize)
  }, [])
  
  return (
    <QlikObject
      objectId={OBJECT_IDS.chart1}
      appId={APP_IDS.dashboard}
      height={dimensions.height}
      width={dimensions.width}
    />
  )
}

// Exemplo 6: Múltiplos objetos do mesmo app
export const ExemploDashboardCompleto = () => {
  return (
    <div>
      {/* Linha de KPIs */}
      <div style={{ 
        display: 'flex', 
        gap: '20px', 
        marginBottom: '20px',
        flexWrap: 'wrap'
      }}>
        <div style={{ flex: '1', minWidth: '200px' }}>
          <QlikObject
            objectId={OBJECT_IDS.kpi1}
            appId={APP_IDS.dashboard}
            height="120px"
          />
        </div>
        <div style={{ flex: '1', minWidth: '200px' }}>
          <QlikObject
            objectId={OBJECT_IDS.kpi2}
            appId={APP_IDS.dashboard}
            height="120px"
          />
        </div>
        <div style={{ flex: '1', minWidth: '200px' }}>
          <QlikObject
            objectId={OBJECT_IDS.kpi3}
            appId={APP_IDS.dashboard}
            height="120px"
          />
        </div>
      </div>
      
      {/* Linha de gráficos */}
      <div style={{ 
        display: 'flex', 
        gap: '20px',
        flexWrap: 'wrap'
      }}>
        <div style={{ flex: '2', minWidth: '400px' }}>
          <QlikObject
            objectId={OBJECT_IDS.chart1}
            appId={APP_IDS.dashboard}
            height="400px"
          />
        </div>
        <div style={{ flex: '1', minWidth: '300px' }}>
          <QlikObject
            objectId={OBJECT_IDS.chart2}
            appId={APP_IDS.dashboard}
            height="400px"
          />
        </div>
      </div>
    </div>
  )
}

// Dicas de uso:
/*
1. Sempre forneça height explícito para melhor performance
2. Use onLoad e onError para feedback ao usuário
3. Considere usar className para estilos personalizados
4. Em layouts responsivos, ajuste height baseado no viewport
5. Para múltiplos objetos do mesmo app, o serviço reutiliza a conexão
6. Use width="100%" para objetos responsivos
7. Teste sempre em modo desenvolvimento primeiro
*/ 