const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const connectDB = require('./config/database');
const checkDatabase = require('./middleware/checkDatabase');
const empresaRoutes = require('./routes/empresaRoutes');
const objetoRoutes = require('./routes/objetoRoutes');
const healthRoutes = require('./routes/healthRoutes');
const errorHandler = require('./middleware/errorHandler');

// Importar rotas
const appRoutes = require('./routes/appRoutes');
const configMashupRoutes = require('./routes/configMashupRoutes');
const paginaRoutes = require('./routes/paginaRoutes');

const app = express();
const PORT = process.env.PORT || 3031;

// Conectar ao MongoDB (não bloqueia o servidor se falhar)
connectDB();

// Middleware de segurança
app.use(helmet());

// Middleware de compressão
app.use(compression());

// Configurar logs mais limpos para desenvolvimento
const isProduction = process.env.NODE_ENV === 'production';
if (isProduction) {
  // Logs completos em produção
  app.use(morgan('combined'));
} else {
  // Logs mais limpos em desenvolvimento
  app.use(morgan('dev'));
}

// Rate limiting mais permissivo para desenvolvimento
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: isProduction ? 100 : 1000, // 1000 requests em dev, 100 em prod
  message: {
    error: 'Muitas requisições deste IP, tente novamente em 15 minutos.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// CORS melhorado
const allowedOrigins = [
  process.env.FRONTEND_URL || 'http://localhost:3333',
  'http://localhost:4848', // Qlik Sense Desktop
  'http://localhost:4848/extensions', // Qlik Sense Extensions
  'http://localhost:4848/dev-hub', // Qlik Sense Dev Hub
  'https://8a6b797f14d6.ngrok-free.app', // Ngrok tunnel (frontend)
  'http://*************:3333', // IP local do frontend
];

app.use(cors({
  origin: function (origin, callback) {
    // Permitir requests sem origin (mobile apps, Postman, etc.)
    if (!origin) return callback(null, true);
    
    // Verificar se a origem está na lista permitida ou se é localhost em desenvolvimento
    if (allowedOrigins.indexOf(origin) !== -1 || (!isProduction && origin.includes('localhost'))) {
      callback(null, true);
    } else {
      console.log(`🚫 CORS bloqueado para origin: ${origin}`);
      callback(new Error('Não permitido pelo CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-requested-with'],
}));

// Body parser
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Rotas que não precisam do banco
app.use('/api/health', healthRoutes);
app.use('/api/config', configMashupRoutes);

// Rotas que precisam do banco (com middleware de verificação)
app.use('/api/empresas', checkDatabase, empresaRoutes);
app.use('/api/empresas', checkDatabase, paginaRoutes); // Rotas de páginas são subrotas de empresas
app.use('/api/objetos', checkDatabase, objetoRoutes);
app.use('/api/apps', appRoutes);

// Rota raiz
app.get('/', (req, res) => {
  res.json({
    message: 'API SCODRO - Backend do Qlik Mashup',
    version: '2.0.0',
    status: 'online',
    endpoints: {
      health: '/api/health',
      config: '/api/config',
      empresas: '/api/empresas',
      paginas: '/api/empresas/:empresaId/paginas',
      apps: '/api/apps',
      objetos: '/api/objetos'
    },
    documentation: 'Consulte o README.md para documentação completa'
  });
});

// Middleware de tratamento de erros
app.use(errorHandler);

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Rota não encontrada',
    message: `A rota ${req.originalUrl} não existe nesta API`
  });
});

// Iniciar servidor
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`📱 Frontend URL: ${process.env.FRONTEND_URL || 'http://localhost:3333'}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🌐 API Externa URL: http://*************:${PORT}/api`);
  console.log(`📊 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`⚙️ Configurações: http://localhost:${PORT}/api/config`);
});

module.exports = app; 