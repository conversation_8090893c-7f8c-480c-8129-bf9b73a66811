const fs = require('fs')
const path = require('path')

console.log('🏗️ Construindo Mashup para Qlik Sense Enterprise/Desktop...')

// Caminhos
const distPath = path.join(__dirname, '../dist')
const enterprisePath = path.join(__dirname, '../mashup-enterprise')
const distIndexPath = path.join(distPath, 'index.html')
const enterpriseIndexPath = path.join(enterprisePath, 'index.html')

// Verificar se a pasta dist existe
if (!fs.existsSync(distPath)) {
  console.error('❌ Pasta dist não encontrada. Execute "npm run build" primeiro.')
  process.exit(1)
}

// Verificar se o index.html do dist existe
if (!fs.existsSync(distIndexPath)) {
  console.error('❌ Arquivo dist/index.html não encontrado.')
  process.exit(1)
}

try {
  // Ler o index.html do dist
  let htmlContent = fs.readFileSync(distIndexPath, 'utf8')
  
  console.log('📝 Processando HTML para Enterprise...')
  
  // Modificar caminhos dos assets para relativos
  htmlContent = htmlContent.replace(/href="\/assets\//g, 'href="./assets/')
  htmlContent = htmlContent.replace(/src="\/assets\//g, 'src="./assets/')
  
  // Adicionar configurações do Qlik Enterprise APÓS as importações dos assets
  const qlikConfig = `
      <!-- Estilos CSS do Qlik Sense -->
    <link rel="stylesheet" href="../../resources/autogenerated/qlik-styles.css">
    
    <!-- CSS de sobrescrita de fontes - DEVE VIR APÓS qlik-styles.css -->
    <style>
      /* FORÇAR ROBOTO - SOBRESCREVER QLIK STYLES */
      /* Garantir que Roboto seja aplicada mesmo após o Qlik carregar seus estilos */
      body, body *, html, html * {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
      
      /* Sobrescrever especificamente os seletores do Qlik */
      [class*="qlik"], [class*="qlik"] *,
      [id*="qlik"], [id*="qlik"] *,
      .qlik-embed-object, .qlik-embed-object *,
      .qlik-object, .qlik-object * {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
      
      /* Forçar em elementos SVG (gráficos) */
      svg, svg *, svg text, svg tspan {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
      
      /* Material-UI específico */
      .MuiTypography-root, .MuiCard-root, .MuiButton-root,
      .MuiTextField-root, .MuiMenuItem-root, .MuiTab-root {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
    </style>
    
    <!-- RequireJS para Qlik Sense -->
    <script src="../../resources/assets/external/requirejs/require.js"></script>
    
    <!-- Configurações específicas para Enterprise/Desktop -->
    <script>
      // Configuração automática para Enterprise/Desktop
      window.QLIK_MASHUP_CONFIG = {
        environment: 'enterprise',
        autoDetect: true
      };
      
      // Configurar RequireJS para Qlik Sense Desktop
      require.config({
        baseUrl: '/resources/',
        paths: {
          qlik: 'js/qlik'
        }
      });
    </script>`
  
  // Inserir as configurações do Qlik antes do </head>
  htmlContent = htmlContent.replace('</head>', qlikConfig + '\n  </head>')
  
  // Adicionar script de força de fonte no final do body, ANTES dos scripts existentes
  const fontForceScript = `
    <!-- Script para forçar fonte Roboto -->
    <script>
      // Função para forçar fonte Roboto em todos os elementos
      /*function forceRobotoFont() {
        console.log('🔤 Forçando fonte Roboto em todos os elementos...');
        
        const robotoFamily = "'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif";
        
        // Aplicar Roboto em todos os elementos existentes
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
          if (el.style) {
            el.style.setProperty('font-family', robotoFamily, 'important');
          }
        });
        
        // Aplicar especificamente em elementos SVG (gráficos)
        const svgTexts = document.querySelectorAll('svg text, svg tspan');
        svgTexts.forEach(el => {
          if (el.style) {
            el.style.setProperty('font-family', robotoFamily, 'important');
          }
        });
        
      }*/
      
      // Executar imediatamente
      //forceRobotoFont();
      
      // Executar após o DOM carregar
      //document.addEventListener('DOMContentLoaded', forceRobotoFont);
      
      // Executar periodicamente para capturar elementos criados dinamicamente pelo Qlik
      //setInterval(forceRobotoFont, 2000);
      
      // Observer para novos elementos DOM (criados pelo Qlik dinamicamente)
      /*const observer = new MutationObserver((mutations) => {
        let shouldForceFont = false;
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) { // Element node
                shouldForceFont = true;
              }
            });
          }
        });
        
        if (shouldForceFont) {
          setTimeout(forceRobotoFont, 100);
        }
      });
      
      // Iniciar observação
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });*/
    </script>
    
    `
  
  // Encontrar onde inserir o script (antes do script existente de loading)
  const existingScriptMatch = htmlContent.match(/<!-- Script para remover loading quando React carregar -->/)
  if (existingScriptMatch) {
    htmlContent = htmlContent.replace(
      '<!-- Script para remover loading quando React carregar -->',
      fontForceScript + '<!-- Script para remover loading quando React carregar -->'
    )
  } else {
    // Se não encontrar, inserir antes do </body>
    htmlContent = htmlContent.replace('</body>', fontForceScript + '</body>')
  }
  
  // Criar diretório enterprise se não existir
  if (!fs.existsSync(enterprisePath)) {
    fs.mkdirSync(enterprisePath, { recursive: true })
  }
  
  // Salvar o arquivo modificado
  fs.writeFileSync(enterpriseIndexPath, htmlContent)
  
  // Copiar pasta assets
  const distAssetsPath = path.join(distPath, 'assets')
  const enterpriseAssetsPath = path.join(enterprisePath, 'assets')
  
  if (fs.existsSync(distAssetsPath)) {
    // Remover pasta assets antiga se existir
    if (fs.existsSync(enterpriseAssetsPath)) {
      fs.rmSync(enterpriseAssetsPath, { recursive: true, force: true })
    }
    
    // Copiar nova pasta assets
    fs.cpSync(distAssetsPath, enterpriseAssetsPath, { recursive: true })
    console.log('📁 Assets copiados para mashup-enterprise/')
  }
  
  console.log('✅ Mashup Enterprise construído com sucesso!')
  console.log('')
  console.log('📋 Resumo das configurações aplicadas:')
  console.log('✅ Caminhos dos assets convertidos para relativos')
  console.log('✅ Estilos CSS do Qlik (../../resources/autogenerated/qlik-styles.css)')
  console.log('✅ CSS de sobrescrita de fontes Roboto aplicado')
  console.log('✅ RequireJS configurado (../../resources/assets/external/requirejs/require.js)')
  console.log('✅ Configuração automática do ambiente Enterprise')
  console.log('✅ Script de força de fonte Roboto incluído')
  console.log('✅ Observer para elementos dinâmicos do Qlik')
  console.log('')
  console.log('📁 Para usar no Qlik Sense Enterprise:')
  console.log('1. Copie a pasta "mashup-enterprise" para o diretório de extensões do Qlik')
  console.log('2. Abra o Qlik Sense Desktop ou acesse o QMC do Enterprise')
  console.log('3. O mashup estará disponível na lista de extensões')
  
} catch (error) {
  console.error('❌ Erro ao construir mashup Enterprise:', error)
  process.exit(1)
} 