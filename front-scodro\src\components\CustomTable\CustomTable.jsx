import React, { useState, useRef, useEffect, useMemo } from 'react'
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  Chip,
  Avatar,
  useTheme,
  alpha,
  TableSortLabel,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  CircularProgress
} from '@mui/material'
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon,
  MoreVert as MoreIcon,
  TrendingUp,
  TrendingDown,
  AttachMoney,
  Person,
  Business,
  Assignment,
  CheckCircle,
  Cancel,
  Warning,
  Info
} from '@mui/icons-material'

// Ícones disponíveis para status e tipos
const STATUS_ICONS = {
  success: CheckCircle,
  error: Cancel,
  warning: Warning,
  info: Info,
  active: CheckCircle,
  inactive: Cancel,
  pending: Warning,
  approved: CheckCircle,
  rejected: Cancel,
  processing: Info
}

// Cores para status
const STATUS_COLORS = {
  success: '#4caf50',
  error: '#f44336',
  warning: '#ff9800',
  info: '#2196f3',
  active: '#4caf50',
  inactive: '#9e9e9e',
  pending: '#ff9800',
  approved: '#4caf50',
  rejected: '#f44336',
  processing: '#2196f3'
}

const CustomTable = ({ data, config = {}, objectId }) => {
  const theme = useTheme()
  const containerRef = useRef(null)
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
  const [page, setPage] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(5)
  const [orderBy, setOrderBy] = useState('')
  const [order, setOrder] = useState('asc')
  const [searchTerm, setSearchTerm] = useState('')
  const [filterMenuAnchor, setFilterMenuAnchor] = useState(null)
  const [selectedFilters, setSelectedFilters] = useState({})

  // Configurações padrão para Tabela
  const defaultConfig = {
    showTitle: true,
    showSearch: true,
    showPagination: true,
    showExport: false,
    showFilters: true,
    showRowNumbers: false,
    alternateRowColors: true,
    compactView: false,
    stickyHeader: true,
    maxHeight: 600,
    borderRadius: 1,
    elevation: 1,
    headerBackgroundColor: theme.palette.primary.main,
    headerTextColor: theme.palette.primary.contrastText,
    rowHeight: 'medium', // 'small', 'medium', 'large'
    columnConfig: {}, // Configurações específicas por coluna
    formatters: {}, // Formatadores personalizados por coluna
    enableSorting: true,
    defaultRowsPerPage: 5,
    rowsPerPageOptions: [5, 10, 25, 50]
  }

  const finalConfig = { ...defaultConfig, ...config.tableConfig, ...config }

  // Detectar tamanho do container
  useEffect(() => {
    if (containerRef.current) {
      let resizeTimer
      
      const updateSize = () => {
        const rect = containerRef.current.getBoundingClientRect()
        const newWidth = Math.round(rect.width)
        const newHeight = Math.round(rect.height)
        
        if (Math.abs(newWidth - containerSize.width) > 5 || 
            Math.abs(newHeight - containerSize.height) > 5) {
          setContainerSize({ width: newWidth, height: newHeight })
        }
      }
      
      const handleResize = () => {
        clearTimeout(resizeTimer)
        resizeTimer = setTimeout(updateSize, 50)
      }
      
      updateSize()
      window.addEventListener('resize', handleResize)
      
      const observer = new MutationObserver(() => {
        handleResize()
      })
      
      observer.observe(containerRef.current, {
        attributes: true,
        attributeFilter: ['style'],
        subtree: true
      })
      
      return () => {
        window.removeEventListener('resize', handleResize)
        observer.disconnect()
        clearTimeout(resizeTimer)
      }
    }
  }, [objectId, containerSize.width, containerSize.height])

  // Processar dados da tabela
  const processedData = useMemo(() => {
    if (!data || !data.rows || !data.columns) {
      return { columns: [], rows: [] }
    }

    const columns = data.columns.map((col, index) => ({
      id: col.id || `col_${index}`,
      label: col.label || col.name || `Coluna ${index + 1}`,
      type: col.type || 'text', // text, number, currency, percentage, date, status, avatar
      sortable: col.sortable !== false,
      filterable: col.filterable !== false,
      width: col.width,
      minWidth: col.minWidth || 80,
      align: col.align || (col.type === 'number' || col.type === 'currency' || col.type === 'percentage' ? 'right' : 'left'),
      format: col.format,
      formatter: finalConfig.formatters[col.id] || col.formatter
    }))

    const rows = data.rows.map((row, rowIndex) => {
      const processedRow = { id: row.id || rowIndex }
      
      columns.forEach((col, colIndex) => {
        const cellValue = row[col.id] || row[colIndex] || row.cells?.[colIndex] || ''
        processedRow[col.id] = cellValue
      })
      
      return processedRow
    })

    return { columns, rows }
  }, [data, finalConfig.formatters])

  // Filtrar e ordenar dados
  const filteredAndSortedData = useMemo(() => {
    let filtered = processedData.rows

    // Aplicar busca
    if (searchTerm) {
      filtered = filtered.filter(row =>
        processedData.columns.some(col =>
          String(row[col.id] || '').toLowerCase().includes(searchTerm.toLowerCase())
        )
      )
    }

    // Aplicar filtros específicos
    Object.entries(selectedFilters).forEach(([columnId, filterValue]) => {
      if (filterValue && filterValue !== 'all') {
        filtered = filtered.filter(row => 
          String(row[columnId] || '').toLowerCase().includes(filterValue.toLowerCase())
        )
      }
    })

    // Aplicar ordenação
    if (orderBy) {
      const column = processedData.columns.find(col => col.id === orderBy)
      const isNumeric = column?.type === 'number' || column?.type === 'currency' || column?.type === 'percentage'
      
      filtered.sort((a, b) => {
        const aVal = a[orderBy] || ''
        const bVal = b[orderBy] || ''
        
        if (isNumeric) {
          const numA = parseFloat(String(aVal).replace(/[^\d.-]/g, '')) || 0
          const numB = parseFloat(String(bVal).replace(/[^\d.-]/g, '')) || 0
          return order === 'asc' ? numA - numB : numB - numA
        } else {
          const strA = String(aVal).toLowerCase()
          const strB = String(bVal).toLowerCase()
          return order === 'asc' 
            ? strA.localeCompare(strB, 'pt-BR')
            : strB.localeCompare(strA, 'pt-BR')
        }
      })
    }

    return filtered
  }, [processedData, searchTerm, selectedFilters, orderBy, order])

  // Dados paginados
  const paginatedData = useMemo(() => {
    const startIndex = page * rowsPerPage
    return filteredAndSortedData.slice(startIndex, startIndex + rowsPerPage)
  }, [filteredAndSortedData, page, rowsPerPage])

  // Função para formatar valor da célula
  const formatCellValue = (value, column) => {
    if (value === null || value === undefined || value === '') {
      return '-'
    }

    // Se há formatter personalizado, usar
    if (column.formatter && typeof column.formatter === 'function') {
      return column.formatter(value)
    }

    // ✅ NOVO: Preservar formatação original do Qlik
    if (column.type === 'qlik_formatted') {
      return String(value) // Retornar exatamente como veio do Qlik
    }

    // Formatação baseada no tipo
    switch (column.type) {
      case 'currency':
        const numValue = parseFloat(String(value).replace(/[^\d.-]/g, ''))
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(isNaN(numValue) ? 0 : numValue)
        
      case 'percentage':
        const pctValue = parseFloat(String(value).replace(/[^\d.-]/g, ''))
        return `${isNaN(pctValue) ? 0 : pctValue.toFixed(1)}%`
        
      case 'number':
        const numberValue = parseFloat(String(value).replace(/[^\d.-]/g, ''))
        return new Intl.NumberFormat('pt-BR').format(isNaN(numberValue) ? 0 : numberValue)
        
      case 'date':
        try {
          const date = new Date(value)
          return date.toLocaleDateString('pt-BR')
        } catch {
          return String(value)
        }
        
      default:
        return String(value)
    }
  }

  // Renderizar célula
  const renderCell = (value, column, row) => {
    const formattedValue = formatCellValue(value, column)

    switch (column.type) {
      case 'status':
        const statusKey = String(value).toLowerCase()
        const StatusIcon = STATUS_ICONS[statusKey] || Info
        const statusColor = STATUS_COLORS[statusKey] || theme.palette.grey[500]
        
        return (
          <Chip
            icon={<StatusIcon sx={{ fontSize: '16px !important' }} />}
            label={formattedValue}
            size="small"
            sx={{
              backgroundColor: alpha(statusColor, 0.1),
              color: statusColor,
              borderColor: statusColor,
              '& .MuiChip-icon': {
                color: statusColor
              }
            }}
            variant="outlined"
          />
        )

      case 'avatar':
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar
              sx={{ 
                width: 32, 
                height: 32, 
                backgroundColor: theme.palette.primary.main,
                fontSize: '0.875rem'
              }}
            >
              {String(formattedValue).charAt(0).toUpperCase()}
            </Avatar>
            <Typography variant="body2">
              {formattedValue}
            </Typography>
          </Box>
        )

      case 'trend':
        const trendValue = parseFloat(String(value).replace(/[^\d.-]/g, ''))
        const isPositive = trendValue >= 0
        const TrendIcon = isPositive ? TrendingUp : TrendingDown
        
        return (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <TrendIcon 
              sx={{ 
                fontSize: 16,
                color: isPositive ? theme.palette.success.main : theme.palette.error.main
              }} 
            />
            <Typography 
              variant="body2"
              sx={{ 
                color: isPositive ? theme.palette.success.main : theme.palette.error.main,
                fontWeight: 'medium'
              }}
            >
              {formattedValue}
            </Typography>
          </Box>
        )

      default:
        return (
          <Typography 
            variant="body2" 
            sx={{ 
              textAlign: 'center',
              fontWeight: column.type === 'number' || column.type === 'currency' || column.type === 'qlik_formatted' ? 'medium' : 'normal'
            }}
          >
            {formattedValue}
          </Typography>
        )
    }
  }

  // Handlers
  const handleRequestSort = (columnId) => {
    if (!finalConfig.enableSorting) return
    
    const isAsc = orderBy === columnId && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(columnId)
  }

  const handleChangePage = (event, newPage) => {
    setPage(newPage)
  }

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10))
    setPage(0)
  }

  const handleSearch = (event) => {
    setSearchTerm(event.target.value)
    setPage(0)
  }

  const handleExport = () => {
    // Implementar exportação se necessário
    console.log('Exportar tabela', { data: filteredAndSortedData, columns: processedData.columns })
  }

  // Se não há dados, mostrar loading ou mensagem
  if (!data || !processedData.columns.length) {
    return (
      <Box 
        ref={containerRef}
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          height: 200,
          backgroundColor: theme.palette.background.paper,
          borderRadius: finalConfig.borderRadius,
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        <Box sx={{ textAlign: 'center' }}>
          <CircularProgress size={24} sx={{ mb: 2 }} />
          <Typography variant="body2" color="text.secondary">
            Carregando dados da tabela...
          </Typography>
        </Box>
      </Box>
    )
  }

  return (
    <Box ref={containerRef} sx={{ width: '100%', height: '100%' }}>
      <Paper 
        elevation={finalConfig.elevation}
        sx={{ 
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: finalConfig.borderRadius,
          overflow: 'hidden'
        }}
      >
        {/* Cabeçalho da Tabela */}
        {(finalConfig.showTitle || finalConfig.showSearch || finalConfig.showExport) && (
          <Box sx={{ 
            p: '10px', 
            borderBottom: `1px solid ${theme.palette.divider}`,
            backgroundColor: '#ffffff'
          }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
              {finalConfig.showTitle && (
                <Typography variant="h6" component="h2" sx={{ fontWeight: 'normal' }}>
                  {data.titulo || `Tabela - ${objectId || 'Dados'}`}
                </Typography>
              )}
              
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                {finalConfig.showSearch && (
                  <TextField
                    size="small"
                    placeholder="Buscar..."
                    value={searchTerm}
                    onChange={handleSearch}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon fontSize="small" />
                        </InputAdornment>
                      )
                    }}
                    sx={{ minWidth: 200 }}
                  />
                )}
                
                {finalConfig.showFilters && (
                  <Tooltip title="Filtros">
                    <IconButton
                      size="small"
                      onClick={(e) => setFilterMenuAnchor(e.currentTarget)}
                    >
                      <FilterIcon />
                    </IconButton>
                  </Tooltip>
                )}
                
                {finalConfig.showExport && (
                  <Tooltip title="Exportar">
                    <IconButton size="small" onClick={handleExport}>
                      <ExportIcon />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </Box>
          </Box>
        )}

        {/* Tabela */}
        <TableContainer 
          sx={{ 
            flex: 1,
            maxHeight: finalConfig.maxHeight,
            overflowX: 'auto',
            '& .MuiTable-root': {
              minWidth: 'unset',
              width: '100%'
            },
            '& .MuiTableCell-root': {
              padding: '8px 12px',
              whiteSpace: 'nowrap',
              borderRight: `1px solid ${alpha(theme.palette.divider, 0.4)}`,
              '&:last-child': {
                borderRight: 'none'
              }
            },
            '& .MuiTableHead-root .MuiTableCell-root': {
              borderRight: `1px solid ${alpha(theme.palette.common.white, 0.3)}`,
              textAlign: 'center',
              '&:last-child': {
                borderRight: 'none'
              }
            },
            '& .MuiTableBody-root .MuiTableCell-root': {
              textAlign: 'center'
            }
          }}
        >
          <Table 
            stickyHeader={finalConfig.stickyHeader}
            size={finalConfig.compactView ? 'small' : 'medium'}
          >
            <TableHead>
              <TableRow>
                {finalConfig.showRowNumbers && (
                  <TableCell 
                    sx={{ 
                      backgroundColor: finalConfig.headerBackgroundColor,
                      color: finalConfig.headerTextColor,
                      fontWeight: 'bold',
                      width: 50,
                      minWidth: 50,
                      textAlign: 'center !important'
                    }}
                  >
                    #
                  </TableCell>
                )}
                {processedData.columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align="center"
                    sx={{
                      backgroundColor: finalConfig.headerBackgroundColor,
                      color: finalConfig.headerTextColor,
                      fontWeight: 'bold',
                      width: column.width,
                      minWidth: column.minWidth,
                      cursor: column.sortable && finalConfig.enableSorting ? 'pointer' : 'default',
                      textAlign: 'center !important'
                    }}
                    sortDirection={orderBy === column.id ? order : false}
                  >
                    {column.sortable && finalConfig.enableSorting ? (
                      <TableSortLabel
                        active={orderBy === column.id}
                        direction={orderBy === column.id ? order : 'asc'}
                        onClick={() => handleRequestSort(column.id)}
                        sx={{
                          color: `${finalConfig.headerTextColor} !important`,
                          '& .MuiTableSortLabel-icon': {
                            color: `${finalConfig.headerTextColor} !important`
                          }
                        }}
                      >
                        {column.label}
                      </TableSortLabel>
                    ) : (
                      column.label
                    )}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedData.map((row, index) => (
                <TableRow
                  key={row.id}
                  sx={{
                    backgroundColor: finalConfig.alternateRowColors && index % 2 === 1 
                      ? alpha(theme.palette.primary.main, 0.02)
                      : 'transparent',
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.primary.main, 0.05)
                    }
                  }}
                >
                  {finalConfig.showRowNumbers && (
                    <TableCell sx={{ 
                      color: theme.palette.text.secondary, 
                      fontSize: '0.875rem',
                      textAlign: 'center !important'
                    }}>
                      {page * rowsPerPage + index + 1}
                    </TableCell>
                  )}
                  {processedData.columns.map((column) => (
                    <TableCell 
                      key={column.id} 
                      align="center"
                      sx={{
                        textAlign: 'center !important'
                      }}
                    >
                      {renderCell(row[column.id], column, row)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
              
              {paginatedData.length === 0 && (
                <TableRow>
                  <TableCell 
                    colSpan={processedData.columns.length + (finalConfig.showRowNumbers ? 1 : 0)}
                    sx={{ textAlign: 'center', py: 4 }}
                  >
                    <Typography variant="body2" color="text.secondary">
                      {searchTerm || Object.values(selectedFilters).some(v => v) 
                        ? 'Nenhum resultado encontrado para os filtros aplicados'
                        : 'Nenhum dado disponível'
                      }
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Paginação */}
        {finalConfig.showPagination && filteredAndSortedData.length > 0 && (
          <TablePagination
            component="div"
            count={filteredAndSortedData.length}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={finalConfig.rowsPerPageOptions}
            labelRowsPerPage="Linhas por página:"
            labelDisplayedRows={({ from, to, count }) => 
              `${from}-${to} de ${count !== -1 ? count : `mais de ${to}`}`
            }
            sx={{
              borderTop: `1px solid ${theme.palette.divider}`,
              backgroundColor: '#ffffff'
            }}
          />
        )}

        {/* Menu de Filtros */}
        <Menu
          anchorEl={filterMenuAnchor}
          open={Boolean(filterMenuAnchor)}
          onClose={() => setFilterMenuAnchor(null)}
        >
          <MenuItem onClick={() => setFilterMenuAnchor(null)}>
            <Typography variant="body2">
              Em breve: Filtros avançados
            </Typography>
          </MenuItem>
        </Menu>
      </Paper>
    </Box>
  )
}

export default CustomTable 