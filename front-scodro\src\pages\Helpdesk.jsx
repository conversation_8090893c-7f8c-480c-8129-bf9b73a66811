import React, { useState } from 'react'
import { <PERSON>, Typography, Alert, Switch, FormControlLabel, Tooltip } from '@mui/material'
import { Edit as EditIcon, Visibility as VisibilityIcon } from '@mui/icons-material'
import GridLayout from '@components/GridLayout/GridLayout'
import { useEmpresa } from '@context/EmpresaContext'

const Helpdesk = () => {
  const { empresaSelecionada, empresaAtual, loading, error } = useEmpresa()
  const [editMode, setEditMode] = useState(process.env.NODE_ENV === 'development' ? false : false)

  // Obter appId de helpdesk da empresa atual
  const getAppId = () => {
    if (!empresaAtual) return null
    
    console.log('🔍 Helpdesk - Procurando AppId...')
    
    // Se tem apps como array
    if (Array.isArray(empresaAtual.apps)) {
      console.log('📂 Helpdesk - Apps é um array:', empresaAtual.apps)
      
      // Procurar app do tipo helpdesk ou com nome helpdesk
      const helpdeskApp = empresaAtual.apps.find(app => 
        app.nome?.toLowerCase().includes('helpdesk') || 
        app.nome?.toLowerCase().includes('suporte') ||
        app.nome?.toLowerCase().includes('support') ||
        app.categoria === 'helpdesk'
      )
      
      if (helpdeskApp && helpdeskApp._id) {
        console.log('✅ Helpdesk - App específico encontrado:', helpdeskApp)
        return helpdeskApp._id // Retornar _id para o backend
      }
      
      // Se não encontrou específico, usar o primeiro app
      if (empresaAtual.apps.length > 0 && empresaAtual.apps[0]._id) {
        console.log('🎯 Helpdesk - Usando primeiro app disponível:', empresaAtual.apps[0])
        return empresaAtual.apps[0]._id // Retornar _id para o backend
      }
    }
    
    // Se apps é um objeto (formato antigo)
    if (empresaAtual.apps && typeof empresaAtual.apps === 'object') {
      console.log('📁 Helpdesk - Apps é um objeto:', empresaAtual.apps)
      
      // Tentar pegar helpdesk primeiro
      if (empresaAtual.apps.helpdesk) {
        return empresaAtual.apps.helpdesk
      }
      
      // Pegar qualquer app disponível
      const appIds = Object.values(empresaAtual.apps).filter(Boolean)
      if (appIds.length > 0) {
        return appIds[0]
      }
    }
    
    // Último fallback: usar ID genérico
    console.log('🔄 Helpdesk - Nenhum app específico, usando fallback genérico')
    return 'helpdesk-' + empresaSelecionada
  }

  const appId = getAppId()

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Carregando dados da empresa...
        </Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mt: 2 }}>
          Erro ao carregar dados: {error}
        </Alert>
      </Box>
    )
  }

  if (!empresaSelecionada || !empresaAtual) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="warning" sx={{ mt: 2 }}>
          Nenhuma empresa selecionada. Selecione uma empresa no menu lateral.
        </Alert>
      </Box>
    )
  }

  if (!appId) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info" sx={{ mt: 2 }}>
          Nenhum app de helpdesk configurado para esta empresa. Configure os apps na página de Configurações.
        </Alert>
      </Box>
    )
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toggle de Habilitação de Edição - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ 
          p: 2, 
          pb: 1,
          display: 'flex', 
          justifyContent: 'flex-end', 
          alignItems: 'center',
          minHeight: '60px'
        }}>
          <Tooltip title={editMode ? "Desabilitar modo de edição" : "Habilitar modo de edição"}>
            <FormControlLabel
              control={
                <Switch
                  checked={editMode}
                  onChange={(e) => setEditMode(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {editMode ? <EditIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
                  <Typography variant="body2">
                    {editMode ? 'Editando' : 'Visualização'}
                  </Typography>
                </Box>
              }
              sx={{ m: 0 }}
            />
          </Tooltip>
        </Box>
      )}

      {/* Aviso sobre modo de edição quando ativo - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && editMode && (
        <Alert 
          severity="info" 
          sx={{ 
            mx: 2, 
            mt: 1, 
            borderRadius: 2,
            '& .MuiAlert-message': { py: 0.5 }
          }}
        >
          <Typography variant="body2">
            <strong>✏️ Modo de Edição Ativo:</strong> Você pode adicionar, mover, redimensionar e remover objetos.
          </Typography>
        </Alert>
      )}

      {/* Grid Layout Dinâmico - ocupa o espaço restante */}
      <Box sx={{ flex: 1, p: 2, pt: 1 }}>
        <GridLayout 
          pagina="helpdesk" 
          appId={appId}
          editMode={process.env.NODE_ENV === 'development' ? editMode : false}
        />
      </Box>
    </Box>
  )
}

export default Helpdesk 