# 🚀 Qlik Sense Mashup React

Mashup React moderno para Qlik Sense, compatível tanto com **Qlik Cloud** quanto com **Qlik Enterprise**, usando as tecnologias front-end mais modernas.

## 📋 Características

- ✅ **React 18** com hooks e functional components
- ✅ **Vite** para build rápido e desenvolvimento
- ✅ **Material-UI** para interface moderna
- ✅ **Detecção automática** de ambiente (Cloud/Enterprise/Development)
- ✅ **Configuração centralizada** de IDs de apps e objetos
- ✅ **Layout responsivo** e mobile-friendly
- ✅ **Modo desenvolvimento** com placeholders visuais
- ✅ **TypeScript ready** (configuração incluída)

## 🏗️ Estrutura do Projeto

```
qlik-sense-mashup-react/
├── src/
│   ├── config/
│   │   ├── apps.js          # IDs dos apps e objetos
│   │   └── qlik.js          # Configurações de conexão
│   ├── components/
│   │   ├── QlikObject.jsx   # Componente para objetos Qlik
│   │   └── Layout.jsx       # Layout principal
│   ├── services/
│   │   └── qlik.js          # Serviço de conexão Qlik
│   ├── pages/
│   │   ├── Dashboard.jsx    # Página principal
│   │   ├── Vendas.jsx       # Página de vendas
│   │   └── Helpdesk.jsx     # Página de helpdesk
│   ├── qext/
│   │   └── helpdesk.qext    # Arquivo QEXT para Enterprise
│   ├── App.jsx              # Componente principal
│   └── main.jsx             # Entry point
├── package.json
├── vite.config.js
└── README.md
```

## 🚀 Instalação e Configuração

### 1. Instalar dependências

```bash
npm install
```

### 2. Configurar IDs dos Apps e Objetos

Edite o arquivo `src/config/apps.js`:

```javascript
export const APP_IDS = {
  dashboard: 'SEU_APP_DASHBOARD_ID',
  vendas: 'SEU_APP_VENDAS_ID',
  helpdesk: 'SEU_APP_HELPDESK_ID'
}

export const OBJECT_IDS = {
  kpi1: 'SEU_OBJETO_KPI_1',
  chart1: 'SEU_OBJETO_CHART_1',
  // ... outros objetos
}
```

### 3. Configurar Conexão Qlik

#### Para Qlik Cloud:
Edite `src/config/qlik.js`:

```javascript
export const cloudConfig = {
  host: 'your-tenant.us.qlikcloud.com',
  webIntegrationId: 'SEU_WEB_INTEGRATION_ID'
}
```

#### Para Qlik Enterprise:
A configuração é automática baseada na URL atual.

## 🛠️ Scripts Disponíveis

### Desenvolvimento
```bash
npm run dev
```
- Inicia servidor local na porta 3000
- Não conecta ao Qlik (exibe placeholders)
- Hot reload ativado

### Build para Produção

#### Qlik Cloud:
```bash
npm run build:cloud
```
- Gera bundle otimizado para Qlik Cloud
- Inclui configuração de Web Integration ID

#### Qlik Enterprise:
```bash
npm run build:enterprise
```
- Gera bundle otimizado para Qlik Enterprise
- Copia arquivo QEXT para a pasta dist/

### Outros Scripts:
```bash
npm run preview    # Preview do build
npm run lint       # Verificar código
npm run lint:fix   # Corrigir problemas de lint
```

## 🌐 Ambientes e Detecção

O mashup detecta automaticamente o ambiente:

### 🔧 Development
- URL contém `localhost` ou porta de desenvolvimento
- Exibe placeholders no lugar dos objetos Qlik
- Console logs detalhados

### ☁️ Qlik Cloud
- Detectado pela presença de `webIntegrationId` configurado
- Usa configuração de Cloud com HTTPS

### 🏢 Qlik Enterprise
- Detectado pela presença de `/extensions` na URL
- Extrai configuração automaticamente da URL atual

## 📱 Páginas Disponíveis

- **Dashboard** (`/`) - Visão geral com KPIs e gráficos principais
- **Vendas** (`/vendas`) - Análise detalhada de vendas
- **Helpdesk** (`/helpdesk`) - Dashboard de tickets de suporte
- **Configurações** (`/configuracoes`) - Página de configurações
- **Sobre** (`/sobre`) - Informações sobre o mashup

## 🎨 Personalização

### Tema e Cores
Edite `src/App.jsx` para personalizar o tema Material-UI:

```javascript
const theme = createTheme({
  palette: {
    primary: {
      main: '#667eea',  // Cor principal
      dark: '#764ba2'   // Cor escura
    }
  }
})
```

### Adicionar Nova Página
1. Crie o componente em `src/pages/`
2. Adicione a rota em `src/App.jsx`
3. Adicione item no menu em `src/components/Layout.jsx`

### Adicionar Novo Objeto Qlik
1. Adicione o ID em `src/config/apps.js`
2. Use o componente `<QlikObject>` na página desejada:

```jsx
<QlikObject
  objectId={OBJECT_IDS.meuObjeto}
  appId={APP_IDS.meuApp}
  height="400px"
/>
```

## 🚀 Deploy

### Qlik Cloud
1. Execute `npm run build:cloud`
2. Faça upload dos arquivos da pasta `dist/` para seu servidor web
3. Configure o Web Integration ID no Qlik Cloud Management Console

### Qlik Enterprise
1. Execute `npm run build:enterprise`
2. Copie a pasta `dist/` para `[QlikSense]/Extensions/helpdesk/`
3. O mashup estará disponível em `/extensions/helpdesk/`

## 🔧 Troubleshooting

### Objetos não carregam
- Verifique se os IDs em `src/config/apps.js` estão corretos
- Confirme se o app está publicado (Enterprise) ou compartilhado (Cloud)
- Verifique o console do navegador para erros

### Erro de CORS (Cloud)
- Confirme se o Web Integration ID está configurado corretamente
- Verifique se o domínio está autorizado no Qlik Cloud

### Erro 404 (Enterprise)
- Confirme se os arquivos estão na pasta correta de extensions
- Verifique se o arquivo QEXT está presente

## 📚 Recursos Adicionais

- [Documentação Qlik Sense Mashup API](https://help.qlik.com/en-US/sense-developer/November2023/Subsystems/Mashups/Content/Sense_Mashups/mashups-introduction.htm)
- [Qlik Cloud Web Integration](https://help.qlik.com/en-US/cloud-services/Subsystems/Hub/Content/Sense_Hub/Admin/mc-administer-web-integrations.htm)
- [Material-UI Documentation](https://mui.com/)
- [Vite Documentation](https://vitejs.dev/)

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.

---

**Desenvolvido com ❤️ para a comunidade Qlik** 