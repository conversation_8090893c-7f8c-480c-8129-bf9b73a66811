# CustomTable Component

Componente de tabela personalizado para o projeto SCODRO, desenvolvido para exibir elementos do tipo 'table' que não são suportados nativamente pelo eCharts.

## 🚀 Características

- **Responsivo**: Adapta-se automaticamente ao tamanho da tela
- **Busca integrada**: Procura em todas as colunas simultaneamente
- **Ordenação**: Clique nos cabeçalhos para ordenar ascendente/descendente
- **Paginação**: Controle de páginas com opções configuráveis
- **Filtros**: Sistema de filtros avançados (em desenvolvimento)
- **Tipos de coluna**: Suporte para diferentes tipos de dados
- **Exportação**: Opção de exportar dados (configurável)
- **Temas**: Integração completa com Material-UI

## 📊 Tipos de Coluna Suportados

### Tipos Básicos
- `text`: Texto simples
- `number`: Números formatados (locale pt-BR)
- `currency`: Valores monetários (R$)
- `percentage`: Porcentagens (%)
- `date`: Datas formatadas (dd/mm/aaaa)

### Tipos Especiais
- `status`: Chips coloridos com ícones de status
- `avatar`: Avatar circular com inicial + nome
- `trend`: Indicadores de tendência com ícones e cores

## 🎨 Configurações Disponíveis

### Configurações de Exibição
```javascript
tableConfig: {
  showTitle: true,          // Mostrar título da tabela
  showSearch: true,         // Barra de busca
  showPagination: true,     // Controles de paginação
  showFilters: true,        // Botão de filtros
  showExport: false,        // Botão de exportação
}
```

### Configurações de Layout
```javascript
tableConfig: {
  showRowNumbers: false,    // Números das linhas
  alternateRowColors: true, // Cores alternadas nas linhas
  compactView: false,       // Visualização compacta
  stickyHeader: true,       // Cabeçalho fixo
  enableSorting: true,      // Permitir ordenação
}
```

### Configurações Visuais
```javascript
tableConfig: {
  maxHeight: 600,                        // Altura máxima (px)
  borderRadius: 1,                       // Raio da borda
  elevation: 1,                          // Elevação (sombra)
  headerBackgroundColor: '#1976d2',      // Cor do cabeçalho
  headerTextColor: '#ffffff',            // Cor do texto do cabeçalho
}
```

### Configurações de Paginação
```javascript
tableConfig: {
  defaultRowsPerPage: 5,           // Linhas por página (padrão)
  rowsPerPageOptions: [5, 10, 25, 50]  // Opções de linhas por página
}
```

## 📝 Estrutura de Dados

### Formato de Entrada
```javascript
const data = {
  tipo: 'table',
  titulo: 'Minha Tabela',
  columns: [
    {
      id: 'nome',
      label: 'Nome',
      type: 'text',
      sortable: true,
      filterable: true,
      minWidth: 150,
      align: 'left'
    },
    {
      id: 'valor',
      label: 'Valor',
      type: 'currency',
      sortable: true,
      align: 'right',
      minWidth: 100
    },
    {
      id: 'status',
      label: 'Status',
      type: 'status',
      sortable: false,
      minWidth: 120
    }
  ],
  rows: [
    {
      id: 1,
      nome: 'João Silva',
      valor: 15000,
      status: 'active'
    },
    // ... mais linhas
  ]
}
```

### Formatadores Personalizados
```javascript
const config = {
  tableConfig: {
    formatters: {
      customColumn: (value) => {
        return `Customizado: ${value}`
      }
    }
  }
}
```

## 🎯 Valores de Status Suportados

### Status com Ícones
- `success`, `approved` → Verde com ✓
- `error`, `rejected` → Vermelho com ✗  
- `warning`, `pending` → Amarelo com ⚠️
- `info`, `processing` → Azul com ℹ️
- `active` → Verde com ✓
- `inactive` → Cinza com ✗

## 🔧 Uso no Projeto

### 1. Integração no QlikObject
O componente é automaticamente usado quando `config.tipo === 'table'`:

```javascript
import CustomTable from '../CustomTable/CustomTable'

// No QlikObject.jsx
if (objectData.tipo === 'table') {
  return (
    <CustomTable 
      data={objectData}
      config={config}
      objectId={objectId}
    />
  )
}
```

### 2. Dados Simulados (Desenvolvimento)
Em modo de desenvolvimento, são gerados dados simulados com:
- 25 linhas de exemplo
- 9 colunas com diferentes tipos
- Dados aleatórios realistas

### 3. Configuração via Interface
Use a página de Configurações para ajustar:
- Aparência da tabela
- Comportamento da paginação
- Cores e estilos
- Funcionalidades ativas

## 🚧 Funcionalidades em Desenvolvimento

- [ ] Filtros avançados por coluna
- [ ] Exportação para CSV/Excel
- [ ] Seleção múltipla de linhas
- [ ] Agrupamento de colunas
- [ ] Colunas redimensionáveis
- [ ] Ordenação por múltiplas colunas

## 🐛 Solução de Problemas

### Tabela não aparece
- Verifique se `data.tipo === 'table'`
- Confirme se há dados em `data.columns` e `data.rows`

### Ordenação não funciona
- Verifique se `enableSorting: true` na configuração
- Confirme se a coluna tem `sortable: true`

### Busca não encontra resultados
- A busca é case-insensitive e procura em todas as colunas
- Valores null/undefined são tratados como strings vazias

## 📋 Exemplo Completo

```javascript
const tableData = {
  tipo: 'table',
  titulo: 'Relatório de Vendas',
  columns: [
    { id: 'id', label: 'ID', type: 'number', width: 80 },
    { id: 'vendedor', label: 'Vendedor', type: 'avatar', minWidth: 180 },
    { id: 'valor', label: 'Valor', type: 'currency', align: 'right' },
    { id: 'meta', label: 'Meta (%)', type: 'percentage', align: 'right' },
    { id: 'status', label: 'Status', type: 'status' },
    { id: 'tendencia', label: 'Tendência', type: 'trend', align: 'center' }
  ],
  rows: [
    {
      id: 1,
      vendedor: 'João Silva',
      valor: 25000,
      meta: 85.5,
      status: 'success',
      tendencia: '+12.5'
    }
  ]
}

const tableConfig = {
  tableConfig: {
    showTitle: true,
    showSearch: true,
    showPagination: true,
    alternateRowColors: true,
    defaultRowsPerPage: 10,
    headerBackgroundColor: '#1976d2',
    headerTextColor: '#ffffff'
  }
}

// Uso
<CustomTable 
  data={tableData} 
  config={tableConfig}
  objectId="minha-tabela"
/>
```

## 🎨 Customização Avançada

### CSS Personalizado
Use o sistema de temas do Material-UI para personalizar:

```javascript
const theme = createTheme({
  components: {
    MuiTableHead: {
      styleOverrides: {
        root: {
          '& .MuiTableCell-head': {
            backgroundColor: '#custom-color',
            color: '#custom-text-color'
          }
        }
      }
    }
  }
})
```

### Componentes de Célula Personalizados
Extenda o método `renderCell` para tipos personalizados:

```javascript
// Em desenvolvimento - permitirá componentes personalizados por tipo
```

---

**Desenvolvido para o projeto SCODRO** | **Versão**: 1.0.0 | **Última atualização**: Dezembro 2024 