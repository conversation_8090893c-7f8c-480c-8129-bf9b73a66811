import React, { useState, useRef, useEffect } from 'react'
import { Box, Typography, Card, CardContent, useTheme, Paper, Chip, TextField, InputAdornment, Dialog, DialogTitle, DialogContent, DialogActions, Button, List, ListItem, ListItemText, Divider } from '@mui/material'
import { FilterList, Clear, Search, Check, Close } from '@mui/icons-material'
import qlikService from '@services/qlik'
import { useQlikSelection } from '@contexts/QlikSelectionContext'

const CustomFilter = ({ data, config = {}, objectId }) => {
  const theme = useTheme()
  const containerRef = useRef(null)
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
  const [searchText, setSearchText] = useState('')
  const [qlikApp, setQlikApp] = useState(null)
  const [fieldName, setFieldName] = useState(null)
  
  // ✅ NOVO: Estados para modal de confirmação (filterpane)
  const [pendingSelections, setPendingSelections] = useState(new Set())
  const [confirmationModalOpen, setConfirmationModalOpen] = useState(false)
  
  // ✅ NOVO: Detectar se é filterpane
  const isFilterpane = config?.objectType === 'filterpane' || 
                      data?.debug?.source === 'filterpane' ||
                      objectId?.toLowerCase().includes('filterpane')
  
  // ✅ Usar Context em vez de localStorage
  const { 
    getFieldSelections, 
    updateFieldSelection, 
    clearFieldSelections,
    selectionVersion 
  } = useQlikSelection()
  
  // ✅ Para filterpane: usar seleções pendentes; para outros: usar seleções do Context
  const localSelections = isFilterpane ? pendingSelections : getFieldSelections(fieldName || objectId)
  
  // ✅ DEBUG: Log inicial
  
  // Configurações padrão para filtros
  const defaultConfig = {
    showTitle: true,
    showSearchBox: true,
    showClearButton: true,
    allowMultiSelect: true,
    backgroundColor: 'transparent',
    borderRadius: '8px',
    maxHeight: '400px', // ✅ AUMENTADO: Altura máxima maior
    compact: false
  }
  
  const finalConfig = { ...defaultConfig, ...config.filterConfig, ...config }

  

  // ✅ NOVO: Inicializar conexão com Qlik
  useEffect(() => {
    const initializeQlik = async () => {
      try {
        if (config.appId) {
          const app = await qlikService.openApp(config.appId)
          setQlikApp(app)
          
          // Tentar deduzir o nome do campo baseado no título
          const possibleFieldNames = [
            'Empreendimento',
            'Empreendimentos', 
            'Nome Empreendimento',
            'Desc Empreendimento',
            'Descrição Empreendimento'
          ]
          
          // Buscar campo correspondente
          app.getList('FieldList', (fieldListReply) => {
            let matchedField = null
            
            for (const possibleName of possibleFieldNames) {
              matchedField = fieldListReply?.qFieldList?.qItems?.find(field => 
                field.qName.toLowerCase() === possibleName.toLowerCase()
              )
              if (matchedField) break
            }
            
            if (!matchedField) {
              matchedField = fieldListReply?.qFieldList?.qItems?.find(field => 
                field.qName.toLowerCase().includes('empreendimento') ||
                field.qName.toLowerCase().includes('empreend')
              )
            }
            
            if (matchedField) {
              setFieldName(matchedField.qName)
              console.log(`🔍 DEBUG_FILTER_FIELD_DETECTED:`, {
                fieldName: matchedField.qName,
                cardinal: matchedField.qCardinal
              })
            }
          })
        }
      } catch (error) {
        console.error('Erro ao inicializar Qlik:', error)
      }
    }
    
    initializeQlik()
  }, [config.appId])

  // ✅ NOVO: Inicializar seleções pendentes para filterpane
  useEffect(() => {
    if (isFilterpane && fieldName) {
      const contextSelections = getFieldSelections(fieldName)
      setPendingSelections(new Set(contextSelections))
      console.log(`🔍 DEBUG_FILTER_PENDING_INITIALIZED:`, JSON.stringify({
        objectId: objectId || 'undefined',
        fieldName: fieldName,
        initialPendingSelections: Array.from(contextSelections),
        isFilterpane: true
      }, null, 2))
    }
  }, [isFilterpane, fieldName, getFieldSelections])

  // Detectar tamanho do container
  useEffect(() => {
    console.log(`🔍 DEBUG_FILTER_EFFECT_MOUNT:`, JSON.stringify({
      objectId: objectId || 'undefined',
      hasContainerRef: !!containerRef.current,
      effectTriggered: true
    }, null, 2))

    if (containerRef.current) {
      const updateSize = () => {
        const rect = containerRef.current.getBoundingClientRect()
        console.log(`🔍 DEBUG_FILTER_SIZE_UPDATE:`, JSON.stringify({
          objectId: objectId || 'undefined',
          width: rect.width,
          height: rect.height,
          sizeUpdateTriggered: true
        }, null, 2))
        setContainerSize({ width: rect.width, height: rect.height })
      }
      
      updateSize()
      window.addEventListener('resize', updateSize)
      return () => window.removeEventListener('resize', updateSize)
    }
  }, [objectId])

  // ✅ NOVO: Função para aplicar seleções pendentes (apenas filterpane)
  const applyPendingSelections = async () => {
    if (!isFilterpane || !qlikApp || !fieldName) {
      console.log(`🖱️ DEBUG_FILTER_APPLY_SKIPPED:`, JSON.stringify({
        objectId: objectId || 'undefined',
        isFilterpane: isFilterpane,
        hasQlikApp: !!qlikApp,
        hasFieldName: !!fieldName,
        reason: 'Condições não atendidas para aplicar seleções'
      }, null, 2))
      return
    }

    console.log(`🖱️ DEBUG_FILTER_APPLY_START:`, JSON.stringify({
      objectId: objectId || 'undefined',
      pendingSelections: Array.from(pendingSelections),
      fieldName: fieldName,
      timestamp: new Date().toISOString()
    }, null, 2))

    try {
      let selectionSuccess = false
      
      // Usar field() para aplicar seleções
      if (typeof qlikApp.field === 'function') {
        try {
          const field = await qlikApp.field(fieldName)
          if (field && typeof field.selectValues === 'function') {
            if (pendingSelections.size === 0) {
              await field.clear()
            } else {
              const values = Array.from(pendingSelections).map(value => ({ qText: value }))
              await field.selectValues(values, false, false) // false, false = replace selection
            }
            selectionSuccess = true
            console.log(`🖱️ DEBUG_FILTER_APPLY_SUCCESS:`, JSON.stringify({
              objectId: objectId || 'undefined',
              appliedSelections: Array.from(pendingSelections),
              method: 'field().selectValues()'
            }, null, 2))
          }
        } catch (error) {
          console.log(`🖱️ DEBUG_FILTER_APPLY_ERROR:`, JSON.stringify({
            objectId: objectId || 'undefined',
            error: error.message,
            method: 'field().selectValues()'
          }, null, 2))
        }
      }

      if (selectionSuccess) {
        // Atualizar Context com as seleções aplicadas
        updateFieldSelection(fieldName, pendingSelections, objectId)
        
        // Fechar modal
        setConfirmationModalOpen(false)
        
        console.log(`🖱️ DEBUG_FILTER_APPLY_COMPLETED:`, JSON.stringify({
          objectId: objectId || 'undefined',
          appliedSelections: Array.from(pendingSelections),
          contextUpdated: true,
          modalClosed: true
        }, null, 2))
      } else {
        console.log(`🖱️ DEBUG_FILTER_APPLY_FAILED:`, JSON.stringify({
          objectId: objectId || 'undefined',
          reason: 'Nenhum método de seleção funcionou'
        }, null, 2))
      }
      
    } catch (error) {
      console.log(`🖱️ DEBUG_FILTER_APPLY_EXCEPTION:`, JSON.stringify({
        objectId: objectId || 'undefined',
        error: error.message,
        errorStack: error.stack
      }, null, 2))
    }
  }

  // ✅ NOVO: Função para cancelar seleções pendentes
  const cancelPendingSelections = () => {
    console.log(`🖱️ DEBUG_FILTER_CANCEL_PENDING:`, JSON.stringify({
      objectId: objectId || 'undefined',
      cancelledSelections: Array.from(pendingSelections),
      action: 'cancel_pending_selections'
    }, null, 2))
    
    // Reverter para seleções do Context
    const contextSelections = getFieldSelections(fieldName || objectId)
    setPendingSelections(new Set(contextSelections))
    setConfirmationModalOpen(false)
  }

  // ✅ MODIFICADO: Função para lidar com cliques nos itens
  const handleItemClick = async (item) => {
    console.log(`🖱️ DEBUG_FILTER_CLICK_START:`, JSON.stringify({
      objectId: objectId || 'undefined',
      clickedItem: item,
      itemValue: item?.value,
      itemSelected: item?.selected,
      isFilterpane: isFilterpane,
      hasQlikApp: !!qlikApp,
      hasFieldName: !!fieldName,
      fieldName: fieldName,
      timestamp: new Date().toISOString()
    }, null, 2))
    
    if (isFilterpane) {
      // ✅ FILTERPANE: Apenas atualizar seleções pendentes (não aplicar no Qlik)
      const newPendingSelections = new Set(pendingSelections)
      
      if (item.selected || pendingSelections.has(item.value)) {
        newPendingSelections.delete(item.value)
        console.log(`🖱️ DEBUG_FILTER_PENDING_DESELECTED:`, JSON.stringify({
          objectId: objectId || 'undefined',
          itemValue: item.value,
          action: 'removed from pending selections'
        }, null, 2))
      } else {
        newPendingSelections.add(item.value)
        console.log(`🖱️ DEBUG_FILTER_PENDING_SELECTED:`, JSON.stringify({
          objectId: objectId || 'undefined',
          itemValue: item.value,
          action: 'added to pending selections'
        }, null, 2))
      }
      
      setPendingSelections(newPendingSelections)
      
      // Não aplicar no Qlik ainda - apenas atualizar UI
      console.log(`🖱️ DEBUG_FILTER_PENDING_UPDATED:`, JSON.stringify({
        objectId: objectId || 'undefined',
        pendingSelections: Array.from(newPendingSelections),
        message: 'Seleções pendentes atualizadas, aguardando confirmação'
      }, null, 2))
      
    } else {
      // ✅ FILTROS NORMAIS: Comportamento original (aplicar imediatamente)
      if (!qlikApp || !fieldName) {
        console.log(`🖱️ DEBUG_FILTER_CLICK_NO_APP_OR_FIELD:`, JSON.stringify({
          objectId: objectId || 'undefined',
          hasQlikApp: !!qlikApp,
          hasFieldName: !!fieldName,
          fieldName: fieldName,
          reason: 'Qlik app ou campo não disponível para seleção',
          clickAborted: true
        }, null, 2))
        return
      }
      
      // Atualizar Context IMEDIATAMENTE (antes da seleção Qlik)
      const newSelections = new Set(localSelections)
      if (item.selected) {
        newSelections.delete(item.value)
        console.log(`🖱️ DEBUG_FILTER_CLICK_CONTEXT_DESELECTED:`, JSON.stringify({
          objectId: objectId || 'undefined',
          itemValue: item.value,
          action: 'removed from context immediately'
        }, null, 2))
      } else {
        newSelections.add(item.value)
        console.log(`🖱️ DEBUG_FILTER_CLICK_CONTEXT_SELECTED:`, JSON.stringify({
          objectId: objectId || 'undefined',
          itemValue: item.value,
          action: 'added to context immediately'
        }, null, 2))
      }
      
      // Atualizar Context instantaneamente
      updateFieldSelection(fieldName, newSelections, objectId)
      
      // Aplicar no Qlik em background
      try {
        let field = null
        let selectionSuccess = false
        
        if (!selectionSuccess && typeof qlikApp.field === 'function') {
          try {
            field = await qlikApp.field(fieldName)
            if (field && typeof field.selectValues === 'function') {
              if (item.selected) {
                await field.clear()
              } else {
                await field.selectValues([{qText: item.value}], true, false)
              }
              selectionSuccess = true
              console.log(`🖱️ DEBUG_FILTER_CLICK_QLIK_SUCCESS:`, JSON.stringify({
                objectId: objectId || 'undefined',
                method: 'field()',
                result: 'success'
              }, null, 2))
            }
          } catch (error) {
            console.log(`🖱️ DEBUG_FILTER_CLICK_QLIK_ERROR:`, JSON.stringify({
              objectId: objectId || 'undefined',
              method: 'field()',
              error: error.message
            }, null, 2))
          }
        }
        
      } catch (error) {
        console.log(`🖱️ DEBUG_FILTER_CLICK_ERROR:`, JSON.stringify({
          objectId: objectId || 'undefined',
          itemValue: item.value,
          fieldName: fieldName,
          error: error.message,
          contextStillUpdated: true
        }, null, 2))
      }
    }
  }

  // ✅ MODIFICADO: Função para limpar todas as seleções
  const handleClearAll = async () => {
    console.log(`🖱️ DEBUG_FILTER_CLICK_CLEAR_ALL_START:`, JSON.stringify({
      objectId: objectId || 'undefined',
      isFilterpane: isFilterpane,
      hasQlikApp: !!qlikApp,
      hasFieldName: !!fieldName,
      fieldName: fieldName,
      action: 'clear_all',
      timestamp: new Date().toISOString()
    }, null, 2))
    
    if (isFilterpane) {
      // ✅ FILTERPANE: Apenas limpar seleções pendentes
      setPendingSelections(new Set())
      console.log(`🖱️ DEBUG_FILTER_PENDING_CLEARED:`, JSON.stringify({
        objectId: objectId || 'undefined',
        action: 'pending_selections_cleared',
        message: 'Seleções pendentes limpas, aguardando confirmação'
      }, null, 2))
    } else {
      // ✅ FILTROS NORMAIS: Limpar Context imediatamente
      clearFieldSelections(fieldName, objectId)
      
      if (!qlikApp || !fieldName) {
        console.log(`🖱️ DEBUG_FILTER_CLICK_CLEAR_CONTEXT_ONLY:`, JSON.stringify({
          objectId: objectId || 'undefined',
          reason: 'Sem Qlik app, apenas Context limpo',
          contextCleared: true
        }, null, 2))
        return
      }
      
      // Aplicar limpeza no Qlik em background
      try {
        let clearSuccess = false
        
        if (!clearSuccess && typeof qlikApp.field === 'function') {
          try {
            const field = await qlikApp.field(fieldName)
            if (field && typeof field.clear === 'function') {
              await field.clear()
              clearSuccess = true
              console.log(`🖱️ DEBUG_FILTER_CLICK_CLEAR_SUCCESS:`, JSON.stringify({
                objectId: objectId || 'undefined',
                method: 'field().clear()',
                result: 'success'
              }, null, 2))
            }
          } catch (error) {
            console.log(`🖱️ DEBUG_FILTER_CLICK_CLEAR_ERROR:`, JSON.stringify({
              objectId: objectId || 'undefined',
              method: 'field().clear()',
              error: error.message
            }, null, 2))
          }
        }
        
      } catch (error) {
        console.log(`🖱️ DEBUG_FILTER_CLICK_CLEAR_EXCEPTION:`, JSON.stringify({
          objectId: objectId || 'undefined',
          fieldName: fieldName,
          error: error.message,
          contextStillCleared: true
        }, null, 2))
      }
    }
  }

  // Processar dados do Qlik
  const processData = () => {
    console.log(`🔍 DEBUG_FILTER_PROCESS_DATA_START:`, JSON.stringify({ 
      objectId: objectId || 'undefined',
      hasData: !!data,
      dataType: typeof data,
      dataContent: data,
      hasConfig: !!config,
      configContent: config,
      finalConfigContent: finalConfig,
      processDataCalled: true
    }, null, 2))
    
    // Novo formato: dados já processados
    if (data && data.tipo === 'filter') {
      console.log(`🔍 DEBUG_FILTER_PROCESSED_FORMAT:`, JSON.stringify({
        objectId: objectId || 'undefined',
        dataFormat: 'processed',
        titulo: data.titulo,
        hasItems: !!data.items,
        itemsLength: data.items?.length || 0,
        itemsSample: data.items?.slice(0, 3) || [],
        hasSelectedItems: !!data.selectedItems,
        selectedItemsLength: data.selectedItems?.length || 0,
        searchText: data.searchText
      }, null, 2))

      return {
        titulo: data.titulo || 'Filtro',
        items: data.items || [],
        selectedItems: data.selectedItems || [],
        searchText: data.searchText || ''
      }
    }
    
    // Formato antigo: hypercube nativo (fallback)
    if (!data || !data.qHyperCube) {
      console.log(`🔍 DEBUG_FILTER_NO_HYPERCUBE:`, JSON.stringify({
        objectId: objectId || 'undefined',
        hasData: !!data,
        hasQHyperCube: !!(data?.qHyperCube),
        dataStructure: data ? Object.keys(data) : [],
        reason: 'Dados não encontrados ou sem qHyperCube'
      }, null, 2))
      return null
    }
    
    const hypercube = data.qHyperCube
    const dataPages = hypercube.qDataPages || []
    
    console.log(`🔍 DEBUG_FILTER_HYPERCUBE_STRUCTURE:`, JSON.stringify({
      objectId: objectId || 'undefined',
      hasHypercube: !!hypercube,
      hasDataPages: !!dataPages,
      dataPagesLength: dataPages.length,
      hypercubeKeys: hypercube ? Object.keys(hypercube) : [],
      dataPagesStructure: dataPages.map((dp, index) => ({
        index,
        hasMatrix: !!dp.qMatrix,
        matrixLength: dp.qMatrix?.length || 0
      }))
    }, null, 2))
    
    if (dataPages.length === 0 || !dataPages[0].qMatrix) {
      console.log(`🔍 DEBUG_FILTER_NO_MATRIX:`, JSON.stringify({
        objectId: objectId || 'undefined',
        dataPagesLength: dataPages.length,
        hasFirstMatrix: !!(dataPages[0]?.qMatrix),
        reason: 'Matrix não encontrada'
      }, null, 2))
      return null
    }
    
    const matrix = dataPages[0].qMatrix
    if (matrix.length === 0) {
      console.log(`🔍 DEBUG_FILTER_EMPTY_MATRIX:`, JSON.stringify({
        objectId: objectId || 'undefined',
        matrixLength: matrix.length,
        reason: 'Matrix vazia'
      }, null, 2))
      return null
    }

    console.log(`🔍 DEBUG_FILTER_MATRIX_PROCESSING:`, JSON.stringify({
      objectId: objectId || 'undefined',
      matrixLength: matrix.length,
      matrixSample: matrix.slice(0, 3),
      firstRowStructure: matrix[0] ? Object.keys(matrix[0]) : [],
      processingStarted: true
    }, null, 2))

    // Extrair itens do filtro da matrix
    const items = matrix.map((row, index) => {
      const item = {
        id: index,
        value: row[0]?.qText || row[0]?.qNum?.toString() || '',
        selected: row[0]?.qState === 'S', // Selected state from Qlik
        excluded: row[0]?.qState === 'X', // Excluded state from Qlik
        possible: row[0]?.qState === 'O' || row[0]?.qState === undefined // Optional/Available
      }

      console.log(`🔍 DEBUG_FILTER_ITEM_${index}:`, JSON.stringify({
        objectId: objectId || 'undefined',
        rowIndex: index,
        rawRow: row[0],
        processedItem: item
      }, null, 2))

      return item
    })

    const processedResult = {
      titulo: 'Filtro',
      items: items,
      selectedItems: items.filter(item => item.selected),
      searchText: ''
    }

    console.log(`🔍 DEBUG_FILTER_HYPERCUBE_RESULT:`, JSON.stringify({
      objectId: objectId || 'undefined',
      processedResult,
      totalItems: items.length,
      selectedCount: processedResult.selectedItems.length
    }, null, 2))

    return processedResult
  }

  // Gerar dados simulados para desenvolvimento
  const getSimulatedData = () => {
    console.log(`🔍 DEBUG_FILTER_SIMULATED_START:`, JSON.stringify({
      objectId: objectId || 'undefined',
      isDevelopment: process.env.NODE_ENV === 'development',
      simulationTriggered: true
    }, null, 2))

    const sampleItems = [
      'São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Brasília',
      'Fortaleza', 'Curitiba', 'Recife', 'Porto Alegre', 'Manaus',
      'Belém', 'Goiânia', 'Guarulhos', 'Campinas', 'São Luís'
    ]

    const simulatedResult = {
      titulo: `Filtro Simulado - ${objectId || 'Exemplo'}`,
      items: sampleItems.map((item, index) => ({
        id: index,
        value: item,
        selected: Math.random() > 0.7, // 30% chance de estar selecionado
        excluded: false,
        possible: true
      })),
      selectedItems: [],
      searchText: ''
    }

    console.log(`🔍 DEBUG_FILTER_SIMULATED_RESULT:`, JSON.stringify({
      objectId: objectId || 'undefined',
      simulatedResult,
      itemsCount: simulatedResult.items.length,
      sampleItems: simulatedResult.items.slice(0, 3)
    }, null, 2))

    return simulatedResult
  }

  // Obter dados processados ou simulados
  const filterData = containerSize.width > 0 ? 
    (process.env.NODE_ENV === 'development' ? getSimulatedData() : processData()) :
    null

  // ✅ NOVO: Filtrar itens baseado na busca
  const filteredItems = filterData?.items?.filter(item => 
    item.value.toLowerCase().includes(searchText.toLowerCase())
  ) || []

  console.log(`🔍 DEBUG_FILTER_FINAL_DATA:`, JSON.stringify({
    objectId: objectId || 'undefined',
    containerWidth: containerSize.width,
    containerHeight: containerSize.height,
    hasFilterData: !!filterData,
    filterDataContent: filterData,
    searchText: searchText,
    filteredItemsCount: filteredItems.length,
    isDevelopment: process.env.NODE_ENV === 'development',
    renderDecision: filterData ? 'RENDER_FILTER' : 'RENDER_LOADING'
  }, null, 2))

  if (!filterData) {
    console.log(`🔍 DEBUG_FILTER_RENDER_LOADING:`, JSON.stringify({
      objectId: objectId || 'undefined',
      reason: 'No filter data available',
      containerSize,
      renderingComponent: 'Loading'
    }, null, 2))

    return (
      <Box 
        ref={containerRef}
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height="100%"
        minHeight="150px"
      >
        <Typography variant="body2" color="textSecondary">
          Carregando filtro...
        </Typography>
      </Box>
    )
  }

  const renderFilterItems = () => {
    console.log(`🔍 DEBUG_FILTER_RENDER_ITEMS:`, JSON.stringify({
      objectId: objectId || 'undefined',
      hasItems: !!(filterData.items),
      itemsLength: filterData.items?.length || 0,
      filteredItemsLength: filteredItems.length,
      searchText: searchText,
      renderingItems: true
    }, null, 2))

    if (!filteredItems || filteredItems.length === 0) {
      console.log(`🔍 DEBUG_FILTER_NO_ITEMS:`, JSON.stringify({
        objectId: objectId || 'undefined',
        hasItems: !!filterData.items,
        itemsLength: filterData.items?.length || 0,
        filteredItemsLength: filteredItems.length,
        searchText: searchText,
        renderingEmptyState: true
      }, null, 2))

      return (
        <Box textAlign="center" py={2}>
          <Typography variant="body2" color="textSecondary">
            {searchText ? 'Nenhum item encontrado' : 'Nenhum item disponível'}
          </Typography>
        </Box>
      )
    }

    console.log(`🔍 DEBUG_FILTER_RENDERING_CHIPS:`, JSON.stringify({
      objectId: objectId || 'undefined',
      itemsToRender: filteredItems.length,
      firstThreeItems: filteredItems.slice(0, 3),
      finalConfig: {
        maxHeight: finalConfig.maxHeight,
        compact: finalConfig.compact
      }
    }, null, 2))

    return (
      <Box 
        sx={{ 
          maxHeight: finalConfig.maxHeight,
          overflowY: 'auto', // ✅ CORRIGIDO: Permitir scroll vertical
          overflowX: 'hidden', // ✅ NOVO: Evitar scroll horizontal
          display: 'flex',
          flexDirection: finalConfig.compact ? 'row' : 'column',
          flexWrap: finalConfig.compact ? 'wrap' : 'nowrap',
          gap: 1,
          p: 1,
          // ✅ NOVO: Estilização do scrollbar
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            background: theme.palette.grey[100],
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-thumb': {
            background: theme.palette.grey[400],
            borderRadius: '3px',
            '&:hover': {
              background: theme.palette.grey[600],
            },
          },
        }}
      >
        {filteredItems.map((item, index) => {
          // ✅ NOVO: Usar seleções locais para determinar estado visual
          const isLocallySelected = localSelections.has(item.value)
          const finalSelected = isLocallySelected || item.selected // Priorizar seleção local
          
          console.log(`🔍 DEBUG_FILTER_CHIP_${index}:`, JSON.stringify({
            objectId: objectId || 'undefined',
            chipIndex: index,
            item: item,
            originalSelected: item.selected,
            isLocallySelected: isLocallySelected,
            finalSelected: finalSelected,
            variant: finalSelected ? 'filled' : 'outlined',
            color: finalSelected ? 'primary' : 'default'
          }, null, 2))

          return (
            <Chip
              key={item.id}
              label={item.value}
              variant={finalSelected ? 'filled' : 'outlined'}
              color={finalSelected ? 'primary' : 'default'}
              size={finalConfig.compact ? 'small' : 'medium'}
              clickable
              sx={{
                opacity: item.excluded ? 0.5 : 1,
                textDecoration: item.excluded ? 'line-through' : 'none',
                minWidth: 'fit-content', // ✅ NOVO: Evitar quebra de texto
                // ✅ MELHORADO: Background mais visível para selecionados
                backgroundColor: finalSelected ? 
                  theme.palette.primary.main : 
                  'transparent',
                color: finalSelected ? 
                  theme.palette.primary.contrastText : 
                  theme.palette.text.primary,
                border: finalSelected ? 
                  `1px solid ${theme.palette.primary.main}` : 
                  `1px solid ${theme.palette.divider}`,
                '&:hover': {
                  backgroundColor: finalSelected ? 
                    theme.palette.primary.dark : 
                    theme.palette.action.hover,
                  transform: 'scale(1.02)', // ✅ NOVO: Efeito hover sutil
                },
                transition: 'all 0.2s ease-in-out', // ✅ NOVO: Transição suave
              }}
              onClick={() => {
                console.log(`🖱️ DEBUG_FILTER_CLICK_CHIP_CLICKED:`, JSON.stringify({
                  objectId: objectId || 'undefined',
                  chipIndex: index,
                  clickedItem: item,
                  itemValue: item.value,
                  currentlySelected: finalSelected, // ✅ CORRIGIDO: Usar estado final
                  action: finalSelected ? 'desselecionar' : 'selecionar',
                  clickTimestamp: new Date().toISOString()
                }, null, 2))
                // ✅ CORRIGIDO: Passar item com estado atualizado
                handleItemClick({...item, selected: finalSelected})
              }}
            />
          )
        })}
      </Box>
    )
  }

  // ✅ MODIFICADO: Renderizar botões de ação para filterpane
  const renderActionButtons = () => {
    if (!finalConfig.showClearButton && !isFilterpane) return null

    return (
      <Box display="flex" alignItems="center" gap={1} mb={1}>
        {finalConfig.showSearchBox && (
          <TextField
            variant="outlined"
            size="small"
            fullWidth
            placeholder="Buscar ou clicar para filtrar"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search color="action" />
                </InputAdornment>
              ),
              endAdornment: searchText && (
                <InputAdornment position="end">
                  <Clear 
                    color="action" 
                    sx={{ 
                      cursor: 'pointer', 
                      fontSize: '20px',
                      '&:hover': { color: theme.palette.primary.main } 
                    }}
                    onClick={() => setSearchText('')}
                  />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                height: '36px',
                fontSize: '14px',
              }
            }}
          />
        )}
        
        {/* Botão de limpeza */}
        {finalConfig.showClearButton && (
          <Clear 
            color="action" 
            sx={{ 
              cursor: 'pointer', 
              fontSize: '24px',
              '&:hover': { color: theme.palette.primary.main } 
            }}
            onClick={handleClearAll}
            title="Limpar todas as seleções"
          />
        )}
        
        {/* ✅ NOVO: Botões de confirmação para filterpane */}
        {isFilterpane && (
          <>
            <Button
              size="small"
              variant="contained"
              color="primary"
              startIcon={<Check />}
              onClick={() => setConfirmationModalOpen(true)}
              disabled={pendingSelections.size === 0}
              sx={{ minWidth: 'auto', px: 1 }}
            >
              Aplicar
            </Button>
            <Button
              size="small"
              variant="outlined"
              color="secondary"
              startIcon={<Close />}
              onClick={cancelPendingSelections}
              disabled={pendingSelections.size === 0}
              sx={{ minWidth: 'auto', px: 1 }}
            >
              Cancelar
            </Button>
          </>
        )}
      </Box>
    )
  }

  console.log(`🔍 DEBUG_FILTER_RENDER_FINAL:`, JSON.stringify({
    objectId: objectId || 'undefined',
    renderingMainComponent: true,
    filterDataTitle: filterData.titulo,
    showTitle: finalConfig.showTitle,
    showSearchBox: finalConfig.showSearchBox,
    showClearButton: finalConfig.showClearButton,
    selectedItemsCount: filterData.selectedItems?.length || 0
  }, null, 2))

  return (
    <Card 
      ref={containerRef}
      sx={{ 
        height: '100%',
        backgroundColor: finalConfig.backgroundColor,
        borderRadius: finalConfig.borderRadius,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {finalConfig.showTitle && (
        <CardContent sx={{ pb: 1 }}>
          <Box display="flex" alignItems="center" gap={1}>
            <FilterList color="primary" />
            <Typography variant="h6" component="div">
              {filterData.titulo}
            </Typography>
            {/* ✅ MELHORADO: Usar seleções locais para contador */}
            {localSelections.size > 0 && (
              <Chip 
                label={`${localSelections.size} selecionado(s)`}
                size="small"
                color="primary"
                variant="outlined"
                sx={{
                  // ✅ NOVO: Estilização mais visível
                  backgroundColor: theme.palette.primary.light,
                  color: theme.palette.primary.contrastText,
                  fontWeight: 'bold'
                }}
              />
            )}
            {/* ✅ DEBUG: Log do contador de seleções */}
            {console.log(`🔍 DEBUG_FILTER_SELECTION_COUNT:`, JSON.stringify({
              objectId: objectId || 'undefined',
              localSelectionsSize: localSelections.size,
              localSelectionsArray: Array.from(localSelections),
              originalSelectedItems: filterData.selectedItems?.length || 0,
              showingCounter: localSelections.size > 0
            }, null, 2))}
          </Box>
        </CardContent>
      )}
      
      <CardContent sx={{ flex: 1, pt: finalConfig.showTitle ? 0 : 1 }}>
        {renderActionButtons()}
        {renderFilterItems()}
      </CardContent>
      
      {/* ✅ NOVO: Modal de Confirmação para Filterpane */}
      <Dialog
        open={confirmationModalOpen}
        onClose={() => setConfirmationModalOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={1}>
            <FilterList color="primary" />
            <Typography variant="h6">
              Confirmar Seleções - {filterData?.titulo || 'Filtro'}
            </Typography>
          </Box>
        </DialogTitle>
        
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Confirme as seleções que serão aplicadas no filtro:
          </Typography>
          
          {pendingSelections.size === 0 ? (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Nenhuma seleção pendente
              </Typography>
            </Box>
          ) : (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                {pendingSelections.size} seleção(ões):
              </Typography>
              <List dense>
                {Array.from(pendingSelections).map((value, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemText
                      primary={value}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="caption" color="text.disabled">
            Após confirmar, estas seleções serão aplicadas no Qlik Sense e 
            todos os outros objetos serão atualizados automaticamente.
          </Typography>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setConfirmationModalOpen(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={applyPendingSelections}
            variant="contained"
            color="primary"
            startIcon={<Check />}
          >
            Confirmar e Aplicar
          </Button>
        </DialogActions>
      </Dialog>
    </Card>
  )
}

export default CustomFilter 