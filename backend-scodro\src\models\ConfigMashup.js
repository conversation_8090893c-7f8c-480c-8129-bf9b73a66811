const mongoose = require('mongoose');

const configMashupSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    default: 'default',
    trim: true
  },
  nome: {
    type: String,
    required: true,
    default: 'Configuração Principal',
    trim: true
  },
  // Configurações do Qlik Cloud
  qlikCloud: {
    tenantUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function(v) {
          if (!v) return true; // Campo opcional
          return /^https?:\/\/.+/.test(v);
        },
        message: 'URL do tenant deve ser uma URL válida'
      }
    },
    webIntegrationId: {
      type: String,
      trim: true
    }
  },
  // Configurações do Qlik Enterprise
  qlikEnterprise: {
    serverUrl: {
      type: String,
      trim: true,
      validate: {
        validator: function(v) {
          if (!v) return true; // Campo opcional
          return /^https?:\/\/.+/.test(v);
        },
        message: 'URL do servidor deve ser uma URL válida'
      }
    },
    prefix: {
      type: String,
      trim: true,
      default: ''
    },
    virtualProxy: {
      type: String,
      trim: true,
      default: ''
    }
  },
  // Configurações gerais
  configuracoes: {
    ambiente: {
      type: String,
      enum: ['cloud', 'enterprise', 'hybrid'],
      default: 'cloud'
    },
    tema: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'light'
    },
    idioma: {
      type: String,
      default: 'pt-BR'
    },
    debug: {
      type: Boolean,
      default: false
    }
  },
  // Metadados
  ativo: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String,
    default: 'system'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices
configMashupSchema.index({ ativo: 1 });

// Middleware para atualizar updatedAt
configMashupSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Método para obter configuração ativa
configMashupSchema.statics.getConfigAtiva = async function() {
  let config = await this.findOne({ ativo: true });
  
  // Se não existir configuração, criar uma padrão
  if (!config) {
    config = new this({
      id: 'default',
      nome: 'Configuração Principal',
      ativo: true
    });
    await config.save();
  }
  
  return config;
};

// Método para validar configuração do ambiente
configMashupSchema.methods.validarConfiguracao = function() {
  const errors = [];
  
  if (this.configuracoes.ambiente === 'cloud') {
    if (!this.qlikCloud.tenantUrl) {
      errors.push('URL do tenant é obrigatória para Qlik Cloud');
    }
    if (!this.qlikCloud.webIntegrationId) {
      errors.push('Web Integration ID é obrigatório para Qlik Cloud');
    }
  }
  
  if (this.configuracoes.ambiente === 'enterprise') {
    if (!this.qlikEnterprise.serverUrl) {
      errors.push('URL do servidor é obrigatória para Qlik Enterprise');
    }
  }
  
  return {
    valido: errors.length === 0,
    erros: errors
  };
};

module.exports = mongoose.model('ConfigMashup', configMashupSchema); 