const Pagina = require('../models/Pagina');
const Empresa = require('../models/Empresa');

class PaginaController {
  // Listar páginas por empresa
  async listarPaginas(req, res) {
    try {
      const { empresaId } = req.params;
      const { ativo, incluirInativas } = req.query;
      
      // Verificar se a empresa existe
      const empresa = await Empresa.findByCustomId(empresaId);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      let query = { empresaId };
      // Por padr<PERSON>, só retorna páginas ativas
      if (incluirInativas !== 'true') {
        query.ativo = ativo === 'false' ? false : true;
      }

      const paginas = await Pagina.find(query).sort({ ordem: 1 });

      res.json({
        success: true,
        data: paginas,
        total: paginas.length
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao listar páginas',
        message: error.message
      });
    }
  }

  // Obter página específica
  async obterPagina(req, res) {
    try {
      const { empresaId, id } = req.params;

      const pagina = await Pagina.findOne({ 
        _id: id, 
        empresaId 
      });

      if (!pagina) {
        return res.status(404).json({
          success: false,
          error: 'Página não encontrada'
        });
      }

      res.json({
        success: true,
        data: pagina
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao obter página',
        message: error.message
      });
    }
  }

  // Criar nova página
  async criarPagina(req, res) {
    try {
      const { empresaId } = req.params;
      const dadosPagina = { ...req.body, empresaId };

      // Verificar se a empresa existe
      const empresa = await Empresa.findByCustomId(empresaId);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      // Verificar se já existe uma página ATIVA com a mesma chave
      const paginaExistente = await Pagina.findOne({
        empresaId,
        chave: dadosPagina.chave,
        ativo: true
      });

      if (paginaExistente) {
        return res.status(409).json({
          success: false,
          error: 'Já existe uma página ativa com esta chave para esta empresa'
        });
      }

      // Obter próxima ordem se não especificada
      if (!dadosPagina.ordem) {
        dadosPagina.ordem = await Pagina.getProximaOrdem(empresaId);
      }

      const pagina = new Pagina(dadosPagina);
      await pagina.save();

      res.status(201).json({
        success: true,
        data: pagina,
        message: 'Página criada com sucesso'
      });
    } catch (error) {
      if (error.code === 11000) {
        return res.status(409).json({
          success: false,
          error: 'Página com esta chave já existe para esta empresa'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Erro ao criar página',
        message: error.message
      });
    }
  }

  // Atualizar página
  async atualizarPagina(req, res) {
    try {
      const { empresaId, id } = req.params;

      const pagina = await Pagina.findOne({ 
        _id: id, 
        empresaId 
      });

      if (!pagina) {
        return res.status(404).json({
          success: false,
          error: 'Página não encontrada'
        });
      }

      // Se está alterando a chave, verificar se não existe outra ATIVA com a mesma chave
      if (req.body.chave && req.body.chave !== pagina.chave) {
        const paginaExistente = await Pagina.findOne({
          empresaId,
          chave: req.body.chave,
          ativo: true,
          _id: { $ne: id }
        });

        if (paginaExistente) {
          return res.status(409).json({
            success: false,
            error: 'Já existe uma página ativa com esta chave para esta empresa'
          });
        }
      }

      // Atualizar campos
      Object.assign(pagina, req.body);
      await pagina.save();

      res.json({
        success: true,
        data: pagina,
        message: 'Página atualizada com sucesso'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao atualizar página',
        message: error.message
      });
    }
  }

  // Excluir página
  async excluirPagina(req, res) {
    try {
      const { empresaId, id } = req.params;
      const { forceDelete } = req.query;

      const pagina = await Pagina.findOne({ 
        _id: id, 
        empresaId 
      });

      if (!pagina) {
        return res.status(404).json({
          success: false,
          error: 'Página não encontrada'
        });
      }

      if (forceDelete === 'true') {
        await Pagina.deleteOne({ _id: id, empresaId });
        res.json({
          success: true,
          message: 'Página excluída permanentemente'
        });
      } else {
        // Soft delete
        pagina.ativo = false;
        await pagina.save();
        res.json({
          success: true,
          message: 'Página desativada com sucesso'
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao excluir página',
        message: error.message
      });
    }
  }

  // Reativar página
  async reativarPagina(req, res) {
    try {
      const { empresaId, id } = req.params;

      const pagina = await Pagina.findOne({ 
        _id: id, 
        empresaId 
      });

      if (!pagina) {
        return res.status(404).json({
          success: false,
          error: 'Página não encontrada'
        });
      }

      pagina.ativo = true;
      await pagina.save();

      res.json({
        success: true,
        data: pagina,
        message: 'Página reativada com sucesso'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao reativar página',
        message: error.message
      });
    }
  }

  // Reordenar páginas
  async reordenarPaginas(req, res) {
    try {
      const { empresaId } = req.params;
      const { paginas } = req.body;

      if (!Array.isArray(paginas)) {
        return res.status(400).json({
          success: false,
          error: 'Lista de páginas deve ser um array'
        });
      }

      await Pagina.reordenarPaginas(empresaId, paginas);

      res.json({
        success: true,
        message: 'Páginas reordenadas com sucesso'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao reordenar páginas',
        message: error.message
      });
    }
  }

  // Criar páginas padrão para uma empresa
  async criarPaginasPadrao(req, res) {
    try {
      const { empresaId } = req.params;

      // Verificar se a empresa existe
      const empresa = await Empresa.findByCustomId(empresaId);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      // Páginas padrão
      const paginasPadrao = [
        {
          empresaId,
          chave: 'dashboard',
          titulo: 'Dashboard',
          descricao: 'Visão geral dos KPIs',
          icone: 'dashboard',
          rota: '/',
          ordem: 1
        },
        {
          empresaId,
          chave: 'vendas',
          titulo: 'Vendas',
          descricao: 'Análise de vendas',
          icone: 'trending_up',
          rota: '/vendas',
          ordem: 2
        },
        {
          empresaId,
          chave: 'helpdesk',
          titulo: 'Helpdesk',
          descricao: 'Suporte e tickets',
          icone: 'support',
          rota: '/helpdesk',
          ordem: 3
        },
        {
          empresaId,
          chave: 'financeiro',
          titulo: 'Financeiro',
          descricao: 'Relatórios financeiros',
          icone: 'account_balance',
          rota: '/financeiro',
          ordem: 4
        }
      ];

      // Verificar quais páginas já existem
      const paginasExistentes = await Pagina.find({ empresaId });
      const chavesExistentes = paginasExistentes.map(p => p.chave);

      // Filtrar apenas páginas que não existem
      const paginasParaCriar = paginasPadrao.filter(p => !chavesExistentes.includes(p.chave));

      if (paginasParaCriar.length === 0) {
        return res.json({
          success: true,
          message: 'Todas as páginas padrão já existem para esta empresa',
          data: []
        });
      }

      // Criar páginas
      const paginasCriadas = await Pagina.insertMany(paginasParaCriar);

      res.status(201).json({
        success: true,
        data: paginasCriadas,
        message: `${paginasCriadas.length} páginas padrão criadas com sucesso`
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao criar páginas padrão',
        message: error.message
      });
    }
  }
}

module.exports = new PaginaController(); 