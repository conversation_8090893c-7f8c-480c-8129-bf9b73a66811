# 🆘 Debug de Tabelas - <PERSON><PERSON><PERSON> de Troubleshooting

Este guia ajuda a investigar problemas com o componente CustomTable quando não está exibindo dados do Qlik Sense.

## 🔍 Como Usar o Debug

### 1. Ativar Modo Debug
Certifique-se de que o console do navegador está aberto (F12 → Console).

### 2. Filtrar Logs de Debug
No console, filtre por:
- `TABLE_DEBUG` - Logs específicos da tabela
- `DEBUG_TABLE` - Logs antigos ainda presentes
- `🆘` - Logs de investigação crítica

### 3. Lista Completa de Logs TABLE_DEBUG
Para facilitar a filtragem, todos os logs relacionados à tabela usam o prefixo `TABLE_DEBUG`:

**Logs Principais:**
- `🆘 TABLE_DEBUG_QLIK_DATA` - Dados iniciais do Qlik
- `🔍 TABLE_DEBUG_MATRIX` - <PERSON>riz processada
- `📋 TABLE_DEBUG_PROCESSED_DATA` - Dados finais formatados
- `🎯 TABLE_DEBUG_CUSTOM_TABLE_PROPS` - Props do componente
- `💡 TABLE_DEBUG_SUGGESTIONS` - Sugestões automáticas

**Logs de Pivot (NOVO):**
- `🔄 TABLE_DEBUG_PIVOT_RAW` - Dados brutos do pivot
- `🔄 TABLE_DEBUG_EXTRACT_PIVOT_START` - Início da extração
- `🔄 TABLE_DEBUG_PIVOT_STRUCTURE_ANALYSIS` - Análise da estrutura
- `🔄 TABLE_DEBUG_PIVOT_ROW_X` - Processamento da linha X
- `📊 TABLE_DEBUG_PIVOT_CELL_X_Y` - Processamento da célula [X,Y]
- `✅ TABLE_DEBUG_PIVOT_ROW_PROCESSED_X` - Linha X processada
- `🔄 TABLE_DEBUG_PIVOT_ALTERNATIVE_STRUCTURE` - Estrutura alternativa
- `✅ TABLE_DEBUG_PIVOT_ALTERNATIVE_ROW` - Linha alternativa criada
- `🔄 TABLE_DEBUG_EXTRACT_PIVOT_RESULT` - Resultado final do pivot
- `🔄 TABLE_DEBUG_MATRIX_PIVOT` - Matriz final do pivot

**Logs de Erro:**
- `⚠️ TABLE_DEBUG_PIVOT_INVALID_STRUCTURE` - Estrutura pivot inválida
- `⚠️ TABLE_DEBUG_PIVOT_ROW_NOT_ARRAY_X` - Linha X não é array

## 📊 Logs de Debug Disponíveis

### A. `TABLE_DEBUG_QLIK_DATA` 
**Quando aparece:** Início do processamento dos dados do Qlik
**O que mostra:**
- Configuração do objeto (`config.tipo`, `config.nome`)
- Estrutura do HyperCube
- Dimensões e medidas disponíveis
- Páginas de dados (DataPages vs StackedDataPages)

**Exemplo:**
```
🆘 TABLE_DEBUG_QLIK_DATA - Tabela Exemplo
  📋 CONFIG: {tipo: "table", nome: "Minha Tabela"}
  📊 HYPERCUBE_DATA: {hasHyperCube: true, keys: ["qHyperCube"]}
  📈 HYPERCUBE_STRUCTURE: {dimensions: 2, measures: 3}
```

### B. `TABLE_DEBUG_MATRIX`
**Quando aparece:** Após extrair a matriz de dados
**O que mostra:**
- Se a matriz existe e quantas linhas tem
- Estrutura das dimensões e medidas
- Amostra dos dados

**Exemplo:**
```
🔍 TABLE_DEBUG_MATRIX - Tabela Exemplo
  📊 MATRIX_INFO: {exists: true, length: 150, firstRowLength: 5}
  📋 HYPERCUBE_METADATA: {dimensions: 2, measures: 3}
```

### C. `TABLE_DEBUG_PROCESSED_DATA`
**Quando aparece:** Após processar os dados para formato de tabela
**O que mostra:**
- Dados finais processados
- Colunas criadas
- Linhas processadas

### D. `TABLE_DEBUG_CUSTOM_TABLE_PROPS`
**Quando aparece:** No componente CustomTable
**O que mostra:**
- Props recebidas pelo componente
- Validação dos dados
- Estrutura final

## 🚨 Problemas Comuns e Soluções

### 1. "HyperCube não encontrado"
**Causa:** O objeto Qlik não tem dados ou configuração incorreta
**Solução:**
- Verificar se o objectId está correto
- Confirmar se o objeto existe no Qlik Sense
- Verificar se há dimensões/medidas configuradas

### 2. "Matriz vazia ou não encontrada"
**Causa:** O objeto tem HyperCube mas sem dados
**Possíveis causas:**
- Filtros aplicados que ocultam todos os dados
- Seleções que resultam em conjunto vazio
- Problema na definição das dimensões/medidas

**Investigar:**
```javascript
// No console, procurar por:
DEBUG_AVAILABLE_PATHS
```

### 3. "INVALID_TABLE_DATA"
**Causa:** Dados chegaram ao CustomTable mas estão malformados
**Verificar:**
- Se `data.tipo === 'table'`
- Se `data.columns` é um array
- Se `data.rows` é um array

### 4. Tabela aparece mas "Sem Dados"
**Causa:** Estrutura correta mas arrays vazios
**Verificar:**
- `columnsCount` e `rowsCount` nos logs
- Se o processamento da matriz está correto

## 🔧 Passos de Investigação

### Passo 1: Verificar Configuração
```javascript
// Procurar no console:
TABLE_DEBUG_QLIK_DATA
```
- ✅ `config.tipo` deve ser "table"
- ✅ `hasHyperCube` deve ser true
- ✅ `dimensions` + `measures` > 0

### Passo 2: Verificar Dados Brutos
```javascript
// Procurar no console:
DEBUG_HYPERCUBE_RAW
```
- ✅ Verificar se `qDataPages`, `qStackedDataPages` ou `qPivotDataPages` existem
- ✅ Confirmar se há dados na matriz

### Passo 3: Verificar Processamento da Matriz
```javascript
// Procurar no console:
TABLE_DEBUG_MATRIX
```
- ✅ `exists` deve ser true
- ✅ `length` deve ser > 0

### Passo 3.1: Se for Pivot, Verificar Extração
```javascript
// Procurar no console (APENAS se hasPivotDataPages = true):
TABLE_DEBUG_PIVOT_RAW
TABLE_DEBUG_EXTRACT_PIVOT_START
TABLE_DEBUG_PIVOT_STRUCTURE_ANALYSIS
TABLE_DEBUG_EXTRACT_PIVOT_RESULT
```
- ✅ `processedMatrixLength` deve ser > 0
- ✅ Verificar se `qLeft`, `qTop` e `qData` foram processados

### Passo 4: Verificar Resultado Final
```javascript
// Procurar no console:
TABLE_DEBUG_PROCESSED_DATA
```
- ✅ `columnsCount` > 0
- ✅ `rowsCount` > 0

### Passo 5: Verificar CustomTable
```javascript
// Procurar no console:
TABLE_DEBUG_CUSTOM_TABLE_PROPS
```
- ✅ `isValidTableData` deve ser true
- ✅ `hasRequiredColumns` e `hasRequiredRows` devem ser true