const mongoose = require('mongoose');

const appSchema = new mongoose.Schema({
  empresaId: {
    type: String,
    required: true,
    trim: true,
    ref: 'Empresa'
  },
  appId: {
    type: String,
    required: true,
    trim: true
  },
  nome: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  descricao: {
    type: String,
    trim: true,
    maxlength: 500
  },
  categoria: {
    type: String,
    enum: ['dashboard', 'vendas', 'financeiro', 'helpdesk', 'rh', 'operacional', 'outros'],
    default: 'dashboard'
  },
  // Configurações específicas do app
  configuracao: {
    altura: {
      type: String,
      default: '600px'
    },
    largura: {
      type: String,
      default: '100%'
    },
    tema: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'light'
    },
    // Configurações de carregamento
    autoLoad: {
      type: Boolean,
      default: true
    },
    // Configurações de interação
    allowInteraction: {
      type: Boolean,
      default: true
    }
  },
  // Ordem de exibição
  ordem: {
    type: Number,
    default: 1
  },
  // Status
  ativo: {
    type: Boolean,
    default: true
  },
  // Metadados
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String,
    default: 'system'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices compostos para performance
appSchema.index({ empresaId: 1, ordem: 1 });
appSchema.index({ empresaId: 1, ativo: 1 });
appSchema.index({ empresaId: 1, categoria: 1 });

// Virtual para objetos relacionados
appSchema.virtual('objetos', {
  ref: 'Objeto',
  localField: '_id',
  foreignField: 'appId'
});

// Middleware para atualizar updatedAt
appSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Método para obter próxima ordem
appSchema.statics.getProximaOrdem = async function(empresaId) {
  const ultimoApp = await this.findOne({ empresaId })
    .sort({ ordem: -1 })
    .select('ordem');
  
  return ultimoApp ? ultimoApp.ordem + 1 : 1;
};

// Método para reordenar apps
appSchema.statics.reordenarApps = async function(empresaId, appsOrdenados) {
  const operations = appsOrdenados.map((app, index) => ({
    updateOne: {
      filter: { _id: app._id, empresaId },
      update: { ordem: index + 1 }
    }
  }));

  return this.bulkWrite(operations);
};

module.exports = mongoose.model('App', appSchema); 