import React, { useState, useRef, useEffect } from 'react'
import { Box, Typography, Card, CardContent, useTheme } from '@mui/material'
import { 
  TrendingUp, 
  TrendingDown, 
  TrendingFlat,
  AttachMoney,
  Person,
  ShoppingCart,
  Timeline,
  Assessment,
  Star,
  Favorite,
  Business,
  LocalAtm,
  AccountBalance,
  Analytics
} from '@mui/icons-material'

// Galeria de ícones disponíveis
const ICON_GALLERY = {
  money: Attach<PERSON><PERSON>,
  person: Person,
  cart: ShoppingCart,
  timeline: Timeline,
  assessment: Assessment,
  star: Star,
  heart: Favorite,
  business: Business,
  atm: LocalAtm,
  bank: AccountBalance,
  analytics: Analytics
}

// Paleta de cores predefinidas
const COLOR_PALETTE = [
  '#1976d2', // Azul
  '#388e3c', // Verde
  '#f57c00', // Laranja
  '#d32f2f', // Vermelho
  '#7b1fa2', // Roxo
  '#00796b', // Teal
  '#5d4037', // <PERSON><PERSON>
  '#616161', // Cinza
  '#e91e63', // Rosa
  '#ff5722'  // Deep Orange
]

const CustomKPI = ({ data, config = {}, objectId }) => {
  const theme = useTheme()
  const containerRef = useRef(null)
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
  
  // Configurações padrão
  const defaultConfig = {
    showTitle: true,
    showIcon: true,
    icone: 'analytics',
    formato: 'numero',
    casasDecimais: 0,
    prefix: '',
    suffix: '',
    showTrend: false,
    cor: '#1976d2',
    corFundo: 'transparent',
    corFonte: '#ffffff',
    textAlign: 'center',
    fontSize: 'auto',
    gradiente: false
  }
  
  const finalConfig = { ...defaultConfig, ...config.kpiConfig, ...config }

  // Detectar tamanho do container uma única vez quando o componente monta
  useEffect(() => {
    if (containerRef.current) {
      const updateSize = () => {
        const rect = containerRef.current.getBoundingClientRect()
        console.log(`📏 CustomKPI tamanho detectado:`, {
          objectId: objectId || 'simulado',
          width: rect.width,
          height: rect.height
        })
        setContainerSize({ width: rect.width, height: rect.height })
      }
      
      // Detectar tamanho inicial
      updateSize()
      
      // Opcional: também detectar redimensionamento da janela
      window.addEventListener('resize', updateSize)
      return () => window.removeEventListener('resize', updateSize)
    }
  }, []) // Executar apenas na montagem

  // Processar dados do Qlik
  const processData = () => {
    // console.log(`📊 DEBUG_CUSTOMKPI_INPUT:`, JSON.stringify({ 
    //   data, 
    //   config, 
    //   finalConfig,
    //   hasData: !!data,
    //   dataType: data?.tipo 
    // }, null, 2))
    
    // Novo formato: dados já processados
    if (data && data.tipo === 'kpi') {
      const value = data.valor
      const formattedValue = data.valorFormatado || formatValue(value, finalConfig)
      
      // console.log(`📊 DEBUG_CUSTOMKPI_PROCESSED:`, JSON.stringify({
      //   value,
      //   formattedValue,
      //   titulo: data.titulo,
      //   medida: data.medida,
      //   finalConfig
      // }, null, 2))
      
      return {
        value,
        previousValue: null,
        formattedValue,
        trend: null,
        titulo: data.titulo,
        medida: data.medida
      }
    }
    
    // Formato antigo: hypercube nativo (fallback)
    if (!data || !data.qHyperCube) {
      console.log(`⚠️ DEBUG_CUSTOMKPI_NO_DATA: Dados não encontrados`)
      return null
    }
    
    const hypercube = data.qHyperCube
    const dataPages = hypercube.qDataPages || []
    
    if (dataPages.length === 0 || !dataPages[0].qMatrix) {
      console.log(`⚠️ DEBUG_CUSTOMKPI_NO_MATRIX: Matrix não encontrada`)
      return null
    }
    
    const matrix = dataPages[0].qMatrix
    if (matrix.length === 0) {
      console.log(`⚠️ DEBUG_CUSTOMKPI_EMPTY_MATRIX: Matrix vazia`)
      return null
    }
    
    const mainValue = matrix[0][0]
    const value = mainValue?.qNum !== undefined ? mainValue.qNum : mainValue?.qText || 0
    const previousValue = matrix[0][1]?.qNum
    
    return {
      value,
      previousValue,
      formattedValue: formatValue(value, finalConfig),
      trend: calculateTrend(value, previousValue)
    }
  }
  
  const formatValue = (value, config) => {
    if (value === null || value === undefined) return 'N/A'
    
    let formatted = value
    
    switch (config.formato) {
      case 'moeda':
        formatted = new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL',
          minimumFractionDigits: config.casasDecimais,
          maximumFractionDigits: config.casasDecimais
        }).format(value)
        break
        
      case 'porcentagem':
        formatted = new Intl.NumberFormat('pt-BR', {
          style: 'percent',
          minimumFractionDigits: config.casasDecimais,
          maximumFractionDigits: config.casasDecimais
        }).format(value / 100)
        break
        
      case 'numero':
      default:
        formatted = new Intl.NumberFormat('pt-BR', {
          minimumFractionDigits: config.casasDecimais,
          maximumFractionDigits: config.casasDecimais
        }).format(value)
        break
    }
    
    return `${config.prefix}${formatted}${config.suffix}`
  }
  
  const calculateTrend = (current, previous) => {
    if (!previous || !current) return null
    
    const change = ((current - previous) / previous) * 100
    
    if (change > 0) return { direction: 'up', percentage: change }
    if (change < 0) return { direction: 'down', percentage: Math.abs(change) }
    return { direction: 'flat', percentage: 0 }
  }
  
  const getTrendIcon = (trend) => {
    if (!trend) return null
    
    const iconProps = { 
      sx: { 
        ml: 1, 
        fontSize: `${Math.max(containerSize.height * 0.08, 16)}px`,
        color: trend.direction === 'up' ? 'success.main' : 
               trend.direction === 'down' ? 'error.main' : 'grey.500'
      } 
    }
    
    switch (trend.direction) {
      case 'up': return <TrendingUp {...iconProps} />
      case 'down': return <TrendingDown {...iconProps} />
      case 'flat': return <TrendingFlat {...iconProps} />
      default: return null
    }
  }

  // Calcular tamanho responsivo da fonte - VERSÃO SIMPLIFICADA
  const getResponsiveFontSize = () => {
    if (finalConfig.fontSize !== 'auto') {
      return finalConfig.fontSize
    }
    
    const { width, height } = containerSize
    
    // Se ainda não temos o tamanho do container, usar valores padrão
    if (width === 0 || height === 0) {
      return '20px'
    }
    
    // Fórmula simples: menor dimensão * 25%
    const minDimension = Math.min(width, height)
    let fontSize = minDimension * 0.25
    
    // Limites
    fontSize = Math.max(fontSize, 12) // Mínimo 12px
    fontSize = Math.min(fontSize, 60) // Máximo 60px
    
    console.log(`📏 Fonte calculada:`, {
      objectId: objectId || 'simulado',
      containerSize: { width, height },
      minDimension,
      fontSize: Math.round(fontSize)
    })
    
    return `${Math.round(fontSize)}px`
  }

  // Calcular tamanho do ícone
  const getIconSize = () => {
    const { width, height } = containerSize
    if (width === 0 || height === 0) return 20
    
    const minDimension = Math.min(width, height)
    const iconSize = Math.max(Math.min(minDimension * 0.20, 40), 12)
    return Math.round(iconSize)
  }

  // Calcular tamanho do título
  const getTitleFontSize = () => {
    const { width, height } = containerSize
    if (width === 0 || height === 0) return '10px'
    
    const minDimension = Math.min(width, height)
    const titleSize = Math.max(Math.min(minDimension * 0.12, 18), 8)
    return `${Math.round(titleSize)}px`
  }

  // Renderizar ícone do KPI
  const renderIcon = () => {
    if (!finalConfig.showIcon || !finalConfig.icone) return null
    
    const IconComponent = ICON_GALLERY[finalConfig.icone] || ICON_GALLERY.analytics
    
    return (
      <IconComponent 
        sx={{ 
          fontSize: `${getIconSize()}px`,
          color: finalConfig.corFonte,
          opacity: 0.8
        }} 
      />
    )
  }

  const processedData = processData()
  
  if (!processedData) {
    // Se não tem dados, criar dados simulados
    console.log(`⚠️ KPI sem dados, criando simulação`)
    const simulatedKPI = {
      value: 12345,
      previousValue: null,
      formattedValue: 'R$ 12.345',
      trend: null,
      titulo: 'KPI de Exemplo',
      medida: 'Valor Simulado'
    }
    
    return (
      <Card 
        ref={containerRef}
        sx={{ 
          height: '100%', 
          background: finalConfig.gradiente ? 
            `linear-gradient(135deg, ${finalConfig.cor} 0%, ${finalConfig.cor}80 100%)` :
            finalConfig.cor,
          border: `1px solid ${finalConfig.cor}`,
          borderRadius: 2,
                  overflow: 'hidden',
        position: 'relative',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08)',
        '&:hover': {
          boxShadow: `0 4px 12px ${finalConfig.cor}20, 0 2px 4px ${finalConfig.cor}16`
        }
        }}
      >
        <CardContent sx={{ 
          height: '100%', 
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          p: 2,
          position: 'relative',
          '&:last-child': { pb: 2 }
        }}>
          {/* Ícone posicionado no canto superior direito */}
          {finalConfig.showIcon && (
            <Box sx={{ 
              position: 'absolute', 
              top: 12, 
              right: 12,
              opacity: 0.6
            }}>
              {renderIcon()}
            </Box>
          )}

          {/* Título */}
          <Typography 
            variant="caption" 
            sx={{ 
              mb: 1, 
              fontSize: getTitleFontSize(),
              fontWeight: 600,
              textTransform: 'uppercase',
              letterSpacing: 0.5,
              textAlign: 'center',
              maxWidth: '80%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              color: finalConfig.corFonte,
              opacity: 0.9
            }}
          >
            {simulatedKPI.titulo}
          </Typography>
          
          {/* Valor principal */}
          <Typography 
            variant="h3" 
            sx={{ 
              fontWeight: 'bold',
              color: finalConfig.corFonte,
              fontSize: getResponsiveFontSize(),
              lineHeight: 1.1,
              textAlign: 'center',
              wordBreak: 'break-all'
            }}
          >
            {simulatedKPI.formattedValue}
          </Typography>
        </CardContent>
      </Card>
    )
  }

  // Criar gradiente se habilitado
  const backgroundStyle = finalConfig.gradiente ? 
    `linear-gradient(135deg, ${finalConfig.cor} 0%, ${finalConfig.cor}80 100%)` :
    finalConfig.cor

  return (
    <Card 
      ref={containerRef}
      sx={{ 
        height: '100%', 
        background: backgroundStyle,
        border: `1px solid ${finalConfig.cor}`,
        borderRadius: 2,
        overflow: 'hidden',
        position: 'relative',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08)',
        '&:hover': {
          boxShadow: `0 4px 12px ${finalConfig.cor}20, 0 2px 4px ${finalConfig.cor}16`
        }
      }}
    >
      <CardContent sx={{ 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        p: 2,
        position: 'relative',
        '&:last-child': { pb: 2 }
      }}>
        {/* Ícone posicionado no canto superior direito */}
        {finalConfig.showIcon && (
          <Box sx={{ 
            position: 'absolute', 
            top: 12, 
            right: 12,
            opacity: 0.6
          }}>
            {renderIcon()}
          </Box>
        )}

        {/* Título */}
        {finalConfig.showTitle && (processedData?.titulo || processedData?.medida) && (
          <Typography 
            variant="caption" 
            sx={{ 
              mb: 1, 
              fontSize: getTitleFontSize(),
              fontWeight: 600,
              textTransform: 'uppercase',
              letterSpacing: 0.5,
              textAlign: 'center',
              maxWidth: '80%',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              color: finalConfig.corFonte,
              opacity: 0.9
            }}
          >
            {processedData.titulo || processedData.medida}
          </Typography>
        )}
        
        {/* Valor principal */}
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          <Typography 
            variant="h3" 
            sx={{ 
              fontWeight: 'bold',
              color: finalConfig.corFonte,
              fontSize: getResponsiveFontSize(),
              lineHeight: 1.1,
              textAlign: 'center',
              wordBreak: 'break-all'
            }}
          >
            {processedData.formattedValue}
          </Typography>
          
          {finalConfig.showTrend && getTrendIcon(processedData.trend)}
        </Box>
        
        {/* Informação de tendência */}
        {finalConfig.showTrend && processedData.trend && (
          <Typography 
            variant="caption" 
            sx={{ 
              mt: 0.5,
              color: processedData.trend.direction === 'up' ? 'success.main' : 
                     processedData.trend.direction === 'down' ? 'error.main' : 'grey.500',
              fontSize: `${Math.max(containerSize.height * 0.05, 10)}px`,
              fontWeight: 500
            }}
          >
            {processedData.trend.direction === 'up' ? '↗' : 
             processedData.trend.direction === 'down' ? '↘' : '→'} 
            {processedData.trend.percentage.toFixed(1)}%
          </Typography>
        )}
      </CardContent>
    </Card>
  )
}

// Exportar também as opções para uso em formulários
export { ICON_GALLERY, COLOR_PALETTE }
export default CustomKPI 