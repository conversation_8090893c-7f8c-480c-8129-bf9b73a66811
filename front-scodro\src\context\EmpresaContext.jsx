import React, { createContext, useContext, useState, useEffect } from 'react'
import { empresaService } from '@services/api'

const EmpresaContext = createContext()

export const useEmpresa = () => {
  const context = useContext(EmpresaContext)
  if (!context) {
    throw new Error('useEmpresa deve ser usado dentro de EmpresaProvider')
  }
  return context
}

export const EmpresaProvider = ({ children }) => {
  const [empresaSelecionada, setEmpresaSelecionada] = useState(null)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const [empresasExpandidas, setEmpresasExpandidas] = useState({})
  const [empresas, setEmpresas] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Carregar empresas do banco de dados
  const carregarEmpresas = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Carregar empresas COM objetos/apps para ter dados completos
      const response = await empresaService.listarEmpresas(true) 
      
      //console.log('🔍 Resposta da API empresas:', response)
      
      if (response.success && response.data) {
        const empresasAtivas = response.data.filter(empresa => empresa.ativo)
        //console.log('🏢 Empresas ativas carregadas:', empresasAtivas)
        
        setEmpresas(empresasAtivas)
        
        // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira
        if (!empresaSelecionada && empresasAtivas.length > 0) {
          // Usar 'id' ao invés de '_id' baseado na estrutura dos dados
          const primeiraEmpresa = empresasAtivas[0].id || empresasAtivas[0]._id
          //console.log('🎯 Selecionando primeira empresa:', primeiraEmpresa, empresasAtivas[0])
          setEmpresaSelecionada(primeiraEmpresa)
          setEmpresasExpandidas({ [primeiraEmpresa]: true })
        }
      } else {
        setEmpresas([])
        console.warn('⚠️ Nenhuma empresa encontrada ou resposta inválida')
      }
    } catch (error) {
      console.error('❌ Erro ao carregar empresas:', error)
      setError(error.message)
      setEmpresas([])
    } finally {
      setLoading(false)
    }
  }

  // Carregar estado do localStorage
  useEffect(() => {
    const savedEmpresa = localStorage.getItem('empresaSelecionada')
    const savedSidebarState = localStorage.getItem('sidebarCollapsed')
    const savedExpandedState = localStorage.getItem('empresasExpandidas')

    if (savedEmpresa) {
      setEmpresaSelecionada(savedEmpresa)
    }
    
    if (savedSidebarState) {
      setSidebarCollapsed(JSON.parse(savedSidebarState))
    }

    if (savedExpandedState) {
      setEmpresasExpandidas(JSON.parse(savedExpandedState))
    }

    // Carregar empresas do banco
    carregarEmpresas()
  }, [])

  // Salvar estado no localStorage
  useEffect(() => {
    if (empresaSelecionada) {
      localStorage.setItem('empresaSelecionada', empresaSelecionada)
    }
  }, [empresaSelecionada])

  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(sidebarCollapsed))
  }, [sidebarCollapsed])

  useEffect(() => {
    localStorage.setItem('empresasExpandidas', JSON.stringify(empresasExpandidas))
  }, [empresasExpandidas])

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleEmpresaExpansion = (empresaId) => {
    setEmpresasExpandidas(prev => ({
      ...prev,
      [empresaId]: !prev[empresaId]
    }))
  }

  const selecionarEmpresa = (empresaId) => {
    setEmpresaSelecionada(empresaId)
    // Expandir automaticamente a empresa selecionada
    setEmpresasExpandidas(prev => ({
      ...prev,
      [empresaId]: true
    }))
  }

  // Função para recarregar empresas (útil após criar/editar)
  const recarregarEmpresas = async () => {
    await carregarEmpresas()
  }

  // Obter empresa atual - buscar por 'id' e '_id' para compatibilidade
  const empresaAtual = empresas.find(emp => 
    (emp.id === empresaSelecionada) || (emp._id === empresaSelecionada)
  )

  const value = {
    empresaSelecionada,
    selecionarEmpresa,
    sidebarCollapsed,
    toggleSidebar,
    empresasExpandidas,
    toggleEmpresaExpansion,
    empresas,
    empresaAtual,
    loading,
    error,
    recarregarEmpresas
  }

  return (
    <EmpresaContext.Provider value={value}>
      {children}
    </EmpresaContext.Provider>
  )
} 