const express = require('express');
const router = express.Router();
const {
  obterConfigAtiva,
  atualizarConfig,
  validarConfig,
  resetarConfig
} = require('../controllers/configMashupController');

// Middleware para verificar conexão com banco
const checkDatabase = require('../middleware/checkDatabase');

// Aplicar middleware de verificação do banco em todas as rotas
router.use(checkDatabase);

// GET /api/config - Obter configuração ativa
router.get('/', obterConfigAtiva);

// PUT /api/config/:id? - Atualizar configuração (id opcional, padrão: 'default')
router.put('/:id?', atualizarConfig);

// POST /api/config/validar - Validar configuração sem salvar
router.post('/validar', validarConfig);

// POST /api/config/:id?/reset - Resetar configuração para padrão
router.post('/:id?/reset', resetarConfig);

module.exports = router; 