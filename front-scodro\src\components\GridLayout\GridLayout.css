/* Customizações para o React Grid Layout */

.react-grid-layout {
  position: relative;
  background: transparent;
  padding: 0;
  transition: margin 0.3s ease;
}

/* Configurações básicas para grid items */
.react-grid-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: 0 0;
  border-radius: 8px;
  overflow: visible;
}

/* Estado durante o drag */
.react-grid-item.react-draggable-dragging {
  z-index: 1000 !important;
  opacity: 0.9;
  transition: none !important; /* Sem transições durante drag */
  cursor: grabbing !important;
}

/* Placeholder simplificado */
.react-grid-item.react-grid-placeholder {
  background: rgba(102, 126, 234, 0.15);
  border: 2px dashed rgba(102, 126, 234, 0.5);
  border-radius: 8px;
  opacity: 0.8;
  z-index: 2;
  user-select: none;
}

/* Handle de resize mais visível */
.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50% 0 8px 0;
  cursor: se-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.react-grid-item > .react-resizable-handle::after {
  content: '⋰';
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.react-grid-item:hover > .react-resizable-handle {
  opacity: 0.8;
}

.react-grid-item > .react-resizable-handle:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Cards do grid simplificados */
.grid-item-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  /* border-radius removido - será controlado pelo sistema centralizado */
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  /* Usar sistema de sombras centralizado */
  overflow: visible;
  padding: 0;
}

.layout.edit-mode .grid-item-card {
  cursor: grab;
}

.layout.edit-mode .grid-item-card:active {
  cursor: grabbing;
}

/* Hover removido - será controlado pelo sistema centralizado */

/* Estados especiais */
.grid-item-card.has-error {
  border-color: rgba(255, 152, 0, 0.3);
  background: rgba(255, 152, 0, 0.02);
}

.grid-item-card.has-error:hover {
  border-color: rgba(255, 152, 0, 0.5);
}

.grid-item-card.no-data {
  border-color: rgba(158, 158, 158, 0.3);
  background: rgba(158, 158, 158, 0.02);
}

.grid-item-card.no-data:hover {
  border-color: rgba(158, 158, 158, 0.5);
}

/* Controles do item simplificados */
.grid-item-controls {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 9999; /* ✅ Aumentado para ficar acima dos elementos dos gráficos */
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-item-card:hover .grid-item-controls {
  opacity: 1;
}

.grid-control-button {
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  padding: 0 !important;
  background: rgba(0, 0, 0, 0.7) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  backdrop-filter: blur(8px);
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.16) !important;
  z-index: 10000 !important; /* ✅ Garantir que os botões fiquem sempre visíveis */
  position: relative !important;
}

.grid-control-button:hover {
  background: rgba(0, 0, 0, 0.9) !important;
}

.grid-control-button.drag-handle {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  cursor: grab !important;
}

.grid-control-button.drag-handle:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
  cursor: grab !important;
}

.grid-control-button.drag-handle:active {
  cursor: grabbing !important;
}

.grid-control-button .MuiSvgIcon-root {
  font-size: 18px !important;
}

/* CardContent básico */
.grid-item-card .MuiCardContent-root {
  padding: 0 !important;
  margin: 0 !important;
  height: 100% !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  /* border-radius removido - será controlado pelo sistema centralizado */
}

/* QlikObject ocupando todo o espaço */
.grid-item-card .MuiBox-root {
  --height: 100% !important;
  /* border-radius removido - será controlado pelo sistema centralizado */
  --overflow: hidden !important;
}

/* Modo de visualização - limpo */
.layout.view-mode .react-grid-item {
  cursor: default !important;
}

.layout.view-mode .react-resizable-handle {
  display: none !important;
}

.layout.view-mode .grid-item-controls {
  display: none !important;
}

.layout.view-mode .grid-item-card {
  cursor: default !important;
}

.layout.view-mode .object-type-chip {
  display: none !important;
}

.layout.view-mode .grid-item-title {
  display: none !important;
}

/* Proteção contra elementos arrastáveis */
.no-drag {
  pointer-events: auto;
  user-select: auto;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  touch-action: auto;
}

/* Drag handle específico */
.drag-handle {
  cursor: grab !important;
  touch-action: none;
  user-select: none;
}

.drag-handle:active {
  cursor: grabbing !important;
}

/* Melhorias de responsividade */
@media (max-width: 768px) {
  .react-grid-item > .react-resizable-handle {
    width: 24px;
    height: 24px;
  }
  
  .grid-item-controls {
    top: 4px;
    right: 4px;
    padding: 2px;
  }

  .grid-control-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
  }

  .grid-control-button .MuiSvgIcon-root {
    font-size: 16px !important;
  }

  .grid-item-card .MuiCardContent-root {
    padding: 12px !important;
  }
  
  /* Layouts de tablet - 2 colunas */
  .react-grid-layout {
    margin: 0 8px;
  }
}

/* Responsividade para mobile (xs) */
@media (max-width: 480px) {
  .grid-item-controls {
    top: 2px;
    right: 2px;
    padding: 1px;
  }

  .grid-control-button {
    width: 24px !important;
    height: 24px !important;
    min-width: 24px !important;
  }

  .grid-control-button .MuiSvgIcon-root {
    font-size: 14px !important;
  }

  .grid-item-card .MuiCardContent-root {
    padding: 8px !important;
  }
  
  /* Layout mobile - empilhado */
  .react-grid-layout {
    margin: 0 4px;
  }
  
  /* Handles de resize menores em mobile */
  .react-grid-item > .react-resizable-handle {
    width: 20px;
    height: 20px;
  }
}

/* Responsividade para telas muito pequenas (xxs) */
@media (max-width: 320px) {
  .grid-item-card .MuiCardContent-root {
    padding: 4px !important;
  }
  
  .grid-control-button {
    width: 20px !important;
    height: 20px !important;
    min-width: 20px !important;
  }
  
  .grid-control-button .MuiSvgIcon-root {
    font-size: 12px !important;
  }
}

/* Estados de loading e vazio */
.grid-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  animation: fadeIn 0.5s ease;
}

.grid-empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
  border-radius: 16px !important;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  border: 2px dashed rgba(102, 126, 234, 0.3) !important;
  animation: fadeIn 0.5s ease;
}

/* Header do grid */
.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.grid-title {
  color: #1976d2;
  font-weight: 600 !important;
  font-size: 1.25rem !important;
}

.add-object-button {
  border-radius: 12px !important;
  text-transform: none !important;
  font-weight: 600 !important;
  padding: 12px 24px !important;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.16), 0 1px 3px rgba(25, 118, 210, 0.12) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.add-object-button:hover {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.20), 0 2px 4px rgba(25, 118, 210, 0.16) !important;
  transform: translateY(-2px);
}

/* Dialog customizado */
.add-object-dialog .MuiDialog-paper {
  border-radius: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08), 0 4px 8px rgba(0, 0, 0, 0.06) !important;
}

/* Animação de fade in global */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tema escuro */
@media (prefers-color-scheme: dark) {
  .grid-item-card {
    background: rgba(30, 30, 30, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .grid-item-card:hover {
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.12), 0 2px 4px rgba(102, 126, 234, 0.08);
  }

  .grid-empty-state {
    background: linear-gradient(135deg, #1e1e1e 0%, #2a2a2a 100%) !important;
    color: #ccc;
  }
}

/* Melhorar visibilidade dos controles em telas pequenas */
@media (max-width: 768px) {
  .grid-item-controls {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 9999; /* ✅ Garantir z-index alto em mobile também */
  }
  
  .grid-control-button {
    background: rgba(255, 255, 255, 0.9) !important;
    color: black !important;
    z-index: 10000 !important; /* ✅ Garantir z-index alto em mobile também */
  }
  
  .grid-control-button.drag-handle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
  }
} 