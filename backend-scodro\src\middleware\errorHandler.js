const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log do erro
  console.error('❌ Erro:', err);

  // Erro de validação do Mongoose
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = {
      statusCode: 400,
      message: 'Erro de validação',
      details: message
    };
  }

  // Erro de duplicação (MongoDB)
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];
    error = {
      statusCode: 409,
      message: `Recurso duplicado: ${field} '${value}' já existe`
    };
  }

  // Erro de cast do Mongoose (ID inválido)
  if (err.name === 'CastError') {
    error = {
      statusCode: 400,
      message: 'ID inválido fornecido'
    };
  }

  // Erro de JSON malformado
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    error = {
      statusCode: 400,
      message: 'JSON malformado na requisição'
    };
  }

  // Erro de limite de tamanho do body
  if (err.type === 'entity.too.large') {
    error = {
      statusCode: 413,
      message: 'Payload muito grande'
    };
  }

  // Erro de timeout
  if (err.code === 'ETIMEDOUT') {
    error = {
      statusCode: 408,
      message: 'Timeout na requisição'
    };
  }

  // Erro de conexão com banco
  if (err.name === 'MongoNetworkError') {
    error = {
      statusCode: 503,
      message: 'Erro de conexão com o banco de dados'
    };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message || 'Erro interno do servidor',
    ...(error.details && { details: error.details }),
    ...(process.env.NODE_ENV === 'development' && { 
      stack: err.stack,
      originalError: err 
    })
  });
};

module.exports = errorHandler; 