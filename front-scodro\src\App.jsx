import React, { useEffect } from 'react'
import { HashRouter as Router, Routes, Route, useLocation, useNavigate } from 'react-router-dom'
import { ThemeProvider, createTheme, CssBaseline } from '@mui/material'
import { EmpresaProvider, useEmpresa } from '@context/EmpresaContext'
import { QlikSelectionProvider } from '@contexts/QlikSelectionContext'
import Layout from '@components/Layout/Layout'
import ErrorBoundary from '@components/ErrorBoundary'
import Dashboard from '@pages/Dashboard'
import Vendas from '@pages/Vendas'
import Helpdesk from '@pages/Helpdesk'
import Financeiro from '@pages/Financeiro'
import Configuracoes from '@pages/Configuracoes'
import DynamicPage from '@components/DynamicPage/DynamicPage'
import qlikService from '@services/qlik'
import SnackbarProvider from '@components/SnackbarProvider'

// Tema personalizado LIMPO - SEM forçagem de fontes
const theme = createTheme({
  palette: {
    primary: {
      main: '#667eea',
      dark: '#764ba2'
    },
    secondary: {
      main: '#f50057'
    },
    background: {
      default: '#f5f5f5'
    }
  },
  typography: {
    // Apenas configuração básica do Material-UI - SEM forçagem
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
  },
  components: {
    // Configurações mínimas - SEM forçagem de fontes
    MuiCard: {
      styleOverrides: {
        root: {
          // Remover box-shadow padrão para evitar conflitos
          boxShadow: 'none',
          // Aplicar uma sombra sutil e consistente
          border: '1px solid rgba(0, 0, 0, 0.08)',
          transition: 'all 0.2s ease-in-out'
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          // Padronizar box-shadow para todos os Papers
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08)',
          transition: 'box-shadow 0.2s ease-in-out'
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none'
        }
      }
    }
  }
})

// Página sobre
const Sobre = () => (
  <div>
    <h2>ℹ️ Sobre</h2>
    <p>Mashup React moderno para Qlik Sense</p>
    <p>Compatível com Qlik Cloud e Qlik Enterprise</p>
    <p>Versão: 1.0.0</p>
    <br />
    <h3>🆕 Novas Funcionalidades:</h3>
    <ul>
      <li>✅ Sidebar colapsável para melhor aproveitamento do espaço</li>
      <li>✅ Sistema hierárquico de empresas</li>
      <li>✅ Gerenciamento de configurações por empresa</li>
      <li>✅ Interface moderna e responsiva</li>
    </ul>
  </div>
)

// Página 404
const NotFound = () => (
  <div style={{ textAlign: 'center', padding: '50px' }}>
    <h2>⚠️ Página não encontrada</h2>
    <p>A página que você está procurando não existe.</p>
  </div>
)

function AutoRedirectOnRoot() {
  const { empresas, loading } = useEmpresa();
  const location = useLocation();
  const navigate = useNavigate();

  React.useEffect(() => {
    if (!loading && location.pathname === '/') {
      if (empresas && empresas.length > 0) {
        const empresa = empresas[0];
        if (empresa.paginas && empresa.paginas.length > 0) {
          navigate(`/${empresa.id || empresa._id}/${empresa.paginas[0].chave}`, { replace: true });
        }
      }
    }
  }, [loading, empresas, location, navigate]);

  return null;
}

function App() {
  useEffect(() => {
    // Apenas inicializar serviços - SEM forçagem de fontes
    qlikService.initialize().catch(console.error)
  }, [])

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <SnackbarProvider>
          <EmpresaProvider>
            <QlikSelectionProvider>
              <Router
                future={{
                  v7_startTransition: true,
                  v7_relativeSplatPath: true
                }}
              >
                <ErrorBoundary>
                  <Layout>
                    {/* Redirecionamento automático da raiz */}
                    <AutoRedirectOnRoot />
                    <Routes>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/dashboard" element={<Dashboard />} />
                      <Route path="/vendas" element={<Vendas />} />
                      <Route path="/helpdesk" element={<Helpdesk />} />
                      <Route path="/financeiro" element={<Financeiro />} />
                      <Route path="/configuracoes" element={<Configuracoes />} />
                      <Route path="/sobre" element={<Sobre />} />
                      
                      {/* Rotas parametrizadas por empresa - URLs simplificadas */}
                      <Route path="/:empresaId" element={<Dashboard />} />
                      <Route path="/:empresaId/dashboard" element={<Dashboard />} />
                      <Route path="/:empresaId/vendas" element={<Vendas />} />
                      <Route path="/:empresaId/helpdesk" element={<Helpdesk />} />
                      <Route path="/:empresaId/financeiro" element={<Financeiro />} />
                      <Route path="/:empresaId/:chave" element={<DynamicPage />} />
                      
                      {/* Rotas dinâmicas (compatibilidade) */}
                      <Route path="/pagina/:chave" element={<DynamicPage />} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Layout>
                </ErrorBoundary>
              </Router>
            </QlikSelectionProvider>
          </EmpresaProvider>
        </SnackbarProvider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App 