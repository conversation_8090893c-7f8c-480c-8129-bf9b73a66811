const mongoose = require('mongoose');

const empresaSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z0-9-_]+$/, 'ID deve conter apenas letras minúsculas, números, hífens e underscores']
  },
  nome: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  cor: {
    type: String,
    required: true,
    match: [/^#[0-9A-Fa-f]{6}$/, 'Cor deve estar no formato hexadecimal (#RRGGBB)'],
    default: '#667eea'
  },
  logo: {
    type: String,
    trim: true,
    maxlength: 100000,
    default: null,
    validate: {
      validator: function(v) {
        if (!v) return true; // Logo é opcional
        // Validar se é uma URL válida ou base64
        const urlRegex = /^https?:\/\/.+/;
        const base64Regex = /^data:image\/(png|jpg|jpeg|gif|svg\+xml);base64,/;
        return urlRegex.test(v) || base64Regex.test(v);
      },
      message: 'Logo deve ser uma URL válida ou imagem em base64'
    }
  },
  ativo: {
    type: Boolean,
    default: true
  },
  // Metadados
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String,
    default: 'system'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Índices para performance
empresaSchema.index({ nome: 1 });
empresaSchema.index({ ativo: 1 });

// Virtual para apps relacionados
empresaSchema.virtual('apps', {
  ref: 'App',
  localField: 'id',
  foreignField: 'empresaId'
});

// Virtual para objetos relacionados (através dos apps)
empresaSchema.virtual('objetos', {
  ref: 'Objeto',
  localField: 'id',
  foreignField: 'empresaId'
});

// Virtual para páginas relacionadas
empresaSchema.virtual('paginas', {
  ref: 'Pagina',
  localField: 'id',
  foreignField: 'empresaId'
});

// Middleware para atualizar updatedAt
empresaSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Método para obter configuração completa com apps, objetos e páginas
empresaSchema.methods.getConfigCompleta = async function() {
  await this.populate([
    {
      path: 'apps',
      populate: {
        path: 'objetos',
        model: 'Objeto'
      }
    },
    {
      path: 'paginas',
      match: { ativo: true },
      options: { sort: { ordem: 1 } }
    }
  ]);
  
  const config = {
    id: this.id,
    nome: this.nome,
    cor: this.cor,
    logo: this.logo,
    ativo: this.ativo,
    apps: [],
    paginas: []
  };

  // Organizar apps com seus objetos
  if (this.apps) {
    config.apps = this.apps.map(app => ({
      _id: app._id,
      appId: app.appId,
      nome: app.nome,
      categoria: app.categoria,
      ordem: app.ordem,
      objetos: app.objetos || []
    }));
  }

  // Organizar páginas
  if (this.paginas) {
    config.paginas = this.paginas.map(pagina => ({
      _id: pagina._id,
      chave: pagina.chave,
      titulo: pagina.titulo,
      descricao: pagina.descricao,
      icone: pagina.icone,
      rota: pagina.rota,
      ordem: pagina.ordem,
      ativo: pagina.ativo,
      configuracao: pagina.configuracao
    }));
  }

  return config;
};

// Método estático para buscar por ID customizado
empresaSchema.statics.findByCustomId = function(customId) {
  return this.findOne({ id: customId });
};

module.exports = mongoose.model('Empresa', empresaSchema); 