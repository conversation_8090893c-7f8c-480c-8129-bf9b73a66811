# 📋 Instruções Detalhadas de Configuração

## 🔧 Configuração Inicial

### 1. Obter IDs dos Apps e Objetos

#### No Qlik Sense Enterprise:
1. Abra o Qlik Sense Hub
2. Acesse o app desejado
3. Para obter o **App ID**:
   - Vá para a URL do app: `https://seu-servidor/sense/app/[APP_ID]`
   - O APP_ID está na URL após `/app/`

4. Para obter **Object IDs**:
   - Abra o app no modo de edição
   - Clique com botão direito no objeto → "Propriedades"
   - O Object ID está nas propriedades avançadas

#### No Qlik Cloud:
1. Acesse o Qlik Cloud
2. Abra o app desejado
3. Para obter o **App ID**:
   - Vá para a URL do app: `https://seu-tenant.qlikcloud.com/sense/app/[APP_ID]`
   - O APP_ID está na URL após `/app/`

4. Para obter **Object IDs**:
   - Mesmo processo do Enterprise

### 2. Configurar Web Integration ID (Qlik Cloud)

1. Acesse o **Management Console** do Qlik Cloud
2. Vá para **Web integrations**
3. Clique em **Create new**
4. Configure:
   - **Name**: Nome do seu mashup
   - **Whitelist origins**: Domínio onde o mashup será hospedado
   - **CSP origins**: Mesmo domínio
5. Copie o **Web Integration ID** gerado

### 3. Editar Configurações

#### Arquivo: `src/config/apps.js`
```javascript
export const APP_IDS = {
  dashboard: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',  // Substitua pelos IDs reais
  vendas: 'b2c3d4e5-f6g7-8901-bcde-f23456789012',
  helpdesk: 'c3d4e5f6-g7h8-9012-cdef-345678901234'
}

export const OBJECT_IDS = {
  kpi1: 'QmJqbF',        // Substitua pelos IDs reais dos objetos
  kpi2: 'pPmJqbF',
  chart1: 'mBshXm',
  table1: 'HdkJpq'
}
```

#### Arquivo: `src/config/qlik.js`
```javascript
export const cloudConfig = {
  host: 'your-tenant.us.qlikcloud.com',           // Seu domínio do Qlik Cloud
  webIntegrationId: 'a1b2c3d4-e5f6-7890-abcd'    // Seu Web Integration ID
}
```

## 🚀 Testando a Configuração

### Modo Desenvolvimento
```bash
npm run dev
```
- Acesse `http://localhost:3000`
- Você deve ver placeholders coloridos no lugar dos objetos Qlik
- Verifique o console para logs de configuração

### Modo Produção (Teste Local)
```bash
npm run build:cloud
npm run preview
```
- Acesse `http://localhost:3000`
- Se configurado corretamente, tentará conectar ao Qlik

## 🔍 Verificação de Problemas

### Console do Navegador
Abra as **Ferramentas do Desenvolvedor** (F12) e verifique:

#### Logs Esperados em Desenvolvimento:
```
🔧 Modo desenvolvimento: Qlik Service simulado
🔧 Mock require.config: {...}
🔧 Modo desenvolvimento: Simulando abertura do app [APP_ID]
```

#### Logs Esperados em Produção:
```
🔗 Conectando ao Qlik Sense (cloud)...
⚙️ Configurando require.js: {...}
✅ Qlik Service inicializado com sucesso
📱 Abrindo app: [APP_ID]
📊 Carregando objeto: [OBJECT_ID]
```

### Erros Comuns e Soluções

#### ❌ "App não encontrado"
- **Causa**: App ID incorreto ou app não publicado/compartilhado
- **Solução**: Verifique o ID e permissões do app

#### ❌ "Object não encontrado"
- **Causa**: Object ID incorreto ou objeto não existe
- **Solução**: Verifique o ID do objeto no app

#### ❌ "CORS Error" (Cloud)
- **Causa**: Domínio não autorizado no Web Integration
- **Solução**: Adicione o domínio na whitelist do Web Integration

#### ❌ "require is not defined"
- **Causa**: Qlik API não carregou
- **Solução**: Verifique a conectividade com o servidor Qlik

## 📝 Exemplo de Configuração Completa

### Para Qlik Cloud:
```javascript
// src/config/qlik.js
export const cloudConfig = {
  host: 'mycompany.us.qlikcloud.com',
  webIntegrationId: '12345678-1234-1234-1234-123456789012'
}

// src/config/apps.js
export const APP_IDS = {
  dashboard: 'abcd1234-5678-90ef-ghij-klmnopqrstuv'
}

export const OBJECT_IDS = {
  kpi1: 'QmJqbF',
  chart1: 'mBshXm'
}
```

### Para Qlik Enterprise:
```javascript
// src/config/apps.js (mesmo formato)
export const APP_IDS = {
  dashboard: 'abcd1234-5678-90ef-ghij-klmnopqrstuv'
}

// A configuração de conexão é automática para Enterprise
```

## 🎯 Próximos Passos

1. ✅ Configure os IDs dos apps e objetos
2. ✅ Configure o Web Integration ID (se Cloud)
3. ✅ Teste em modo desenvolvimento
4. ✅ Teste em modo produção
5. ✅ Faça o deploy conforme instruções do README

## 📞 Suporte

Se encontrar problemas:
1. Verifique os logs do console
2. Confirme as configurações de IDs
3. Teste a conectividade com o Qlik Sense
4. Verifique as permissões dos apps e objetos 