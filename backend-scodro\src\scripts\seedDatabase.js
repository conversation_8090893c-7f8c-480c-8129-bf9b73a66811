const mongoose = require('mongoose');
require('dotenv').config();

const Empresa = require('../models/Empresa');
const Objeto = require('../models/Objeto');

// Dados de exemplo
const empresasExemplo = [
  {
    id: 'empresa-exemplo',
    nome: 'Empresa Exemplo',
    cor: '#667eea',
    appId: 'SEU_APP_DASHBOARD_EMPRESA_EXEMPLO',
    qlikConfig: {
      webIntegrationId: 'SEU_WEB_INTEGRATION_ID',
      serverUrl: '',
      prefix: ''
    }
  },
  {
    id: 'empresa-teste-2',
    nome: 'Empresa Teste 2',
    cor: '#764ba2',
    appId: 'SEU_APP_DASHBOARD_EMPRESA_TESTE_2',
    qlikConfig: {
      webIntegrationId: 'SEU_WEB_INTEGRATION_ID_2',
      serverUrl: '',
      prefix: ''
    }
  }
];

const objetosExemplo = [
  // Objetos para empresa-exemplo
  {
    empresaId: 'empresa-exemplo',
    chave: 'kpi1',
    objectId: 'SEU_OBJETO_KPI_1_EMPRESA_EXEMPLO',
    nome: 'KPI Vendas Mensais',
    tipo: 'kpi',
    categoria: 'dashboard',
    descricao: 'KPI mostrando vendas do mês atual',
    ordem: 1
  },
  {
    empresaId: 'empresa-exemplo',
    chave: 'kpi2',
    objectId: 'SEU_OBJETO_KPI_2_EMPRESA_EXEMPLO',
    nome: 'KPI Clientes Ativos',
    tipo: 'kpi',
    categoria: 'dashboard',
    descricao: 'Número de clientes ativos',
    ordem: 2
  },
  {
    empresaId: 'empresa-exemplo',
    chave: 'chart1',
    objectId: 'SEU_OBJETO_CHART_1_EMPRESA_EXEMPLO',
    nome: 'Gráfico Vendas por Região',
    tipo: 'chart',
    categoria: 'vendas',
    descricao: 'Gráfico de barras com vendas por região',
    configuracao: {
      altura: '400px',
      largura: '100%'
    },
    ordem: 1
  },
  {
    empresaId: 'empresa-exemplo',
    chave: 'table1',
    objectId: 'SEU_OBJETO_TABLE_1_EMPRESA_EXEMPLO',
    nome: 'Tabela Tickets Helpdesk',
    tipo: 'table',
    categoria: 'helpdesk',
    descricao: 'Tabela com tickets de suporte',
    ordem: 1
  },
  
  // Objetos para empresa-teste-2
  {
    empresaId: 'empresa-teste-2',
    chave: 'kpi1',
    objectId: 'SEU_OBJETO_KPI_1_EMPRESA_TESTE_2',
    nome: 'KPI Receita',
    tipo: 'kpi',
    categoria: 'financeiro',
    descricao: 'KPI de receita total',
    ordem: 1
  },
  {
    empresaId: 'empresa-teste-2',
    chave: 'chart1',
    objectId: 'SEU_OBJETO_CHART_1_EMPRESA_TESTE_2',
    nome: 'Gráfico Evolução Financeira',
    tipo: 'chart',
    categoria: 'financeiro',
    descricao: 'Gráfico de linha com evolução financeira',
    ordem: 2
  }
];

async function seedDatabase() {
  try {
    // Conectar ao MongoDB (sem opções deprecated)
    await mongoose.connect(process.env.MONGO_URI);

    console.log('🍃 Conectado ao MongoDB');

    // Limpar dados existentes (opcional)
    const limparDados = process.argv.includes('--clear');
    if (limparDados) {
      console.log('🧹 Limpando dados existentes...');
      await Objeto.deleteMany({});
      await Empresa.deleteMany({});
      console.log('✅ Dados limpos');
    }

    // Inserir empresas
    console.log('📊 Inserindo empresas...');
    for (const empresaData of empresasExemplo) {
      const empresaExistente = await Empresa.findByCustomId(empresaData.id);
      if (!empresaExistente) {
        const empresa = new Empresa(empresaData);
        await empresa.save();
        console.log(`✅ Empresa criada: ${empresa.nome}`);
      } else {
        console.log(`⚠️ Empresa já existe: ${empresaData.nome}`);
      }
    }

    // Inserir objetos
    console.log('🎯 Inserindo objetos...');
    for (const objetoData of objetosExemplo) {
      const objetoExistente = await Objeto.findOne({
        empresaId: objetoData.empresaId,
        chave: objetoData.chave
      });

      if (!objetoExistente) {
        const objeto = new Objeto(objetoData);
        await objeto.save();
        console.log(`✅ Objeto criado: ${objeto.nome} (${objeto.empresaId})`);
      } else {
        console.log(`⚠️ Objeto já existe: ${objetoData.nome} (${objetoData.empresaId})`);
      }
    }

    // Verificar dados inseridos
    const totalEmpresas = await Empresa.countDocuments();
    const totalObjetos = await Objeto.countDocuments();

    console.log('\n📈 Resumo:');
    console.log(`📊 Total de empresas: ${totalEmpresas}`);
    console.log(`🎯 Total de objetos: ${totalObjetos}`);

    // Exibir configuração de exemplo
    console.log('\n🔧 Configuração de exemplo para o frontend:');
    const empresas = await Empresa.find().populate('objetos');
    for (const empresa of empresas) {
      const config = await empresa.getConfigCompleta();
      console.log(`\n${empresa.nome}:`);
      console.log(JSON.stringify(config, null, 2));
    }

    console.log('\n✅ Seed concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro no seed:', error);
    
    if (error.code === 8000) {
      console.log('\n🔧 Dicas para resolver erro de autenticação:');
      console.log('1. Verifique se o usuário e senha estão corretos');
      console.log('2. Confirme se o usuário tem permissões no banco SCODRO');
      console.log('3. Verifique se seu IP está na whitelist do MongoDB Atlas');
      console.log('4. Teste a conexão no MongoDB Compass');
    }
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Desconectado do MongoDB');
    process.exit(0);
  }
}

// Executar seed
if (require.main === module) {
  console.log('🌱 Iniciando seed do banco de dados...');
  console.log('💡 Use --clear para limpar dados existentes');
  seedDatabase();
}

module.exports = seedDatabase; 