// Configuração de Empresas e seus Apps/Objetos do Qlik Sense
// Estrutura hierárquica: Empresa > Dashboard/Vendas/Helpdesk/Financeiro

export const EMPRESAS = {
  'empresa-exemplo': {
    id: 'empresa-exemplo',
    nome: 'Empresa Exemplo',
    cor: '#667eea',
    apps: {
      dashboard: 'SEU_APP_DASHBOARD_EMPRESA_EXEMPLO',
      vendas: 'SEU_APP_VENDAS_EMPRESA_EXEMPLO',
      financeiro: 'SEU_APP_FINANCEIRO_EMPRESA_EXEMPLO',
      helpdesk: 'SEU_APP_HELPDESK_EMPRESA_EXEMPLO'
    },
    objetos: {
      // KPIs
      kpi1: 'SEU_OBJETO_KPI_1_EMPRESA_EXEMPLO',
      kpi2: 'SEU_OBJETO_KPI_2_EMPRESA_EXEMPLO',
      kpi3: 'SEU_OBJETO_KPI_3_EMPRESA_EXEMPLO',
      
      // Gráficos
      chart1: 'SEU_OBJETO_CHART_1_EMPRESA_EXEMPLO',
      chart2: 'SEU_OBJETO_CHART_2_EMPRESA_EXEMPLO',
      barChart: 'SEU_OBJETO_BAR_CHART_EMPRESA_EXEMPLO',
      lineChart: 'SEU_OBJETO_LINE_CHART_EMPRESA_EXEMPLO',
      
      // Tabelas
      table1: 'SEU_OBJETO_TABLE_1_EMPRESA_EXEMPLO',
      table2: 'SEU_OBJETO_TABLE_2_EMPRESA_EXEMPLO',
      
      // Filtros
      filter1: 'SEU_OBJETO_FILTER_1_EMPRESA_EXEMPLO',
      filter2: 'SEU_OBJETO_FILTER_2_EMPRESA_EXEMPLO'
    }
  },
  'empresa-teste-2': {
    id: 'empresa-teste-2',
    nome: 'Empresa Teste 2',
    cor: '#764ba2',
    apps: {
      dashboard: 'SEU_APP_DASHBOARD_EMPRESA_TESTE_2',
      vendas: 'SEU_APP_VENDAS_EMPRESA_TESTE_2',
      financeiro: 'SEU_APP_FINANCEIRO_EMPRESA_TESTE_2',
      helpdesk: 'SEU_APP_HELPDESK_EMPRESA_TESTE_2'
    },
    objetos: {
      // KPIs
      kpi1: 'SEU_OBJETO_KPI_1_EMPRESA_TESTE_2',
      kpi2: 'SEU_OBJETO_KPI_2_EMPRESA_TESTE_2',
      kpi3: 'SEU_OBJETO_KPI_3_EMPRESA_TESTE_2',
      
      // Gráficos
      chart1: 'SEU_OBJETO_CHART_1_EMPRESA_TESTE_2',
      chart2: 'SEU_OBJETO_CHART_2_EMPRESA_TESTE_2',
      barChart: 'SEU_OBJETO_BAR_CHART_EMPRESA_TESTE_2',
      lineChart: 'SEU_OBJETO_LINE_CHART_EMPRESA_TESTE_2',
      
      // Tabelas
      table1: 'SEU_OBJETO_TABLE_1_EMPRESA_TESTE_2',
      table2: 'SEU_OBJETO_TABLE_2_EMPRESA_TESTE_2',
      
      // Filtros
      filter1: 'SEU_OBJETO_FILTER_1_EMPRESA_TESTE_2',
      filter2: 'SEU_OBJETO_FILTER_2_EMPRESA_TESTE_2'
    }
  }
}

// Função para obter configuração de uma empresa específica
export const getEmpresaConfig = (empresaId) => {
  return EMPRESAS[empresaId] || null
}

// Função para obter todas as empresas
export const getAllEmpresas = () => {
  return Object.values(EMPRESAS)
}

// Função para obter app ID de uma empresa e módulo
export const getAppId = (empresaId, modulo) => {
  const empresa = getEmpresaConfig(empresaId)
  return empresa?.apps[modulo] || null
}

// Função para obter objeto ID de uma empresa
export const getObjectId = (empresaId, objetoId) => {
  const empresa = getEmpresaConfig(empresaId)
  return empresa?.objetos[objetoId] || null
}

// Configuração de páginas e seus objetos por empresa
export const getPageConfig = (empresaId, pagina) => {
  const empresa = getEmpresaConfig(empresaId)
  if (!empresa) return null

  const configs = {
    dashboard: {
      appId: empresa.apps.dashboard,
      objects: [
        empresa.objetos.kpi1,
        empresa.objetos.kpi2,
        empresa.objetos.kpi3,
        empresa.objetos.chart1,
        empresa.objetos.chart2
      ]
    },
    vendas: {
      appId: empresa.apps.vendas,
      objects: [
        empresa.objetos.barChart,
        empresa.objetos.lineChart,
        empresa.objetos.table1
      ]
    },
    helpdesk: {
      appId: empresa.apps.helpdesk,
      objects: [
        empresa.objetos.filter1,
        empresa.objetos.filter2,
        empresa.objetos.table2
      ]
    },
    financeiro: {
      appId: empresa.apps.financeiro,
      objects: [
        empresa.objetos.kpi1,
        empresa.objetos.chart1
      ]
    }
  }

  return configs[pagina] || null
}

// Compatibilidade com código antigo (será removido futuramente)
export const APP_IDS = {
  dashboard: EMPRESAS['empresa-exemplo'].apps.dashboard,
  vendas: EMPRESAS['empresa-exemplo'].apps.vendas,
  financeiro: EMPRESAS['empresa-exemplo'].apps.financeiro,
  helpdesk: EMPRESAS['empresa-exemplo'].apps.helpdesk
}

export const OBJECT_IDS = EMPRESAS['empresa-exemplo'].objetos 