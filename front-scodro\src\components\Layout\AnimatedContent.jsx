import React from 'react'
import { Box } from '@mui/material'
import useSidebarAnimation from '../../hooks/useSidebarAnimation'

const AnimatedContent = ({ children, sidebarCollapsed, className = '' }) => {
  const { isAnimating, animationPhase } = useSidebarAnimation(sidebarCollapsed)

  const getAnimationStyles = () => {
    switch (animationPhase) {
      case 'start':
        return {
          opacity: 0.9,
          transform: 'scale(0.99)',
          filter: 'blur(0.3px)'
        }
      case 'transition':
        return {
          opacity: 0.8,
          transform: 'scale(0.98)',
          filter: 'blur(0.5px)'
        }
      case 'end':
        return {
          opacity: 1,
          transform: 'scale(1)',
          filter: 'blur(0px)'
        }
      default:
        return {
          opacity: 1,
          transform: 'scale(1)',
          filter: 'blur(0px)'
        }
    }
  }

  return (
    <Box
      className={`animated-content ${className}`}
      sx={{
        ...getAnimationStyles(),
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        willChange: 'transform, opacity, filter',
        transform: 'translateZ(0)', // Force hardware acceleration
        '& > *': {
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        }
      }}
    >
      {children}
    </Box>
  )
}

export default AnimatedContent 