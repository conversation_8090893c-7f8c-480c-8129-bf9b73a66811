const Empresa = require('../models/Empresa');
const Objeto = require('../models/Objeto');
const { validarEmpresa, validarEmpresaUpdate } = require('../validators/empresaValidator');

class EmpresaController {
  // Listar todas as empresas
  async listarEmpresas(req, res) {
    try {
      const { ativo, incluirObjetos } = req.query;
      
      let query = {};
      if (ativo !== undefined) {
        query.ativo = ativo === 'true';
      }

      let empresas = await Empresa.find(query).sort({ nome: 1 });

      if (incluirObjetos === 'true') {
        empresas = await Promise.all(
          empresas.map(async (empresa) => {
            const config = await empresa.getConfigCompleta();
            return config;
          })
        );
      }

      res.json({
        success: true,
        data: empresas,
        total: empresas.length
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao listar empresas',
        message: error.message
      });
    }
  }

  // Obter empresa por ID
  async obterEmpresa(req, res) {
    try {
      const { id } = req.params;
      const { incluirObjetos } = req.query;

      const empresa = await Empresa.findByCustomId(id);
      
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      let resultado = empresa;
      if (incluirObjetos === 'true') {
        resultado = await empresa.getConfigCompleta();
      }

      res.json({
        success: true,
        data: resultado
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao obter empresa',
        message: error.message
      });
    }
  }

  // Criar nova empresa
  async criarEmpresa(req, res) {
    try {
      const { error } = validarEmpresa(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Dados inválidos',
          details: error.details.map(detail => detail.message)
        });
      }

      // Verificar se já existe empresa com o mesmo ID
      const empresaExistente = await Empresa.findByCustomId(req.body.id);
      if (empresaExistente) {
        return res.status(409).json({
          success: false,
          error: 'Já existe uma empresa com este ID'
        });
      }

      const empresa = new Empresa(req.body);
      await empresa.save();

      res.status(201).json({
        success: true,
        data: empresa,
        message: 'Empresa criada com sucesso'
      });
    } catch (error) {
      if (error.code === 11000) {
        return res.status(409).json({
          success: false,
          error: 'Empresa com este ID já existe'
        });
      }

      res.status(500).json({
        success: false,
        error: 'Erro ao criar empresa',
        message: error.message
      });
    }
  }

  // Atualizar empresa
  async atualizarEmpresa(req, res) {
    try {
      const { id } = req.params;
      
      const { error } = validarEmpresaUpdate(req.body);
      if (error) {
        return res.status(400).json({
          success: false,
          error: 'Dados inválidos',
          details: error.details.map(detail => detail.message)
        });
      }

      const empresa = await Empresa.findByCustomId(id);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      // Atualizar campos
      Object.assign(empresa, req.body);
      await empresa.save();

      res.json({
        success: true,
        data: empresa,
        message: 'Empresa atualizada com sucesso'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao atualizar empresa',
        message: error.message
      });
    }
  }

  // Excluir empresa (soft delete)
  async excluirEmpresa(req, res) {
    try {
      const { id } = req.params;
      const { forceDelete } = req.query;

      const empresa = await Empresa.findByCustomId(id);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      if (forceDelete === 'true') {
        // Excluir todos os objetos relacionados
        await Objeto.deleteMany({ empresaId: id });
        // Excluir empresa
        await Empresa.deleteOne({ id });
        
        res.json({
          success: true,
          message: 'Empresa e objetos relacionados excluídos permanentemente'
        });
      } else {
        // Soft delete
        empresa.ativo = false;
        await empresa.save();
        
        res.json({
          success: true,
          message: 'Empresa desativada com sucesso'
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao excluir empresa',
        message: error.message
      });
    }
  }

  // Obter configuração completa para o frontend
  async obterConfigFrontend(req, res) {
    try {
      const { id } = req.params;

      const empresa = await Empresa.findByCustomId(id);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      if (!empresa.ativo) {
        return res.status(403).json({
          success: false,
          error: 'Empresa não está ativa'
        });
      }

      const config = await empresa.getConfigCompleta();

      res.json({
        success: true,
        data: config
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao obter configuração',
        message: error.message
      });
    }
  }

  // Reativar empresa
  async reativarEmpresa(req, res) {
    try {
      const { id } = req.params;

      const empresa = await Empresa.findByCustomId(id);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      empresa.ativo = true;
      await empresa.save();

      res.json({
        success: true,
        data: empresa,
        message: 'Empresa reativada com sucesso'
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao reativar empresa',
        message: error.message
      });
    }
  }

  // ✅ NOVO: Obter objetos da empresa com filtros
  async obterObjetosEmpresa(req, res) {
    try {
      const { id } = req.params;
      const { tipo, categoria, ativo } = req.query;

      // Verificar se a empresa existe
      const empresa = await Empresa.findByCustomId(id);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      // Construir query para filtrar objetos
      let query = { empresaId: id };
      
      if (tipo) query.tipo = tipo;
      if (categoria) query.categoria = categoria;
      if (ativo !== undefined) query.ativo = ativo === 'true';

      // ✅ CORRIGIDO: Buscar objetos com populate do App para trazer o appId do Qlik
      const objetos = await Objeto.find(query)
        .populate('appId', 'appId nome categoria')  // Fazer populate do App para trazer o appId do Qlik
        .sort({ categoria: 1, ordem: 1 });

      // ✅ CORRIGIDO: Transformar os dados para incluir o qlikAppId
      const objetosComQlikAppId = objetos.map(objeto => {
        const objetoObj = objeto.toObject();
        return {
          ...objetoObj,
          qlikAppId: objeto.appId?.appId,  // Adicionar o appId do Qlik Sense
          appName: objeto.appId?.nome,     // Nome do app para debug
          appCategoria: objeto.appId?.categoria  // Categoria do app
        };
      });

      res.json({
        success: true,
        data: objetosComQlikAppId,
        total: objetosComQlikAppId.length,
        filters: {
          tipo: tipo || null,
          categoria: categoria || null,
          ativo: ativo !== undefined ? ativo === 'true' : null
        },
        empresa: {
          id: empresa.id,
          nome: empresa.nome
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao obter objetos da empresa',
        message: error.message
      });
    }
  }
}

module.exports = new EmpresaController(); 