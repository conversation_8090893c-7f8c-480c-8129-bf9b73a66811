const mongoose = require('mongoose');

const connectDB = async () => {
  try {
    // Configurações atualizadas para Mongoose 8+
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      // Removendo opções deprecated
      // useNewUrlParser e useUnifiedTopology não são mais necessárias
    });

    console.log(`🍃 MongoDB conectado: ${conn.connection.host}`);
    console.log(`📊 Database: ${conn.connection.name}`);
    
    // Event listeners para monitoramento da conexão
    mongoose.connection.on('error', (err) => {
      console.error('❌ Erro na conexão MongoDB:', err);
    });

    mongoose.connection.on('disconnected', () => {
      console.warn('⚠️ MongoDB desconectado');
    });

    mongoose.connection.on('reconnected', () => {
      console.log('🔄 MongoDB reconectado');
    });

    // Teste de ping para confirmar conexão
    await mongoose.connection.db.admin().ping();
    console.log('🏓 Ping MongoDB bem-sucedido!');

  } catch (error) {
    console.error('❌ Erro ao conectar ao MongoDB:', error.message);
    console.warn('⚠️ Servidor continuará funcionando sem MongoDB');
    console.warn('💡 Verifique a string de conexão MONGO_URI no arquivo .env');
    console.warn('💡 Certifique-se de que seu IP está na whitelist do MongoDB Atlas');
    
    // Não parar o processo, apenas avisar
    // process.exit(1);
  }
};

module.exports = connectDB; 