/* ===================================
   🎨 SISTEMA DE SOMBRAS PADRONIZADO
   ================================== */

/* Variáveis CSS para sombras consistentes */
:root {
  /* Sombras básicas */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.04), 0 4px 8px rgba(0, 0, 0, 0.06);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.06), 0 6px 12px rgba(0, 0, 0, 0.08);
  
  /* Sombras coloridas para hover states */
  --shadow-primary: 0 4px 12px rgba(102, 126, 234, 0.12), 0 2px 4px rgba(102, 126, 234, 0.08);
  --shadow-primary-lg: 0 8px 24px rgba(102, 126, 234, 0.15), 0 4px 8px rgba(102, 126, 234, 0.10);
  
  /* Transições padrão */
  --shadow-transition: all 0.2s ease-in-out;
  
  /* ===================================
     🎨 SISTEMA DE BORDER-RADIUS PADRONIZADO
     ================================== */
  
  /* Border-radius consistentes */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;
  
  /* Regra de ouro: elementos filhos devem ter border-radius menor ou igual ao pai */
  --card-radius: 12px;      /* Para cards principais */
  --card-content-radius: 11px;  /* Para conteúdo interno (1px menor) */
  --card-inner-radius: 10px;    /* Para elementos internos (2px menor) */
}

/* Classes utilitárias para sombras */
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.shadow-primary { box-shadow: var(--shadow-primary); }
.shadow-primary-lg { box-shadow: var(--shadow-primary-lg); }
.shadow-none { box-shadow: none; }

/* Classes utilitárias para border-radius */
.radius-xs { border-radius: var(--radius-xs); }
.radius-sm { border-radius: var(--radius-sm); }
.radius-md { border-radius: var(--radius-md); }
.radius-lg { border-radius: var(--radius-lg); }
.radius-xl { border-radius: var(--radius-xl); }
.radius-full { border-radius: var(--radius-full); }

/* Transições */
.shadow-transition { transition: var(--shadow-transition); }

/* Hover states consistentes */
.hover-shadow-sm:hover { box-shadow: var(--shadow-sm); }
.hover-shadow-md:hover { box-shadow: var(--shadow-md); }
.hover-shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover-shadow-primary:hover { box-shadow: var(--shadow-primary); }
.hover-shadow-primary-lg:hover { box-shadow: var(--shadow-primary-lg); }

/* ===================================
   🔧 FIXES ESPECÍFICOS MUI
   ================================== */

/* Remover sombras conflitantes do Material-UI */
.MuiCard-root {
  box-shadow: var(--shadow-sm) !important;
  transition: var(--shadow-transition) !important;
  border-radius: var(--card-radius) !important;
}

.MuiCard-root:hover {
  box-shadow: var(--shadow-primary) !important;
}

.MuiPaper-root {
  box-shadow: var(--shadow-sm) !important;
  transition: var(--shadow-transition) !important;
}

/* Específico para gráficos */
.custom-chart-card {
  box-shadow: var(--shadow-sm) !important;
  border-radius: var(--card-radius) !important;
}

.custom-chart-card:hover {
  box-shadow: var(--shadow-primary) !important;
}

/* Grid layout cards */
.grid-item-card {
  box-shadow: var(--shadow-sm) !important;
  transition: var(--shadow-transition) !important;
  border-radius: var(--card-radius) !important;
}

.grid-item-card:hover {
  box-shadow: var(--shadow-primary) !important;
}

/* Corrigir hierarchy de border-radius */
.grid-item-card .MuiCardContent-root {
  border-radius: var(--card-content-radius) !important;
}

.grid-item-card .MuiBox-root {
  border-radius: var(--card-inner-radius) !important;
}

/* ===================================
   🎯 REGRAS ESPECÍFICAS PARA EVITAR CONFLITOS
   ================================== */

/* Garantir que elementos filhos nunca tenham border-radius maior que o pai */
.MuiCard-root * {
  border-radius: min(var(--card-content-radius), inherit) !important;
}

.MuiCardContent-root * {
  border-radius: min(var(--card-inner-radius), inherit) !important;
} 