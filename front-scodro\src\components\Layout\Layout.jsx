import React, { useState, useMemo, useEffect, useCallback } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  AppBar,
  Toolbar,
  Typography,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Box,
  useTheme,
  useMediaQuery,
  Chip,
  Collapse,
  Tooltip,
  CircularProgress,
  Alert,
  Badge
} from '@mui/material'
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  TrendingUp as SalesIcon,
  Support as HelpdeskIcon,
  AccountBalance as FinanceIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
  Business as BusinessIcon,
  ExpandLess,
  ExpandMore,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  FilterList as FilterListIcon,
  // Ícones adicionais para páginas
  Analytics,
  Assignment,
  People,
  Inventory,
  ShoppingCart,
  Timeline,
  Assessment,
  LocalAtm,
  Receipt,
  Bar<PERSON><PERSON>,
  PieChart
} from '@mui/icons-material'
import { useEmpresa } from '@context/EmpresaContext'
import { useQlikSelection } from '@contexts/QlikSelectionContext'
import qlikService from '@services/qlik'
import AnimatedContent from './AnimatedContent'
import GlobalFilterSidebar from '@components/GlobalFilterSidebar'
import '../../styles/sidebar-transitions.css'

const drawerWidthExpanded = 280
const drawerWidthCollapsed = 72

// ✅ NOVO: Função para criar tom mais escuro
const darkenColor = (color, amount = 0.2) => {
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)

  const darkenedR = Math.round(r * (1 - amount))
  const darkenedG = Math.round(g * (1 - amount))
  const darkenedB = Math.round(b * (1 - amount))

  return `#${darkenedR.toString(16).padStart(2, '0')}${darkenedG.toString(16).padStart(2, '0')}${darkenedB.toString(16).padStart(2, '0')}`
}

// ✅ NOVO: Componente para estilos dinâmicos do scrollbar
const DynamicScrollbarStyles = ({ colors }) => {
  useEffect(() => {
    // Remover estilo anterior se existir
    const existingStyle = document.getElementById('dynamic-scrollbar-styles')
    if (existingStyle) {
      existingStyle.remove()
    }

    // Criar novo estilo
    const style = document.createElement('style')
    style.id = 'dynamic-scrollbar-styles'
    style.textContent = `
      /* Scrollbars dinâmicos baseados na cor da empresa */
      *::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryDark} 100%) !important;
        transition: background 0.5s ease-in-out !important;
      }

      *::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, ${darkenColor(colors.primary, 0.1)} 0%, ${darkenColor(colors.primaryDark, 0.1)} 100%) !important;
      }

      /* Firefox */
      * {
        scrollbar-color: ${colors.primary} #f1f1f1 !important;
        transition: scrollbar-color 0.5s ease-in-out !important;
      }

      @media (prefers-color-scheme: dark) {
        * {
          scrollbar-color: ${colors.primary} #2c2c2c !important;
        }
      }
    `

    document.head.appendChild(style)

    // Cleanup quando o componente for desmontado
    return () => {
      const styleToRemove = document.getElementById('dynamic-scrollbar-styles')
      if (styleToRemove) {
        styleToRemove.remove()
      }
    }
  }, [colors.primary, colors.primaryDark])

  return null
}

const Layout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [globalFilterOpen, setGlobalFilterOpen] = useState(false)
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const location = useLocation()

  const {
    empresaSelecionada,
    selecionarEmpresa,
    sidebarCollapsed,
    toggleSidebar,
    empresasExpandidas,
    toggleEmpresaExpansion,
    empresas,
    empresaAtual,
    loading,
    error
  } = useEmpresa()

  // Detectar empresa na URL e selecionar automaticamente
  useEffect(() => {
    // Pular rotas de sistema (/configuracoes, /sobre, etc.)
    const rotasSistema = ['/configuracoes', '/sobre', '/dashboard', '/vendas', '/helpdesk', '/financeiro']
    if (rotasSistema.some(rota => location.pathname.startsWith(rota))) {
      return
    }

    // Extrair empresaId da URL simplificada
    const match = location.pathname.match(/^\/([^\/]+)/)
    if (match && match[1]) {
      const empresaIdNaUrl = match[1]
      if (empresaIdNaUrl !== empresaSelecionada && empresas.length > 0) {
        const empresaExiste = empresas.some(emp => (emp.id === empresaIdNaUrl || emp._id === empresaIdNaUrl))
        if (empresaExiste) {
          selecionarEmpresa(empresaIdNaUrl)
        }
      }
    }
  }, [location.pathname, empresaSelecionada, empresas, selecionarEmpresa])

  // ✅ NOVO: Hook para seleções globais
  const { selections } = useQlikSelection()

  // ✅ NOVO: Calcular total de seleções ativas
  const totalSelections = useMemo(() => {
    return Array.from(selections.values()).reduce((acc, fieldSelections) => acc + fieldSelections.size, 0)
  }, [selections])

  // ✅ NOVO: Cores dinâmicas baseadas na empresa selecionada
  const dynamicColors = useMemo(() => {
    const empresaCor = empresaAtual?.cor || '#667eea'
    const corEscura = darkenColor(empresaCor, 0.3)

    return {
      primary: empresaCor,
      primaryDark: corEscura,
      gradient: `linear-gradient(135deg, ${empresaCor} 0%, ${corEscura} 100%)`,
      gradientReverse: `linear-gradient(135deg, ${corEscura} 0%, ${empresaCor} 100%)`
    }
  }, [empresaAtual?.cor])

  const drawerWidth = sidebarCollapsed ? drawerWidthCollapsed : drawerWidthExpanded

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  // Função para obter o ícone correto baseado no nome
  const getIconForPage = (iconName) => {
    const iconMap = {
      'dashboard': <DashboardIcon />,
      'trending_up': <SalesIcon />,
      'support': <HelpdeskIcon />,
      'account_balance': <FinanceIcon />,
      'analytics': <Analytics />,
      'assignment': <Assignment />,
      'people': <People />,
      'inventory': <Inventory />,
      'shopping_cart': <ShoppingCart />,
      'timeline': <Timeline />,
      'assessment': <Assessment />,
      'business': <BusinessIcon />,
      'local_atm': <LocalAtm />,
      'receipt': <Receipt />,
      'bar_chart': <BarChart />,
      'pie_chart': <PieChart />
    };
    return iconMap[iconName] || <DashboardIcon />;
  };



  const getEnvironmentChip = () => {
    const config = qlikService.getConfig()
    const environment = config?.environment || 'development'

    const chipProps = {
      development: { label: 'DEV', color: 'warning' },
      cloud: { label: 'CLOUD', color: 'info' },
      enterprise: { label: 'ENTERPRISE', color: 'success' }
    }

    return (
      <Chip
        size="small"
        variant="outlined"
        {...chipProps[environment]}
        sx={{ ml: sidebarCollapsed ? 0 : 2, display: sidebarCollapsed ? 'none' : 'flex' }}
      />
    )
  }

  const getCurrentPageTitle = useCallback(() => {
    try {
      // Safety check
      if (!location?.pathname) return 'Dashboard'
      
      // Pular rotas de sistema
      const rotasSistema = ['/configuracoes', '/sobre', '/dashboard', '/vendas', '/helpdesk', '/financeiro']
      if (rotasSistema.some(rota => location.pathname.startsWith(rota))) {
        const pathSegments = location.pathname.split('/').filter(Boolean)
        if (pathSegments.length > 0) {
          const lastSegment = pathSegments[pathSegments.length - 1]
          const capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1)
          return capitalize(lastSegment)
        }
        return 'Dashboard'
      }

      // Extrair empresa e chave da URL simplificada
      const match = location.pathname.match(/^\/([^\/]+)(?:\/([^\/]+))?$/)
      if (match && empresas && Array.isArray(empresas)) {
        const [, empresaId, chave] = match
        const empresa = empresas.find(emp => (emp.id === empresaId || emp._id === empresaId))
        if (empresa && empresa.paginas && Array.isArray(empresa.paginas)) {
          const paginaChave = chave || 'dashboard'
          const pagina = empresa.paginas.find(p => p.chave === paginaChave)
          return pagina?.titulo || 'Dashboard'
        }
      }
      
      return 'Dashboard'
    } catch (error) {
      console.warn('Erro em getCurrentPageTitle:', error)
      return 'Dashboard'
    }
  }, [location?.pathname, empresas])

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }} className="sidebar-transition">
      {/* Header do Drawer */}
      <Box
        className="sidebar-header"
        sx={{
          p: sidebarCollapsed ? 1 : 3,
          background: dynamicColors.gradient,
          color: 'white',
          position: 'relative',
          minHeight: sidebarCollapsed ? '64px' : 'auto',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        {!sidebarCollapsed && (
          <Box className="sidebar-fade show sidebar-stable-container">
            {/*<Typography 
              variant="h6" 
              className="sidebar-stable-text"
              sx={{ 
                fontWeight: 'bold', 
                mb: 1, 
                fontSize: '1.2rem',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis'
              }}
            >
              Qlik Mashup
            </Typography>
            */}

            {/* Logo da empresa ou nome da empresa */}
            {empresaAtual?.logo ? (
              <Box className="sidebar-stable-container" sx={{
                textAlign: 'center'
              }}>
                <Box
                  component="img"
                  src={empresaAtual.logo}
                  alt={`Logo ${empresaAtual.nome}`}
                  className="sidebar-stable-image"
                  sx={{
                    maxWidth: '100%',
                    maxHeight: 80,
                    objectFit: 'contain',
                    borderRadius: 2
                  }}
                  onError={(e) => {
                    e.target.style.display = 'none'
                  }}
                />
              </Box>
            ) : (
              <Typography
                variant="body2"
                className="sidebar-stable-text"
                sx={{
                  opacity: 0.9,
                  mb: 1,
                  textAlign: 'center',
                  fontWeight: 500,
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis'
                }}
              >
                {empresaAtual?.nome || 'Dashboard'}
              </Typography>
            )}
          </Box>
        )}

        {/* Botão de colapsar/expandir */}
        <Tooltip title={sidebarCollapsed ? "Expandir sidebar" : "Colapsar sidebar"}>
          <IconButton
            onClick={toggleSidebar}
            className="sidebar-toggle-button"
            sx={{
              position: 'absolute',
              top: '8px',
              right: '8px',
              color: 'white',
              backgroundColor: 'rgba(255,255,255,0.1)',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.2)'
              }
            }}
            size="small"
          >
            {sidebarCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {/* Lista de Empresas */}
      <List sx={{ flex: 1, pt: 2, px: 1 }}>
        {loading && !sidebarCollapsed && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
            <CircularProgress size={24} />
          </Box>
        )}

        {error && !sidebarCollapsed && (
          <Box sx={{ p: 1 }}>
            <Alert severity="error" sx={{ fontSize: '0.75rem' }}>
              Erro ao carregar empresas
            </Alert>
          </Box>
        )}

        {!loading && !error && empresas.length === 0 && !sidebarCollapsed && (
          <Box sx={{ p: 2, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Nenhuma empresa cadastrada
            </Typography>
          </Box>
        )}

        {empresas.map((empresa) => (
          <Box key={empresa.id || empresa._id}>
            {/* Header da Empresa */}
            <ListItem
              onClick={() => toggleEmpresaExpansion(empresa.id || empresa._id)}
              className="menu-item-hover"
              sx={{
                mb: 1,
                borderRadius: 2,
                cursor: 'pointer',
                backgroundColor: empresaSelecionada === (empresa.id || empresa._id) ? dynamicColors.primary : 'transparent',
                color: empresaSelecionada === (empresa.id || empresa._id) ? 'white' : 'inherit',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  backgroundColor: empresaSelecionada === (empresa.id || empresa._id) ? dynamicColors.primaryDark : 'action.hover'
                },
                minHeight: 48,
                px: sidebarCollapsed ? 1 : 2
              }}
            >
              <ListItemIcon
                className="icon-transition"
                sx={{
                  color: empresaSelecionada === (empresa.id || empresa._id) ? 'white' : empresa.cor,
                  minWidth: sidebarCollapsed ? 'auto' : 40,
                  justifyContent: 'center',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                }}
              >
                <BusinessIcon />
              </ListItemIcon>

              {!sidebarCollapsed && (
                <Box className="text-transition sidebar-stable-container" sx={{ display: 'flex', alignItems: 'center' }}>
                  <ListItemText
                    primary={empresa.nome}
                    primaryTypographyProps={{
                      className: "sidebar-stable-text",
                      sx: {
                        fontWeight: empresaSelecionada === (empresa.id || empresa._id) ? 'bold' : 'normal',
                        fontSize: '0.9rem',
                        overflow: 'hidden',
                        whiteSpace: 'nowrap',
                        textOverflow: 'ellipsis'
                      }
                    }}
                  />
                  {empresasExpandidas[empresa.id || empresa._id] ? <ExpandLess /> : <ExpandMore />}
                </Box>
              )}
            </ListItem>

            {/* Menu Items da Empresa */}
            {!sidebarCollapsed && (
              <Collapse in={empresasExpandidas[empresa.id || empresa._id]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {empresa.paginas?.filter(pagina => pagina.ativo && pagina.configuracao?.showInSidebar !== false)
                    .sort((a, b) => a.ordem - b.ordem)
                    .map((pagina) => {
                      const empresaId = empresa.id || empresa._id;
                      const isDefaultPage = ['dashboard', 'vendas', 'helpdesk', 'financeiro'].includes(pagina.chave);
                      const path = isDefaultPage 
                        ? `/${empresaId}${pagina.rota}` 
                        : `/${empresaId}/${pagina.chave}`;
                      
                      return (
                        <ListItem
                          key={`${empresaId}-${pagina.chave}`}
                          component={Link}
                          to={path}
                          onClick={() => selecionarEmpresa(empresaId)}
                          className="menu-item-hover"
                          sx={{
                            ml: 2,
                            mb: 0.5,
                            borderRadius: 2,
                            textDecoration: 'none',
                            backgroundColor: (location.pathname === path && empresaSelecionada === empresaId)
                              ? dynamicColors.primary : 'transparent',
                            color: (location.pathname === path && empresaSelecionada === empresaId)
                              ? 'white' : 'inherit',
                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                            '&:hover': {
                              backgroundColor: (location.pathname === path && empresaSelecionada === empresaId)
                                ? dynamicColors.primaryDark : 'action.hover'
                            }
                          }}
                        >
                          <ListItemIcon
                            className="icon-transition"
                            sx={{
                              color: (location.pathname === path && empresaSelecionada === empresaId)
                                ? 'white' : dynamicColors.primary,
                              minWidth: 36,
                              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                            }}
                          >
                            {getIconForPage(pagina.icone)}
                          </ListItemIcon>
                          <ListItemText
                            primary={pagina.titulo}
                            secondary={pagina.descricao}
                            primaryTypographyProps={{
                              sx: {
                                overflow: 'hidden',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis'
                              }
                            }}
                            secondaryTypographyProps={{
                              sx: {
                                color: (location.pathname === path && empresaSelecionada === empresaId)
                                  ? 'rgba(255,255,255,0.7)' : 'text.secondary',
                                fontSize: '0.7rem',
                                overflow: 'hidden',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis'
                              }
                            }}
                          />
                        </ListItem>
                      )
                    })
                  }
                </List>
              </Collapse>
            )}

            {/* Tooltip para sidebar colapsado */}
            {sidebarCollapsed && (
              <Tooltip title={empresa.nome} placement="right">
                <Box />
              </Tooltip>
            )}
          </Box>
        ))}
      </List>

      {/* Footer do Drawer */}
      <Box className="sidebar-footer" sx={{ p: sidebarCollapsed ? 1 : 2, borderTop: 1, borderColor: 'divider' }}>
        <List>
          <Tooltip title={sidebarCollapsed ? "Configurações" : ""} placement="right">
            <ListItem
              component={Link}
              to="/configuracoes"
              className="menu-item-hover"
              sx={{
                borderRadius: 2,
                textDecoration: 'none',
                color: 'inherit',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': { backgroundColor: 'action.hover' },
                minHeight: 48,
                px: sidebarCollapsed ? 1 : 2,
                justifyContent: sidebarCollapsed ? 'center' : 'flex-start'
              }}
            >
              <ListItemIcon className="icon-transition" sx={{ minWidth: sidebarCollapsed ? 'auto' : 40, justifyContent: 'center' }}>
                <SettingsIcon />
              </ListItemIcon>
              {!sidebarCollapsed && (
                <ListItemText
                  primary="Configurações"
                  primaryTypographyProps={{
                    sx: {
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis'
                    }
                  }}
                />
              )}
            </ListItem>
          </Tooltip>

          <Tooltip title={sidebarCollapsed ? "Sobre" : ""} placement="right">
            <ListItem
              component={Link}
              to="/sobre"
              className="menu-item-hover"
              sx={{
                borderRadius: 2,
                textDecoration: 'none',
                color: 'inherit',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': { backgroundColor: 'action.hover' },
                minHeight: 48,
                px: sidebarCollapsed ? 1 : 2,
                justifyContent: sidebarCollapsed ? 'center' : 'flex-start'
              }}
            >
              <ListItemIcon className="icon-transition" sx={{ minWidth: sidebarCollapsed ? 'auto' : 40, justifyContent: 'center' }}>
                <InfoIcon />
              </ListItemIcon>
              {!sidebarCollapsed && (
                <ListItemText
                  primary="Sobre"
                  primaryTypographyProps={{
                    sx: {
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis'
                    }
                  }}
                />
              )}
            </ListItem>
          </Tooltip>
        </List>
      </Box>
    </Box>
  )

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Estilos dinâmicos para scrollbar */}
      <DynamicScrollbarStyles colors={dynamicColors} />

      {/* AppBar */}
      <AppBar
        position="fixed"
        className="appbar-transition"
        sx={{
          width: { md: `calc(100% - ${drawerWidth}px)` },
          ml: { md: `${drawerWidth}px` },
          background: dynamicColors.gradient,
          borderRadius: 0,
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="abrir menu"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {getCurrentPageTitle()}
          </Typography>

          <Typography
            variant="body2"
            sx={{
              mr: 2,
              opacity: 0.9,
              cursor: 'pointer',
              '&:hover': {
                opacity: 1
              }
            }}
            onClick={() => setGlobalFilterOpen(true)}
          >
            Filtros
          </Typography>

          {/* ✅ NOVO: Botão de Filtros Globais */}
          <Tooltip title="Filtros Globais">
            <IconButton
              color="inherit"
              onClick={() => setGlobalFilterOpen(true)}
              sx={{                
                backgroundColor: 'rgba(255,255,255,0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.2)'
                }
              }}
            >
              <Badge badgeContent={totalSelections} color="secondary">
                <FilterListIcon />
              </Badge>
            </IconButton>
          </Tooltip>


        </Toolbar>
      </AppBar>

      {/* ✅ NOVO: Sidebar Global de Filtros */}
      <GlobalFilterSidebar
        open={globalFilterOpen}
        onClose={() => setGlobalFilterOpen(false)}
      />

      {/* Drawer */}
      <Box
        component="nav"
        className="drawer-width-transition"
        sx={{
          width: { md: drawerWidth },
          flexShrink: { md: 0 },
          transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        {/* Mobile drawer */}
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{ keepMounted: true }}
          sx={{
            display: { xs: 'block', md: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidthExpanded,
              border: 'none',
              borderRadius: 0,
              boxShadow: theme.shadows[8]
            },
          }}
        >
          {drawer}
        </Drawer>

        {/* Desktop drawer */}
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', md: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              border: 'none',
              borderRadius: 0,
              boxShadow: theme.shadows[2],
              transition: 'width 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
              overflowX: 'hidden'
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* Main content */}
      <Box
        component="main"
        className="main-content-transition"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        <Toolbar /> {/* Spacer for AppBar */}
        <Box sx={{ p: 3 }}>
          <AnimatedContent sidebarCollapsed={sidebarCollapsed}>
            {children}
          </AnimatedContent>
        </Box>
      </Box>
    </Box>
  )
}

export default Layout