const ConfigMashup = require('../models/ConfigMashup');

// Obter configuração ativa
const obterConfigAtiva = async (req, res) => {
  try {
    const config = await ConfigMashup.getConfigAtiva();
    
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Erro ao obter configuração:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Atualizar configuração
const atualizarConfig = async (req, res) => {
  try {
    const { id = 'default' } = req.params;
    const dadosConfig = req.body;

    let config = await ConfigMashup.findOne({ id });
    
    if (!config) {
      // Criar nova configuração se não existir
      config = new ConfigMashup({ id, ...dadosConfig });
    } else {
      // Atualizar configuração existente
      Object.assign(config, dadosConfig);
    }

    // Validar configuração antes de salvar
    const validacao = config.validarConfiguracao();
    if (!validacao.valido) {
      return res.status(400).json({
        success: false,
        error: 'Dados de configuração inválidos',
        detalhes: validacao.erros
      });
    }

    await config.save();

    res.json({
      success: true,
      data: config,
      message: 'Configuração atualizada com sucesso'
    });
  } catch (error) {
    console.error('Erro ao atualizar configuração:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        detalhes: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Validar configuração
const validarConfig = async (req, res) => {
  try {
    const dadosConfig = req.body;
    
    // Criar instância temporária para validação
    const configTemp = new ConfigMashup(dadosConfig);
    const validacao = configTemp.validarConfiguracao();

    res.json({
      success: true,
      data: {
        valido: validacao.valido,
        erros: validacao.erros
      }
    });
  } catch (error) {
    console.error('Erro ao validar configuração:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Resetar configuração para padrão
const resetarConfig = async (req, res) => {
  try {
    const { id = 'default' } = req.params;

    // Remover configuração existente
    await ConfigMashup.deleteOne({ id });

    // Criar nova configuração padrão
    const configPadrao = new ConfigMashup({
      id,
      nome: 'Configuração Principal',
      ativo: true
    });

    await configPadrao.save();

    res.json({
      success: true,
      data: configPadrao,
      message: 'Configuração resetada para padrão'
    });
  } catch (error) {
    console.error('Erro ao resetar configuração:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

module.exports = {
  obterConfigAtiva,
  atualizarConfig,
  validarConfig,
  resetarConfig
}; 