const express = require('express');
const router = express.Router();
const empresaController = require('../controllers/empresaController');

// Rotas para empresas
// GET /api/empresas - Listar todas as empresas
router.get('/', empresaController.listarEmpresas);

// GET /api/empresas/:id - Obter empresa específica
router.get('/:id', empresaController.obterEmpresa);

// GET /api/empresas/:id/config - Obter configuração completa para frontend
router.get('/:id/config', empresaController.obterConfigFrontend);

// GET /api/empresas/:id/objetos - Obter objetos da empresa (com filtros opcionais)
router.get('/:id/objetos', empresaController.obterObjetosEmpresa);

// POST /api/empresas - Criar nova empresa
router.post('/', empresaController.criarEmpresa);

// PUT /api/empresas/:id - Atualizar empresa
router.put('/:id', empresaController.atualizarEmpresa);

// DELETE /api/empresas/:id - Excluir/desativar empresa
router.delete('/:id', empresaController.excluirEmpresa);

// PATCH /api/empresas/:id/reativar - Reativar empresa
router.patch('/:id/reativar', empresaController.reativarEmpresa);

module.exports = router; 