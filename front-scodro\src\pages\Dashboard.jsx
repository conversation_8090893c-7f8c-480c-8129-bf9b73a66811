import React, { useEffect, useState } from 'react'
import { Box, Typography, Alert, Switch, FormControlLabel, Tooltip } from '@mui/material'
import { Edit as EditIcon, Visibility as VisibilityIcon } from '@mui/icons-material'
import GridLayout from '@components/GridLayout/GridLayout'
import { useEmpresa } from '@context/EmpresaContext'

const Dashboard = () => {
  const { empresaSelecionada, empresaAtual, loading, error } = useEmpresa()
  const [editMode, setEditMode] = useState(process.env.NODE_ENV === 'development' ? false : false)

  // Debug da empresa atual
  useEffect(() => {
    if (empresaAtual) {
      //console.log('🎯 Dashboard - Empresa atual:', empresaAtual)
      //console.log('📱 Dashboard - Apps disponíveis:', empresaAtual.apps)
      //console.log('🔧 Dashboard - Estrutura completa:', JSON.stringify(empresaAtual, null, 2))
    }
  }, [empresaAtual])

  // Obter appId do dashboard da empresa atual
  const getAppId = () => {
    if (!empresaAtual) return null
    
    //console.log('🔍 Procurando AppId para dashboard...')
    
    // Se tem apps como array
    if (Array.isArray(empresaAtual.apps)) {
      //console.log('📂 Apps é um array:', empresaAtual.apps)
      
      // Procurar app do tipo dashboard ou com nome dashboard
      const dashboardApp = empresaAtual.apps.find(app => 
        app.nome?.toLowerCase().includes('dashboard') || 
        app.categoria === 'dashboard'
      )
      
      if (dashboardApp && dashboardApp._id) {
        //console.log('✅ App dashboard encontrado:', dashboardApp)
        //console.log('🔑 Usando _id do app para backend:', dashboardApp._id)
        //console.log('📋 AppId do Qlik:', dashboardApp.appId)
        return dashboardApp._id // Retornar _id para o backend, não appId
      }
      
      // Se não encontrou dashboard específico, usar o primeiro app
      if (empresaAtual.apps.length > 0 && empresaAtual.apps[0]._id) {
        //console.log('🎯 Usando primeiro app disponível:', empresaAtual.apps[0])
        //console.log('🔑 _id do primeiro app:', empresaAtual.apps[0]._id)
        return empresaAtual.apps[0]._id // Retornar _id para o backend
      }
    }
    
    // Se apps é um objeto (formato antigo)
    if (empresaAtual.apps && typeof empresaAtual.apps === 'object') {
      //console.log('📁 Apps é um objeto:', empresaAtual.apps)
      
      // Tentar pegar dashboard primeiro
      if (empresaAtual.apps.dashboard) {
        //console.log('✅ Dashboard app encontrado no objeto')
        return empresaAtual.apps.dashboard
      }
      
      // Pegar qualquer app disponível
      const appIds = Object.values(empresaAtual.apps).filter(Boolean)
      if (appIds.length > 0) {
        //console.log('🎯 Usando primeiro app do objeto:', appIds[0])
        return appIds[0]
      }
    }
    
    // Último fallback: usar ID genérico da empresa para permitir criação de objetos
    //console.log('🔄 Nenhum app específico, usando fallback genérico')
    return 'dashboard-' + empresaSelecionada
  }

  const appId = getAppId()
  
  //console.log('🎯 AppId final para Dashboard:', appId)

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Carregando dados da empresa...
        </Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mt: 2 }}>
          Erro ao carregar dados: {error}
        </Alert>
      </Box>
    )
  }

  if (!empresaSelecionada || !empresaAtual) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="warning" sx={{ mt: 2 }}>
          Nenhuma empresa selecionada. Selecione uma empresa no menu lateral.
        </Alert>
      </Box>
    )
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toggle de Habilitação de Edição - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ 
          p: 2, 
          pb: 1,
          display: 'flex', 
          justifyContent: 'flex-end', 
          alignItems: 'center',
          minHeight: '60px'
        }}>
          <Tooltip title={editMode ? "Desabilitar modo de edição" : "Habilitar modo de edição"}>
            <FormControlLabel
              control={
                <Switch
                  checked={editMode}
                  onChange={(e) => setEditMode(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {editMode ? <EditIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
                  <Typography variant="body2">
                    {editMode ? 'Editando' : 'Visualização'}
                  </Typography>
                </Box>
              }
              sx={{ m: 0 }}
            />
          </Tooltip>
        </Box>
      )}

      {/* Aviso sobre modo de edição quando ativo - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && editMode && (
        <Alert 
          severity="info" 
          sx={{ 
            mx: 2, 
            mt: 1, 
            borderRadius: 2,
            '& .MuiAlert-message': { py: 0.5 }
          }}
        >
          <Typography variant="body2">
            <strong>✏️ Modo de Edição Ativo:</strong> Você pode adicionar, mover, redimensionar e remover objetos.
          </Typography>
        </Alert>
      )}

      {/* Aviso se não há app específico configurado - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && editMode && (!empresaAtual.apps || (Array.isArray(empresaAtual.apps) && empresaAtual.apps.length === 0)) && (
        <Alert severity="info" sx={{ mx: 2, mt: 1 }}>
          <Typography variant="body2">
            <strong>💡 Dica:</strong> Esta empresa ainda não possui apps Qlik configurados. 
            Você pode criar objetos diretamente ou configurar apps na página de Configurações.
          </Typography>
        </Alert>
      )}

      {/* Grid Layout Dinâmico - ocupa o espaço restante */}
      <Box sx={{ flex: 1, p: 2, pt: 1}}>
        <GridLayout 
          pagina="dashboard" 
          appId={appId}
          editMode={process.env.NODE_ENV === 'development' ? editMode : false}
        />
      </Box>

      {/* Informações adicionais - apenas em modo de edição e desenvolvimento */}
      {process.env.NODE_ENV === 'development' && editMode && (
        <Box sx={{ p: 2, pt: 0 }}>
          <Alert severity="info" sx={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
            color: 'white',
            '& .MuiAlert-icon': { color: 'white' }
          }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
              💡 Como usar o Dashboard Dinâmico
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '0.875rem' }}>
              • <strong>Adicionar:</strong> Use o botão "+" para adicionar objetos Qlik •{' '}
              <strong>Mover:</strong> Arraste pelo ícone de mover •{' '}
              <strong>Redimensionar:</strong> Arraste as bordas •{' '}
              <strong>Remover:</strong> Use o ícone de lixeira
            </Typography>
          </Alert>
        </Box>
      )}
    </Box>
  )
}

export default Dashboard 