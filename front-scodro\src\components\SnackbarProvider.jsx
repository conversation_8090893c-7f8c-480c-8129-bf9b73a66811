import React, { createContext, useContext, useState, useCallback } from 'react';
import { Snackbar, Alert, Box } from '@mui/material';

const SnackbarContext = createContext();

export function useSnackbar() {
  return useContext(SnackbarContext);
}

const SnackbarProvider = ({ children }) => {
  const [snackbars, setSnackbars] = useState([]);

  const showSnackbar = useCallback((message, severity = 'info') => {
    const id = Date.now() + Math.random(); // ID único para cada toast
    const newSnackbar = { 
      id, 
      message, 
      severity, 
      open: true 
    };
    
    setSnackbars((prev) => [...prev, newSnackbar]);
    
    // Auto-remover após 6 segundos
    setTimeout(() => {
      setSnackbars((prev) => prev.filter(snack => snack.id !== id));
    }, 6000);
  }, []);

  const handleClose = useCallback((id) => {
    setSnackbars((prev) => prev.filter(snack => snack.id !== id));
  }, []);

  return (
    <SnackbarContext.Provider value={{ showSnackbar }}>
      {children}
      
      {/* Container para múltiplos toasts no topo */}
      <Box
        sx={{
          position: 'fixed',
          top: 16,
          right: 16,
          zIndex: 9999,
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          maxWidth: '400px',
          pointerEvents: 'none', // Permite cliques através do container
        }}
      >
        {snackbars.map((snackbar, index) => (
          <Alert 
            key={snackbar.id}
            onClose={() => handleClose(snackbar.id)} 
            severity={snackbar.severity} 
            sx={{ 
              width: '100%',
              boxShadow: 3,
              pointerEvents: 'auto', // Permite cliques no Alert
              '& .MuiAlert-message': {
                fontSize: '0.875rem',
                fontWeight: 500,
              }
            }}
            elevation={6}
            variant="filled"
          >
            {snackbar.message}
          </Alert>
        ))}
      </Box>
    </SnackbarContext.Provider>
  );
};

export default SnackbarProvider; 