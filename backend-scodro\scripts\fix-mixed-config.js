const mongoose = require('mongoose')
const Objeto = require('../src/models/Objeto')

// Conectar ao MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scodro', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    })
    console.log('✅ Conectado ao MongoDB')
  } catch (error) {
    console.error('❌ Erro ao conectar ao MongoDB:', error)
    process.exit(1)
  }
}

// Função para corrigir configurações mixed
const fixMixedConfigs = async () => {
  try {
    console.log('🔍 Procurando objetos do tipo "mixed"...')
    
    // Buscar todos os objetos do tipo mixed
    const objetosMixed = await Objeto.find({ tipo: 'mixed' })
    
    console.log(`📊 Encontrados ${objetosMixed.length} objetos mixed`)
    
    for (const objeto of objetosMixed) {
      console.log(`\n🔧 Corrigindo objeto: ${objeto.nome} (ID: ${objeto._id})`)
      
      // Garantir que a configuração chartConfig existe
      if (!objeto.configuracao) {
        objeto.configuracao = {}
      }
      
      if (!objeto.configuracao.chartConfig) {
        objeto.configuracao.chartConfig = {}
      }
      
      // Corrigir tipoGrafico
      objeto.configuracao.chartConfig.tipoGrafico = 'mixed'
      
      // Corrigir mixedConfig baseado no nome das medidas (se disponível)
      if (!objeto.configuracao.chartConfig.mixedConfig) {
        objeto.configuracao.chartConfig.mixedConfig = {
          barSeries: [0, 1], // Primeiras duas séries como barras (valores)
          lineSeries: [2, 3] // Últimas duas séries como linhas (porcentagens)
        }
      }
      
      // Garantir dataZoom
      if (!objeto.configuracao.chartConfig.dataZoom) {
        objeto.configuracao.chartConfig.dataZoom = {
          enabled: false,
          type: 'slider',
          start: 0,
          end: 100,
          showDetail: true,
          realtime: true
        }
      }
      
      // Configurações padrão para mixed
      objeto.configuracao.chartConfig.showLegend = true
      objeto.configuracao.chartConfig.showTitle = true
      objeto.configuracao.chartConfig.showAxes = true
      objeto.configuracao.chartConfig.animacao = true
      
      console.log(`📝 Configuração corrigida:`, {
        tipoGrafico: objeto.configuracao.chartConfig.tipoGrafico,
        mixedConfig: objeto.configuracao.chartConfig.mixedConfig,
        dataZoom: objeto.configuracao.chartConfig.dataZoom
      })
      
      // Salvar as mudanças
      await objeto.save()
      console.log(`✅ Objeto "${objeto.nome}" corrigido com sucesso!`)
    }
    
    console.log(`\n🎉 Correção concluída! ${objetosMixed.length} objetos processados.`)
    
  } catch (error) {
    console.error('❌ Erro ao corrigir configurações:', error)
  }
}

// Função principal
const main = async () => {
  await connectDB()
  await fixMixedConfigs()
  await mongoose.disconnect()
  console.log('🔌 Desconectado do MongoDB')
  process.exit(0)
}

// Executar se chamado diretamente
if (require.main === module) {
  main()
}

module.exports = { fixMixedConfigs } 