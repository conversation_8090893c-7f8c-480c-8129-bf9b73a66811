import React from 'react'
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>T<PERSON>le, Button, Typography } from '@mui/material'
import { Refresh as RefreshIcon } from '@mui/icons-material'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    // Atualiza o state para renderizar a UI de fallback
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Log do erro para debugging
    console.error('ErrorBoundary capturou um erro:', error, errorInfo)
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    })
  }

  handleReload = () => {
    // Recarregar a página
    window.location.reload()
  }

  handleReset = () => {
    // Reset do estado do erro
    this.setState({ hasError: false, error: null, errorInfo: null })
  }

  render() {
    if (this.state.hasError) {
      return (
        <Box 
          sx={{ 
            p: 4, 
            minHeight: '50vh', 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            textAlign: 'center'
          }}
        >
          <Alert 
            severity="error" 
            sx={{ 
              mb: 3, 
              maxWidth: 600,
              width: '100%'
            }}
          >
            <AlertTitle>⚠️ Erro na Aplicação</AlertTitle>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Ocorreu um erro inesperado. Isso pode ser um problema temporário.
            </Typography>
            
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                <Typography variant="caption" component="pre" sx={{ fontSize: '0.75rem' }}>
                  {this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </Typography>
              </Box>
            )}
          </Alert>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button 
              variant="contained" 
              startIcon={<RefreshIcon />}
              onClick={this.handleReload}
            >
              Recarregar Página
            </Button>
            
            <Button 
              variant="outlined" 
              onClick={this.handleReset}
            >
              Tentar Novamente
            </Button>
          </Box>
        </Box>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary 