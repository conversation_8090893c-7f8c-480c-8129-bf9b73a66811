import { getQlikConfig, getRequireConfig } from '@config/qlik'

class QlikService {
  constructor() {
    this.qlik = null
    this.app = null
    this.config = null
    this.isInitialized = false
    // Verificar se está rodando em mashup (detectar se RequireJS está presente)
    this.isDevelopment = process.env.NODE_ENV === 'development' && !window.require
  }

  // Inicialização do serviço Qlik
  async initialize() {
    if (this.isInitialized) {
      return this.qlik
    }

    // Em desenvolvimento (sem RequireJS), não inicializa conexão real
    if (this.isDevelopment) {
      this.isInitialized = true
      return this.createMockQlik()
    }

    try {
      this.config = getQlikConfig()
      console.log(`🔗 Conectando ao Qlik Sense (${this.config.environment})...`, this.config)

      // Verificar se RequireJS está disponível
      if (!window.require) {
        throw new Error('RequireJS não está disponível. Certifique-se de que o mashup está sendo executado no Qlik Sense.')
      }

      // Configurar require.js
      await this.setupRequireConfig()
      
      // Carregar Qlik API
      this.qlik = await this.loadQlikAPI()
      
      this.isInitialized = true
      console.log('✅ Qlik Service inicializado com sucesso')
      
      return this.qlik
    } catch (error) {
      console.error('❌ Erro ao inicializar Qlik Service:', error)
      throw error
    }
  }

  // Configurar require.js
  setupRequireConfig() {
    return new Promise((resolve, reject) => {
      try {
        const requireConfig = getRequireConfig(this.config)
        console.log('⚙️ Configurando require.js:', requireConfig)
        
        window.require.config(requireConfig)
        resolve()
      } catch (error) {
        reject(error)
      }
    })
  }

  // Carregar API do Qlik
  loadQlikAPI() {
    return new Promise((resolve, reject) => {
      window.require(['js/qlik'], (qlik) => {
        if (qlik) {
          resolve(qlik)
        } else {
          reject(new Error('Falha ao carregar API do Qlik'))
        }
      }, (error) => {
        reject(error)
      })
    })
  }

  // Abrir app do Qlik
  async openApp(appId) {
    if (this.isDevelopment) {
      console.log(`🔧 Modo desenvolvimento: Simulando abertura do app ${appId}`)
      return this.createMockApp(appId)
    }

    if (!this.qlik) {
      await this.initialize()
    }

    try {
      console.log(`📱 Abrindo app: ${appId}`)
      this.app = this.qlik.openApp(appId, this.config)
      return this.app
    } catch (error) {
      console.error(`❌ Erro ao abrir app ${appId}:`, error)
      throw error
    }
  }

  // Obter objeto do Qlik
  async getObject(objectId, elementId) {
    if (this.isDevelopment) {
      console.log(`🔧 Modo desenvolvimento: Simulando objeto ${objectId}`)
      return this.createMockObject(objectId, elementId)
    }

    if (!this.app) {
      throw new Error('App não está aberto. Chame openApp() primeiro.')
    }

    try {
      console.log(`📊 Carregando objeto: ${objectId}`)
      const object = await this.app.getObject(elementId, objectId)
      return object
    } catch (error) {
      console.error(`❌ Erro ao carregar objeto ${objectId}:`, error)
      throw error
    }
  }

  // Criar mock do Qlik para desenvolvimento
  createMockQlik() {
    return {
      openApp: (appId) => this.createMockApp(appId),
      resize: () => console.log('🔧 Mock: resize chamado'),
      getGlobal: () => ({
        isPersonalMode: () => Promise.resolve(false),
        getAuthenticatedUser: () => Promise.resolve({ qReturn: 'mock-user' })
      })
    }
  }

  // Criar mock do App para desenvolvimento
  createMockApp(appId) {
    const mockFieldList = {
      qFieldList: {
        qItems: [
          { qName: 'Empreendimento', qCardinal: 50 },
          { qName: 'Status', qCardinal: 5 },
          { qName: 'Categoria', qCardinal: 10 },
          { qName: 'Região', qCardinal: 15 },
          { qName: 'Vendedor', qCardinal: 25 }
        ]
      }
    }

    const mockListData = {
      qListObject: {
        qDataPages: [{
          qMatrix: [
            [{ qText: 'Empreendimento A', qState: 'O' }],
            [{ qText: 'Empreendimento B', qState: 'O' }],
            [{ qText: 'Empreendimento C', qState: 'S' }],
            [{ qText: 'Empreendimento D', qState: 'O' }],
            [{ qText: 'Empreendimento E', qState: 'O' }],
            [{ qText: 'Villa Real', qState: 'O' }],
            [{ qText: 'Residencial Jardim', qState: 'O' }],
            [{ qText: 'Condomínio Sunset', qState: 'O' }],
          ]
        }]
      }
    }

    return {
      id: appId,
      getObject: (elementId, objectId) => this.createMockObject(objectId, elementId),
      
      // ✅ CORRIGIDO: Implementar getList com callback para filtros
      getList: (type, callback) => {
        console.log(`🔧 Mock: getList(${type}) chamado`)
        setTimeout(() => {
          if (type === 'FieldList') {
            callback(mockFieldList)
          } else {
            callback({ qListObject: { qDataPages: [] } })
          }
        }, 200) // Simula delay
      },
      
      // ✅ NOVO: Implementar createList com callback para filtros
      createList: (listObjectDef, callback) => {
        console.log(`🔧 Mock: createList chamado`, listObjectDef)
        setTimeout(() => {
          callback(mockListData)
        }, 300) // Simula delay
      },
      
      // ✅ NOVO: Implementar field() para seleções
      field: async (fieldName) => {
        console.log(`🔧 Mock: field(${fieldName}) chamado`)
        return {
          selectValues: async (values, toggle, softLock) => {
            console.log(`🔧 Mock: selectValues chamado`, { values, toggle, softLock })
            return Promise.resolve()
          },
          clear: async () => {
            console.log(`🔧 Mock: clear chamado`)
            return Promise.resolve()
          }
        }
      },
      
      createGenericObject: () => Promise.resolve({}),
      destroySessionObject: () => Promise.resolve()
    }
  }

  // Criar mock do Object para desenvolvimento
  createMockObject(objectId, elementId) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const element = document.getElementById(elementId)
        if (element) {
          element.innerHTML = `
            <div style="
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              border-radius: 8px;
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 20px;
              box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            ">
              <div>
                <h3 style="margin: 0 0 10px 0;">📊 Objeto Qlik</h3>
                <p style="margin: 0; opacity: 0.9;">ID: ${objectId}</p>
                <p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.7;">Modo Desenvolvimento</p>
              </div>
            </div>
          `
        }
        resolve({
          id: objectId,
          elementId: elementId,
          resize: () => console.log(`🔧 Mock: resize objeto ${objectId}`),
          destroy: () => console.log(`🔧 Mock: destroy objeto ${objectId}`)
        })
      }, 500) // Simula delay de carregamento
    })
  }

  // Redimensionar todos os objetos
  resize() {
    if (this.qlik && this.qlik.resize) {
      this.qlik.resize()
    }
  }

  // Obter configuração atual
  getConfig() {
    return this.config
  }

  // Verificar se está inicializado
  isReady() {
    return this.isInitialized
  }
}

// Instância singleton
const qlikService = new QlikService()

export default qlikService 