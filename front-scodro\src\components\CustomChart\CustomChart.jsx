import React, { useMemo, useEffect, useState, useRef } from 'react'
import { createPortal } from 'react-dom'
import { Box, Typography, Card, CardContent, useTheme, IconButton, Tooltip, Menu, MenuItem, ListItemIcon, ListItemText } from '@mui/material'
import { 
  MoreVert as MoreVertIcon, 
  Fullscreen as FullscreenIcon, 
  ZoomIn as ZoomInIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon
} from '@mui/icons-material'
import ReactEcharts from 'echarts-for-react'
import * as echarts from 'echarts'
import './CustomChart.css'

const CustomChart = ({ data, config = {}, onExpand, onRefresh, onDownload, onSettings }) => {
  const theme = useTheme()
  const containerRef = useRef(null)
  const chartRef = useRef(null)
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
  const [renderKey, setRenderKey] = useState(0) // Para forçar re-render
  const [anchorEl, setAnchorEl] = useState(null)
  const [isFullScreen, setIsFullScreen] = useState(false)
  const open = Boolean(anchorEl)

  // Handlers para o menu do botão
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  // Método para alternar fullscreen (implementação própria)
  const handleExpand = () => {
    handleMenuClose()
    
    // Executar nossa implementação de fullscreen (ignorando onExpand externo que pode interferir)
    setIsFullScreen(prevState => {
      const newState = !prevState
      
      // Bloquear/restaurar scroll do body
      if (newState) {
        document.body.style.overflow = 'hidden'
      } else {
        document.body.style.overflow = ''
      }
      
      // Forçar redimensionamento do gráfico após mudança de estado
      setTimeout(() => {
        if (chartRef.current) {
          const echartsInstance = chartRef.current.getEchartsInstance()
          if (echartsInstance) {
            echartsInstance.resize()
          }
        }
      }, 200)
      
      return newState
    })
  }

  const handleRefresh = () => {
    handleMenuClose()
    if (onRefresh) {
      onRefresh()
    } else {
      // Funcionalidade padrão - forçar re-render do gráfico
      setRenderKey(prev => prev + 1)
    }
  }

  const handleDownload = () => {
    handleMenuClose()
    if (onDownload) {
      onDownload()
    } else {
      // Funcionalidade padrão - baixar como PNG
      if (chartRef.current) {
        const echartsInstance = chartRef.current.getEchartsInstance()
        if (echartsInstance) {
          const url = echartsInstance.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e' : '#ffffff'
          })
          const link = document.createElement('a')
          link.download = `grafico-${Date.now()}.png`
          link.href = url
          link.click()
        }
      }
    }
  }

  const handleSettings = () => {
    handleMenuClose()
    if (onSettings) {
      onSettings()
    }
  }

  // 🔧 CORREÇÃO: Detectar tamanho do container e mudanças (otimizado para evitar re-renders desnecessários)
  useEffect(() => {
    if (containerRef.current) {
      const updateSize = () => {
        const rect = containerRef.current.getBoundingClientRect()
        const newSize = { width: rect.width, height: rect.height }
        
        // Só atualizar se o tamanho realmente mudou (evitar re-renders desnecessários)
        setContainerSize(prevSize => {
          if (prevSize.width !== newSize.width || prevSize.height !== newSize.height) {
            // Só forçar redimensionamento do ECharts, sem renderKey (evita "piscada")
            if (chartRef.current) {
              setTimeout(() => {
                const echartsInstance = chartRef.current.getEchartsInstance()
                if (echartsInstance) {
                  echartsInstance.resize()
                }
              }, 100)
            }
            
            return newSize
          }
          return prevSize
        })
      }
      
      // Detectar tamanho inicial
      updateSize()
      
      // Detectar redimensionamento da janela
      window.addEventListener('resize', updateSize)
      
      return () => {
        window.removeEventListener('resize', updateSize)
      }
    }
  }, [])

  // Suporte para tecla ESC em fullscreen
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isFullScreen) {
        setIsFullScreen(false)
        document.body.style.overflow = ''
      }
    }

    if (isFullScreen) {
      document.addEventListener('keydown', handleEscKey)
      return () => {
        document.removeEventListener('keydown', handleEscKey)
        document.body.style.overflow = ''
      }
    }
  }, [isFullScreen])


  
  // Registrar tema customizado do ECharts baseado no Material-UI
  useEffect(() => {
    const customTheme = {
      color: [
        theme.palette.primary.main,
        theme.palette.secondary.main,
        theme.palette.success.main,
        theme.palette.warning.main,
        theme.palette.error.main,
        theme.palette.info.main,
        '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff7f'
      ],
      backgroundColor: 'transparent',
      textStyle: {
        color: theme.palette.text.primary
      },
      title: {
        textStyle: {
          color: theme.palette.text.primary
        }
      },
      legend: {
        textStyle: {
          color: theme.palette.text.secondary
        }
      },
      categoryAxis: {
        axisLine: {
          lineStyle: {
            color: theme.palette.divider
          }
        },
        axisTick: {
          lineStyle: {
            color: theme.palette.divider
          }
        },
        axisLabel: {
          color: theme.palette.text.secondary
        },
        splitLine: {
          lineStyle: {
            color: theme.palette.divider
          }
        }
      },
      valueAxis: {
        axisLine: {
          lineStyle: {
            color: theme.palette.divider
          }
        },
        axisTick: {
          lineStyle: {
            color: theme.palette.divider
          }
        },
        axisLabel: {
          color: theme.palette.text.secondary
        },
        splitLine: {
          lineStyle: {
            color: theme.palette.divider
          }
        }
      }
    }
    
    echarts.registerTheme('materialTheme', customTheme)
  }, [theme])
  
  // Configurações padrão
  const defaultConfig = {
    showTitle: true,
    showLegend: true,
    showDataLabels: true,
    showAxes: true,
    tipoGrafico: 'bar', // bar, line, pie, area, scatter, mixed
    backgroundColor: 'transparent',
    animacao: true,
    grid: {
      top: 50,
      right: 20,
      bottom: 20,
      left: 20
    }
  }
  
  const finalConfig = { ...defaultConfig, ...config }
  
  // ✅ SIMPLIFICADO: Debug apenas do essencial
  
  // Cores padrão para gráficos
  const chartColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
    '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff7f'
  ]
  
  // Processar dados do Qlik
  const processData = () => {
    // Novo formato: dados já processados
    if (data && data.tipo === 'chart') {
      const chartData = data.dados
      
      // ✅ NOVO: Garantir que o título seja extraído corretamente
      return {
        ...chartData,
        title: data.titulo || chartData.title || 'Gráfico' // Usar titulo do data primeiro
      }
    }
    
    // Fallback para dados antigos (hypercube nativo)
    if (!data || !data.qHyperCube) {
      return null
    }
    
    const hypercube = data.qHyperCube
    const dataPages = hypercube.qDataPages || []
    
    if (dataPages.length === 0 || !dataPages[0].qMatrix) {
      return null
    }
    
    const matrix = dataPages[0].qMatrix
    const dimensions = hypercube.qDimensionInfo || []
    const measures = hypercube.qMeasureInfo || []
    
    // ✅ NOVO: Processar dados com ordenação inteligente por data
    const rawData = []
    
    // Processar cada linha da matriz primeiro
    matrix.forEach((row, rowIndex) => {
      const category = row[0]?.qText || row[0]?.qNum || 'N/A'
      const categoryString = String(category)
      
      // Tentar identificar se é uma data
      const isDateLike = /\w{3}\s\d{4}/.test(categoryString) || // "Jan 2024"
                         /\d{1,2}\/\d{1,2}\/\d{4}/.test(categoryString) || // "01/01/2024"
                         /\d{4}-\d{1,2}-\d{1,2}/.test(categoryString) // "2024-01-01"
      
      const measureData = []
      row.slice(1).forEach((cell, measureIndex) => {
        const value = cell?.qNum !== undefined ? cell.qNum : 0
        const formattedValue = cell?.qText || String(value)
        
        measureData.push({
          value: value,
          formattedValue: formattedValue,
          originalText: cell?.qText || String(value)
        })
      })
      
      rawData.push({
        category: categoryString,
        categoryOriginal: category,
        isDate: isDateLike,
        measures: measureData,
        originalIndex: rowIndex
      })
    })
    
    // ✅ NOVO: Ordenar por data se aplicável
    if (rawData.length > 0 && rawData.some(item => item.isDate)) {
      rawData.sort((a, b) => {
        try {
          // Tentar converter para Date
          const dateA = parseDate(a.category)
          const dateB = parseDate(b.category)
          
          if (dateA && dateB) {
            return dateA.getTime() - dateB.getTime() // Ordem crescente (mais antigo primeiro)
          }
        } catch (error) {
          console.warn(`⚠️ Erro ao ordenar datas:`, error)
        }
        
        // Fallback: ordenação alfabética
        return a.category.localeCompare(b.category)
      })
    }
    
    // Extrair dados ordenados
    const categories = rawData.map(item => item.category)
    const series = []
    
    // Inicializar séries baseado nas medidas
    measures.forEach((measure, index) => {
      let seriesName = measure.qFallbackTitle || `Série ${index + 1}`
      
      if (seriesName.includes('=DUAL(') || seriesName.includes('=')) {
        if (measure.qAttrExprInfo && measure.qAttrExprInfo.length > 0) {
          const attrExpr = measure.qAttrExprInfo[0]
          if (attrExpr.qFallbackTitle && !attrExpr.qFallbackTitle.includes('=')) {
            seriesName = attrExpr.qFallbackTitle
          }
        }
        
        if (seriesName.includes('=')) {
          seriesName = `Medida ${index + 1}`
        }
      }
      
      series.push({
        name: seriesName,
        data: rawData.map(item => item.measures[index] || { value: 0, formattedValue: '0', originalText: '0' })
      })
    })
    
    
    
    return {
      categories,
      series,
      title: data.qMeta?.title || 'Gráfico',
      dimensions,
      measures
    }
  }
  
  // ✅ NOVO: Função para parse inteligente de datas
  const parseDate = (dateString) => {
    try {
      // Formato "Jan 2024", "Fev 2025", etc. (português)
      const monthYearMatch = dateString.match(/(\w{3})\s(\d{4})/)
      if (monthYearMatch) {
        const monthMap = {
          'Jan': 0, 'Fev': 1, 'Mar': 2, 'Abr': 3, 'Mai': 4, 'Jun': 5,
          'Jul': 6, 'Ago': 7, 'Set': 8, 'Out': 9, 'Nov': 10, 'Dez': 11
        }
        const month = monthMap[monthYearMatch[1]]
        const year = parseInt(monthYearMatch[2])
        if (month !== undefined) {
          return new Date(year, month, 1)
        }
      }
      
      // Formato "01/01/2024"
      const ddmmyyyyMatch = dateString.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/)
      if (ddmmyyyyMatch) {
        const day = parseInt(ddmmyyyyMatch[1])
        const month = parseInt(ddmmyyyyMatch[2]) - 1 // Month is 0-indexed
        const year = parseInt(ddmmyyyyMatch[3])
        return new Date(year, month, day)
      }
      
      // Formato "2024-01-01"
      const yyyymmddMatch = dateString.match(/(\d{4})-(\d{1,2})-(\d{1,2})/)
      if (yyyymmddMatch) {
        const year = parseInt(yyyymmddMatch[1])
        const month = parseInt(yyyymmddMatch[2]) - 1
        const day = parseInt(yyyymmddMatch[3])
        return new Date(year, month, day)
      }
      
      // Tentar Date.parse como último recurso
      const parsed = new Date(dateString)
      return isNaN(parsed.getTime()) ? null : parsed
    } catch (error) {
      return null
    }
  }
  
  const getEchartsType = (tipo) => {
    switch (tipo) {
      case 'line': return 'line'
      case 'area': return 'line'
      case 'bar': return 'bar' // Barras verticais
      case 'column': return 'bar' // Barras horizontais (mas com eixos invertidos)
      case 'pie': return 'pie'
      case 'donut': return 'pie'
      case 'scatter': return 'scatter'
      case 'mixed': return 'mixed' // Novo tipo para gráficos mistos
      case 'barLine': return 'mixed' // ✅ NOVO: Alias para mixed
      case 'lineBar': return 'mixed' // ✅ NOVO: Alias para mixed
      case 'barline': return 'mixed' // ✅ NOVO: Alias para mixed (lowercase)
      case 'linebar': return 'mixed' // ✅ NOVO: Alias para mixed (lowercase)
      case 'multiKpi': return 'bar' // Fallback para multiKpi
      default: 
        return 'bar'
    }
  }
  
  // Função auxiliar para verificar se é gráfico de barras horizontais
  const isHorizontalBar = (tipo) => {
    return tipo === 'column'
  }
  
  // Gerar opções do ECharts - RESPONSIVO
  const getChartOptions = useMemo(() => {
    const processedData = processData()
    
    if (!processedData) return {}
    
    const { categories, series, title } = processedData
    const { width, height } = containerSize
    
    // ✅ NOVO: Função para detectar e evitar sobreposição de labels
    const getAntiOverlapLabelConfig = (seriesData, seriesType, usePercentage, seriesIndex) => {
      if (!finalConfig.showDataLabels) return { show: false }
      
      // Detectar valores próximos que podem se sobrepor
      const detectOverlap = (data, threshold = 0.15) => {
        const overlaps = []
        for (let i = 0; i < data.length; i++) {
          const currentValue = typeof data[i] === 'object' ? data[i].value : data[i]
          
          // Verificar sobreposição com outras séries no mesmo ponto
          for (let j = 0; j < sortedSeries.length; j++) {
            if (j !== seriesIndex) {
              const otherValue = typeof sortedSeries[j].data[i] === 'object' ? 
                sortedSeries[j].data[i].value : sortedSeries[j].data[i]
              
              // Se valores estão próximos (diferença < threshold% do maior valor)
              const maxValue = Math.max(Math.abs(currentValue), Math.abs(otherValue))
              const difference = Math.abs(currentValue - otherValue)
              
              if (maxValue > 0 && (difference / maxValue) < threshold) {
                overlaps.push(i)
              }
            }
          }
        }
        return overlaps
      }
      
      const overlappingPoints = detectOverlap(seriesData)
      
      return {
        show: finalConfig.showDataLabels,
        color: theme.palette.text.primary,
        fontSize: 11, // ✅ AUMENTADO: Era 10, agora 11
        fontWeight: 'bold', // ✅ NOVO: Fonte em negrito para melhor legibilidade
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif', // ✅ NOVO: Fonte mais legível
        // ✅ NOVO: Sombra para melhor contraste
        textBorderColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
        textBorderWidth: 2,
        textShadowColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
        textShadowBlur: 3,
        textShadowOffsetX: 1,
        textShadowOffsetY: 1,
        // ✅ ESTRATÉGIA ANTI-SOBREPOSIÇÃO: Alternar posições para evitar conflito
        position: function(pos, params, el, elRect, size) {
          const dataIndex = params.dataIndex
          const isOverlapping = overlappingPoints.includes(dataIndex)
          
          if (!isOverlapping) {
            // ✅ MELHORADO: Posições com mais espaçamento
            return seriesType === 'bar' ? 'top' : 'top'
          }
          
          // ✅ SOBREPOSIÇÃO DETECTADA: Usar posições alternativas com mais espaço
          if (seriesType === 'bar') {
            // Para barras: alternar entre 'top' e 'inside' baseado no índice da série
            return seriesIndex % 2 === 0 ? 'top' : 'inside'
          } else {
            // Para linhas: usar posições mais distantes da linha
            return seriesIndex % 2 === 0 ? [pos[0], pos[1] - 25] : [pos[0], pos[1] + 25]
          }
        },
        // ✅ NOVO: Offset adicional para melhor separação
        offset: [0, seriesType === 'line' ? -8 : -5], // Mais espaço para linhas
        formatter: function(params) {
          const dataPoint = params.data
          if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
            return dataPoint.formattedValue
          }
          if (seriesType === 'line' && usePercentage) {
            return params.value.toFixed(2) + '%'
          }
          return params.value.toLocaleString('pt-BR')
        }
      }
    }
    
    // ✅ NOVO: Ordenar categorias por data se forem datas
    let sortedCategories = [...categories]
    let sortedSeries = series.map(s => ({ ...s, data: [...s.data] }))
    
    // Verificar se as categorias são datas e ordenar
    const isDateCategories = categories.some(cat => {
      const parsed = parseDate(String(cat))
      return parsed !== null
    })
    
    if (isDateCategories) {
      // Criar array de índices com suas respectivas datas
      const categoryWithIndex = categories.map((cat, index) => ({
        category: cat,
        originalIndex: index,
        parsedDate: parseDate(String(cat))
      }))
      
      // Ordenar por data (crescente - mais antigo primeiro)
      categoryWithIndex.sort((a, b) => {
        if (a.parsedDate && b.parsedDate) {
          return a.parsedDate.getTime() - b.parsedDate.getTime() // Crescente
        }
        // Fallback para ordenação alfabética
        return String(a.category).localeCompare(String(b.category))
      })
      
      // Extrair categorias ordenadas
      sortedCategories = categoryWithIndex.map(item => item.category)
      
      // Reordenar dados das séries de acordo com a nova ordem
      sortedSeries = series.map(s => ({
        ...s,
        data: categoryWithIndex.map(item => s.data[item.originalIndex])
      }))
      
      
    }
    
    // Verificar se é gráfico de barras horizontais
    const isHorizontal = isHorizontalBar(finalConfig.tipoGrafico)
    
    // Calcular tamanhos responsivos baseados no container
    const responsiveFontSize = Math.max(Math.min(Math.min(width, height) * 0.03, 14), 8)
    const titleFontSize = Math.max(Math.min(Math.min(width, height) * 0.04, 18), 10)
    const legendFontSize = Math.max(Math.min(Math.min(width, height) * 0.025, 12), 8)
    
    // ✅ NOVO: Função inteligente para calcular margens baseadas no conteúdo
    const calculateSmartMargins = () => {
      // Calcular largura necessária para labels do eixo Y (categorias)
      let maxLabelWidth = 0
      if (sortedCategories && sortedCategories.length > 0) {
        const longestLabel = sortedCategories.reduce((longest, current) => 
          String(current).length > String(longest).length ? current : longest
        )
        // ✅ AJUSTE: Cálculo mais conservador (1px por caractere + limite menor)
        maxLabelWidth = Math.min(String(longestLabel).length * 1, width * 0.15) // Máximo 15% da largura
      }
      
      // ✅ NOVO: Calcular largura necessária para data labels (se habilitados)
      let maxDataLabelWidth = 0
      if (finalConfig.showDataLabels && sortedSeries && sortedSeries.length > 0) {
        // Encontrar o data label mais longo
        let longestDataLabel = ''
        sortedSeries.forEach(serie => {
          if (serie.data && Array.isArray(serie.data)) {
            serie.data.forEach(dataPoint => {
              if (typeof dataPoint === 'object' && dataPoint.details) {
                const detalhes = dataPoint.details
                const partes = []
                if (detalhes.percentual) partes.push(detalhes.percentual)
                if (detalhes.quantidade) partes.push(detalhes.quantidade)
                if (detalhes.valor) partes.push(detalhes.valor)
                const labelText = partes.join(' | ')
                if (labelText.length > longestDataLabel.length) {
                  longestDataLabel = labelText
                }
              }
            })
          }
        })
        // Estimar largura do data label (aproximadamente 5px por caractere para labels)
        maxDataLabelWidth = Math.min(longestDataLabel.length * 5, width * 0.3)
      }
      
      // Margens inteligentes
      const smartMargins = {
        top: 15, // ✅ FIXO: Não precisa mais de espaço para título no ECharts
        right: Math.max(
          width * 0.03, 
          maxDataLabelWidth + 5, // Espaço extra para data labels
          60
        ), // Margem mínima à direita
        bottom: Math.max(height * 0.08, 25), // Espaço para labels do eixo X
        left: isHorizontal ? 
          Math.max(maxLabelWidth + 5, 30) : // Para barras horizontais, mínimo 30px
          Math.max(width * 0.04, 30), // Para barras verticais, margem bem menor
        containLabel: true
      }
      
      return smartMargins
    }
    
    // Grid responsivo - INTELIGENTE
    const responsiveGrid = calculateSmartMargins()
    
    
    
    // Configuração base consistente - RESPONSIVA
    const baseOptions = {
      backgroundColor: 'transparent',
      animation: finalConfig.animacao,
      color: chartColors,
      
      // Configuração global de texto - RESPONSIVA
      textStyle: {
        color: theme.palette.text.primary,
        fontSize: responsiveFontSize
      },
      
      title: undefined, // ✅ REMOVIDO: Título agora é renderizado separadamente como Typography
      
      // ✅ NOVO: Configuração de DataZoom
      dataZoom: finalConfig.dataZoom?.enabled ? (() => {
        const dataZoomConfig = []
        
        if (finalConfig.dataZoom.type === 'slider' || finalConfig.dataZoom.type === 'both') {
          dataZoomConfig.push({
            type: 'slider',
            start: finalConfig.dataZoom.start || 0,
            end: finalConfig.dataZoom.end || 100,
            showDetail: finalConfig.dataZoom.showDetail !== false,
            realtime: finalConfig.dataZoom.realtime !== false,
            bottom: finalConfig.dataZoom.type === 'both' ? 45 : 10,
            textStyle: {
              color: theme.palette.text.secondary,
              fontSize: Math.max(responsiveFontSize - 2, 8)
            },
            handleStyle: {
              color: theme.palette.primary.main,
              borderColor: theme.palette.primary.main
            },
            fillerColor: `${theme.palette.primary.main}20`,
            borderColor: theme.palette.divider,
            backgroundColor: theme.palette.background.paper,
            selectedDataBackground: {
              color: `${theme.palette.primary.main}40`
            }
          })
        }
        
        if (finalConfig.dataZoom.type === 'inside' || finalConfig.dataZoom.type === 'both') {
          dataZoomConfig.push({
            type: 'inside',
            start: finalConfig.dataZoom.start || 0,
            end: finalConfig.dataZoom.end || 100,
            zoomOnMouseWheel: true,
            moveOnMouseMove: true,
            preventDefaultMouseMove: true
          })
        }
        
        return dataZoomConfig
      })() : undefined,
      
      tooltip: {
        trigger: finalConfig.tipoGrafico === 'pie' || finalConfig.tipoGrafico === 'donut' ? 'item' : 'axis',
        backgroundColor: theme.palette.background.paper,
        borderColor: theme.palette.divider,
        borderWidth: 1,
        textStyle: {
          color: theme.palette.text.primary,
          fontSize: responsiveFontSize
        },
        confine: true,
        // Formatter personalizado para exibir valores formatados
        formatter: function(params) {
          if (Array.isArray(params)) {
            // Tooltip para múltiplas séries
            let result = `<strong>${params[0].axisValue}</strong><br/>`
            params.forEach(param => {
              const dataPoint = param.data
              let displayValue = param.value
              
              // ✅ NOVO: Formato específico para dados DUAL() - percentual | quantidade | valor
              if (typeof dataPoint === 'object' && dataPoint.details) {
                const detalhes = dataPoint.details
                const partes = []
                
                if (detalhes.percentual) partes.push(detalhes.percentual)
                if (detalhes.quantidade) partes.push(detalhes.quantidade)
                if (detalhes.valor) partes.push(detalhes.valor)
                
                displayValue = partes.join(' | ')
              } else if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
                displayValue = dataPoint.formattedValue
              } else if (typeof dataPoint === 'object' && dataPoint.originalText && dataPoint.originalText.includes('\n')) {
                // Fallback para texto original formatado
                const linhas = dataPoint.originalText.split('\n')
                displayValue = linhas[0] || param.value // Usar primeira linha (percentual)
              }
              
              result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${displayValue}<br/>`
            })
            return result
          } else {
            // Tooltip para série única
            const dataPoint = params.data
            let displayValue = params.value
            
            // ✅ NOVO: Formato específico para dados DUAL() - percentual | quantidade | valor
            if (typeof dataPoint === 'object' && dataPoint.details) {
              const detalhes = dataPoint.details
              const partes = []
              
              if (detalhes.percentual) partes.push(detalhes.percentual)
              if (detalhes.quantidade) partes.push(detalhes.quantidade)
              if (detalhes.valor) partes.push(detalhes.valor)
              
              displayValue = partes.join(' | ')
            } else if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
              displayValue = dataPoint.formattedValue
            } else if (typeof dataPoint === 'object' && dataPoint.originalText && dataPoint.originalText.includes('\n')) {
              // Fallback para texto original formatado
              const linhas = dataPoint.originalText.split('\n')
              displayValue = linhas[0] || params.value // Usar primeira linha (percentual)
            }
            
            return `<strong>${params.name}</strong><br/>
                    <span style="color:${params.color}">●</span> ${params.seriesName}: ${displayValue}`
          }
        }
      },
      
      legend: finalConfig.showLegend && sortedSeries.length > 1 ? {
        bottom: finalConfig.dataZoom?.enabled ? 70 : 10, // Ajustar posição se dataZoom estiver habilitado
        type: 'scroll',
        textStyle: {
          color: theme.palette.text.secondary,
          fontSize: legendFontSize
        }
      } : undefined
    }
    
    // Configurações específicas por tipo de gráfico
    switch (finalConfig.tipoGrafico) {
      case 'pie':
      case 'donut':
        return {
          ...baseOptions,
          series: [{
            type: 'pie',
            radius: finalConfig.tipoGrafico === 'donut' ? ['40%', '70%'] : '70%',
            center: ['50%', '50%'],
            data: sortedCategories.map((name, index) => {
              const seriesData = sortedSeries[0]?.data[index]
              const value = typeof seriesData === 'object' ? seriesData.value : (seriesData || 0)
              return {
                name,
                value: value,
                // Manter referência ao valor formatado para tooltip
                originalData: seriesData
              }
            }),
            label: {
              show: finalConfig.showDataLabels,
              color: theme.palette.text.primary,
              fontSize: 11,
              fontWeight: 'bold',
              textBorderColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
              textBorderWidth: 2,
              formatter: function(params) {
                // Usar valor formatado se disponível
                const originalData = params.data.originalData
                if (typeof originalData === 'object' && originalData.formattedValue) {
                  return `${params.name}\n${originalData.formattedValue}`
                }
                return `${params.name}\n${params.value}`
              }
            },
            labelLine: {
              lineStyle: {
                color: theme.palette.text.secondary
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.3)'
              }
            }
          }]
        }
        
      case 'area':
        return {
          ...baseOptions,
          grid: {
            ...responsiveGrid,
            top: finalConfig.showTitle ? Math.max(titleFontSize + 15, 35) : 15,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: sortedCategories,
            boundaryGap: false,
            axisLabel: {
              show: finalConfig.showAxes,
              color: theme.palette.text.secondary,
              fontSize: 11,
              rotate: sortedCategories.some(cat => String(cat).length > 8) ? 45 : 0
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                width: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              show: finalConfig.showAxes,
              color: theme.palette.text.secondary,
              fontSize: 11
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                type: 'dashed',
                opacity: 0.5
              }
            }
          },
          series: sortedSeries.map((s, index) => ({
            ...s,
            type: 'line',
            areaStyle: {
              opacity: 0.3
            },
            smooth: true,
            symbol: 'circle',
            symbolSize: 4,
            lineStyle: {
              width: 2
            },
            label: {
              show: finalConfig.showDataLabels,
              color: theme.palette.text.primary,
              fontSize: 11,
              fontWeight: 'bold',
              textBorderColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
              textBorderWidth: 2,
              formatter: function(params) {
                // ✅ NOVO: Formato específico para dados DUAL() - percentual | quantidade | valor
                const dataPoint = params.data
                if (typeof dataPoint === 'object' && dataPoint.details) {
                  const detalhes = dataPoint.details
                  const partes = []
                  
                  if (detalhes.percentual) partes.push(detalhes.percentual)
                  if (detalhes.quantidade) partes.push(detalhes.quantidade)
                  if (detalhes.valor) partes.push(detalhes.valor)
                  
                  return partes.join(' | ')
                } else if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
                  return dataPoint.formattedValue
                }
                return params.value
              }
            },
            itemStyle: {
              color: chartColors[index % chartColors.length]
            },
            // Configurar dados para incluir valores formatados
            data: s.data.map(dataPoint => {
              if (typeof dataPoint === 'object') {
                return {
                  value: dataPoint.value,
                  formattedValue: dataPoint.formattedValue,
                  details: dataPoint.details,
                  originalText: dataPoint.originalText
                }
              }
              return dataPoint
            })
          }))
        }
        
      // Tratamento especial para barras horizontais (column)
      case 'column':
        // ✅ NOVO: Ordenar dados por valor decrescente (maior para menor) para barras horizontais
        const dataWithIndex = sortedCategories.map((category, index) => ({
          category: category,
          originalIndex: index,
          // Usar o valor da primeira série para ordenação (assumindo que é a série principal)
          value: sortedSeries[0]?.data[index]?.value || sortedSeries[0]?.data[index] || 0
        }))

        // Ordenar por valor decrescente (maior para menor)
        dataWithIndex.sort((a, b) => {
          const valueA = typeof a.value === 'object' ? a.value.value : a.value
          const valueB = typeof b.value === 'object' ? b.value.value : b.value
          return valueA - valueB// Decrescente: maior primeiro (de cima para baixo)
        })

        // Extrair categorias ordenadas e reordenar dados das séries
        const sortedCategoriesDesc = dataWithIndex.map(item => item.category)
        const sortedSeriesDesc = sortedSeries.map(serie => ({
          ...serie,
          data: dataWithIndex.map(item => serie.data[item.originalIndex])
        }))

        return {
          ...baseOptions,
          grid: {
            ...responsiveGrid,
            containLabel: true
          },
          xAxis: {
            type: 'value',
            axisLabel: {
              show: finalConfig.showAxes,
              color: theme.palette.text.secondary,
              fontSize: 11
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                width: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                type: 'dashed',
                opacity: 0.5
              }
            }
          },
          yAxis: {
            type: 'category',
            data: sortedCategoriesDesc, // ✅ CORRIGIDO: Usar categorias ordenadas por valor
            axisLabel: {
              show: finalConfig.showAxes,
              color: theme.palette.text.secondary,
              fontSize: 11,
              interval: 0
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                width: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          series: sortedSeriesDesc.map((s, index) => ({ // ✅ CORRIGIDO: Usar séries ordenadas
            ...s,
            type: 'bar',
            label: {
              show: finalConfig.showDataLabels,
              color: theme.palette.text.primary,
              fontSize: 11,
              fontWeight: 'bold',
              textBorderColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
              textBorderWidth: 2,
              position: 'right',
              formatter: function(params) {
                // ✅ NOVO: Formato específico para dados DUAL() - percentual | quantidade | valor
                const dataPoint = params.data
                if (typeof dataPoint === 'object' && dataPoint.details) {
                  const detalhes = dataPoint.details
                  const partes = []
                  
                  if (detalhes.percentual) partes.push(detalhes.percentual)
                  if (detalhes.quantidade) partes.push(detalhes.quantidade)
                  if (detalhes.valor) partes.push(detalhes.valor)
                  
                  return partes.join(' | ')
                } else if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
                  return dataPoint.formattedValue
                }
                return params.value
              }
            },
            itemStyle: {
              color: chartColors[index % chartColors.length],
              borderRadius: [0, 4, 4, 0] // Bordas arredondadas à direita para barras horizontais
            },
            // Configurar dados para incluir valores formatados
            data: s.data.map(dataPoint => {
              if (typeof dataPoint === 'object') {
                // ✅ CORRIGIDO: Para linhas (percentuais), converter valores decimais para percentual
                let value = dataPoint.value || 0
                if (dataPoint.formattedValue && dataPoint.formattedValue.includes('%')) {
                  value = parseFloat(dataPoint.formattedValue.replace('%', ''))
                }
                
                return {
                  value: value,
                  formattedValue: dataPoint.formattedValue,
                  details: dataPoint.details,
                  originalText: dataPoint.originalText
                }
              }
              return dataPoint
            })
          }))
        }
        
      // ✅ VERIFICAÇÃO: Só aplicar mixed se realmente tiver configuração adequada
      case 'mixed':
        // ✅ NOVO: Extrair configurações de porcentagem
        const usePercentageScaleMixed = finalConfig.mixedConfig?.usePercentageScale === true
        const percentageAxisMinMixed = finalConfig.mixedConfig?.percentageAxisMin || 0
        const percentageAxisMaxMixed = finalConfig.mixedConfig?.percentageAxisMax || 100
        const autoDetectPercentageMixed = finalConfig.mixedConfig?.autoDetectPercentage !== false
        const valueAxisNameMixed = finalConfig.mixedConfig?.valueAxisName || 'Valores'
        const percentageAxisNameMixed = finalConfig.mixedConfig?.percentageAxisName || 'Porcentagem (%)'
        
        if (!finalConfig.mixedConfig || sortedSeries.length < 2) {
          // ✅ MIXED INTELIGENTE: Detectar automaticamente quais séries são porcentagens vs valores
          const mixedConfigInteligente = { barSeries: [], lineSeries: [] }
          
          sortedSeries.forEach((serie, index) => {
            const nomeSerie = serie.name.toLowerCase()
            const isPercentage = usePercentageScaleMixed && (nomeSerie.includes('% ') || 
                                 nomeSerie.startsWith('%') ||
                                 nomeSerie.includes('perc') || 
                                 nomeSerie.includes('taxa') ||
                                 nomeSerie.includes('fixa/prev') || 
                                 nomeSerie.includes('porcentagem') ||
                                 nomeSerie.includes('percentual'))
            
            if (isPercentage) {
              mixedConfigInteligente.lineSeries.push(index)
            } else {
              mixedConfigInteligente.barSeries.push(index)
            }
          })
          
          return {
            ...baseOptions,
            grid: {
              ...responsiveGrid,
              top: 15, // ✅ FIXO: Não precisa mais de espaço para título no ECharts
              bottom: (() => {
                let bottomSpace = 40
                if (finalConfig.dataZoom?.enabled) {
                  bottomSpace += 50
                }
                return bottomSpace
              })(),
              containLabel: true
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
                crossStyle: {
                  color: theme.palette.text.secondary
                }
              },
              backgroundColor: theme.palette.background.paper,
              borderColor: theme.palette.divider,
              borderWidth: 1,
              textStyle: {
                color: theme.palette.text.primary,
                fontSize: responsiveFontSize
              }
            },
            legend: {
              data: sortedSeries.map(s => s.name),
              bottom: finalConfig.dataZoom?.enabled ? 75 : 10,
              textStyle: {
                color: theme.palette.text.secondary,
                fontSize: legendFontSize
              }
            },
            xAxis: [
              {
                type: 'category',
                data: sortedCategories,
                axisPointer: {
                  type: 'shadow'
                },
                axisLabel: {
                  show: finalConfig.showAxes,
                  color: theme.palette.text.secondary,
                  fontSize: 11,
                  rotate: sortedCategories.some(cat => String(cat).length > 8) ? 45 : 0
                },
                axisLine: {
                  show: true,
                  lineStyle: {
                    color: theme.palette.divider,
                    width: 1
                  }
                },
                axisTick: {
                  show: false
                }
              }
            ],
            yAxis: [
              {
                type: 'value',
                name: usePercentageScaleMixed ? valueAxisNameMixed : '', // ✅ Só mostrar nome se usar porcentagem
                position: 'left',
                axisLabel: {
                  show: finalConfig.showAxes,
                  color: theme.palette.text.secondary,
                  fontSize: 11,
                  formatter: function(value) {
                    return value.toLocaleString('pt-BR')
                  }
                },
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    color: theme.palette.divider,
                    type: 'dashed',
                    opacity: 0.5
                  }
                }
              },
              {
                type: 'value',
                name: usePercentageScaleMixed ? percentageAxisNameMixed : '', // ✅ Só mostrar nome se usar porcentagem
                position: 'right',
                min: usePercentageScaleMixed ? percentageAxisMinMixed : undefined,
                max: usePercentageScaleMixed ? function(value) {
                  return Math.max(value.max * 1.1, percentageAxisMaxMixed)
                } : undefined,
                axisLabel: {
                  show: finalConfig.showAxes,
                  color: theme.palette.text.secondary,
                  fontSize: 11,
                  formatter: function(value) {
                    if (usePercentageScaleMixed) {
                      return value.toFixed(1) + '%'
                    }
                    return value.toLocaleString('pt-BR')
                  }
                },
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                splitLine: {
                  show: false
                }
              }
            ],
            series: sortedSeries.map((s, index) => {
              const isBarSeries = mixedConfigInteligente.barSeries.includes(index)
              const isLineSeries = usePercentageScaleMixed && mixedConfigInteligente.lineSeries.includes(index)
              const seriesType = isBarSeries ? 'bar' : 'line'
              
              return {
                name: s.name,
                type: seriesType,
                yAxisIndex: isLineSeries ? 1 : 0,
                data: s.data.map(dataPoint => {
                  if (typeof dataPoint === 'object') {
                    let value = dataPoint.value || 0
                    if (seriesType === 'line' && usePercentageScaleMixed && autoDetectPercentageMixed && value <= 1 && value > 0) {
                      value = value * 100
                    }
                    
                    return {
                      value: value,
                      formattedValue: dataPoint.formattedValue,
                      details: dataPoint.details,
                      originalText: dataPoint.originalText
                    }
                  }
                  return dataPoint || 0
                }),
                ...(seriesType === 'bar' && {
                  itemStyle: {
                    color: chartColors[index % chartColors.length],
                    borderRadius: [4, 4, 0, 0]
                  },
                  // ✅ APLICAR ANTI-SOBREPOSIÇÃO
                  label: getAntiOverlapLabelConfig(s.data, 'bar', false, index)
                }),
                ...(seriesType === 'line' && {
                  smooth: true,
                  symbol: 'circle',
                  symbolSize: 6,
                  lineStyle: {
                    width: 3,
                    color: chartColors[index % chartColors.length]
                  },
                  itemStyle: {
                    color: chartColors[index % chartColors.length]
                  },
                  tooltip: {
                    valueFormatter: function (value) {
                      if (usePercentageScaleMixed) {
                        return value.toFixed(2) + '%'
                      }
                      return value.toLocaleString('pt-BR')
                    }
                  },
                  // ✅ APLICAR ANTI-SOBREPOSIÇÃO
                  label: getAntiOverlapLabelConfig(s.data, 'line', usePercentageScaleMixed, index)
                })
              }
            })
          }
        }
        
        // ✅ MIXED COMPLETO: Template para gráficos mistos com configuração adequada
        return {
          ...baseOptions,
          grid: {
            ...responsiveGrid,
            top: 15, // ✅ FIXO: Não precisa mais de espaço para título no ECharts
            bottom: (() => {
              let bottomSpace = 40
              if (finalConfig.dataZoom?.enabled) {
                bottomSpace += 50
              }
              return bottomSpace
            })(),
            containLabel: true
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: theme.palette.text.secondary
              }
            },
            backgroundColor: theme.palette.background.paper,
            borderColor: theme.palette.divider,
            borderWidth: 1,
            textStyle: {
              color: theme.palette.text.primary,
              fontSize: responsiveFontSize
            },
            formatter: function(params) {
              if (!Array.isArray(params)) return ''
              
              let result = `<strong>${params[0].axisValue}</strong><br/>`
              params.forEach(param => {
                const dataPoint = param.data
                let displayValue = param.value
                
                if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
                  displayValue = dataPoint.formattedValue
                } else if (typeof dataPoint === 'object' && dataPoint.details) {
                  const detalhes = dataPoint.details
                  if (detalhes.percentual) {
                    displayValue = detalhes.percentual
                  } else if (detalhes.quantidade) {
                    displayValue = detalhes.quantidade
                  } else if (detalhes.valor) {
                    displayValue = detalhes.valor
                  }
                } else if (typeof param.value === 'number') {
                  const mixedConfig = finalConfig.mixedConfig
                  const serieIndex = sortedSeries.findIndex(s => s.name === param.seriesName)
                  const isLineSeries = usePercentageScaleMixed && mixedConfig.lineSeries?.includes(serieIndex)
                  
                  if (isLineSeries) {
                    if (autoDetectPercentageMixed && param.value <= 1) {
                      displayValue = (param.value * 100).toFixed(2) + '%'
                    } else {
                      displayValue = param.value.toFixed(2) + '%'
                    }
                  } else {
                    displayValue = param.value.toLocaleString('pt-BR')
                  }
                }
                
                result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${displayValue}<br/>`
              })
              return result
            }
          },
          legend: {
            data: sortedSeries.map(s => s.name),
            bottom: finalConfig.dataZoom?.enabled ? 55 : 10,
            textStyle: {
              color: theme.palette.text.secondary,
              fontSize: legendFontSize
            }
          },
          xAxis: [
            {
              type: 'category',
              data: sortedCategories,
              axisPointer: {
                type: 'shadow'
              },
              axisLabel: {
                show: finalConfig.showAxes,
                color: theme.palette.text.secondary,
                fontSize: 11,
                rotate: sortedCategories.some(cat => String(cat).length > 8) ? 45 : 0
              },
              axisLine: {
                show: true,
                lineStyle: {
                  color: theme.palette.divider,
                  width: 1
                }
              },
              axisTick: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: usePercentageScaleMixed ? valueAxisNameMixed : '', // ✅ Só mostrar nome se usar porcentagem
              position: 'left',
              axisLabel: {
                show: finalConfig.showAxes,
                color: theme.palette.text.secondary,
                fontSize: 11,
                formatter: function(value) {
                  return value.toLocaleString('pt-BR')
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: theme.palette.divider,
                  type: 'dashed',
                  opacity: 0.5
                }
              }
            },
            {
              type: 'value',
              name: usePercentageScaleMixed ? percentageAxisNameMixed : '', // ✅ Só mostrar nome se usar porcentagem
              position: 'right',
              min: usePercentageScaleMixed ? percentageAxisMinMixed : undefined,
              max: usePercentageScaleMixed ? function(value) {
                return Math.max(value.max * 1.1, percentageAxisMaxMixed)
              } : undefined,
              axisLabel: {
                show: finalConfig.showAxes,
                color: theme.palette.text.secondary,
                fontSize: 11,
                formatter: function(value) {
                  if (usePercentageScaleMixed) {
                    return value.toFixed(1) + '%'
                  }
                  return value.toLocaleString('pt-BR')
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: sortedSeries.map((s, index) => {
            const mixedConfig = finalConfig.mixedConfig
            const isBarSeries = mixedConfig.barSeries?.includes(index)
            const isLineSeries = mixedConfig.lineSeries?.includes(index)
            
            let seriesType = 'bar'
            if (isLineSeries) {
              seriesType = 'line'
            } else if (isBarSeries) {
              seriesType = 'bar'
            } else {
              if (usePercentageScaleMixed) {
                const nomeSerie = s.name.toLowerCase()
                const isPercentage = nomeSerie.includes('% ') || 
                                     nomeSerie.startsWith('%') ||
                                     nomeSerie.includes('perc') || 
                                     nomeSerie.includes('taxa') ||
                                     nomeSerie.includes('fixa/prev') || 
                                     nomeSerie.includes('porcentagem') ||
                                     nomeSerie.includes('percentual')
                seriesType = isPercentage ? 'line' : 'bar'
              }
            }
            
            return {
              name: s.name,
              type: seriesType,
              yAxisIndex: (seriesType === 'line' && usePercentageScaleMixed) ? 1 : 0,
              data: s.data.map(dataPoint => {
                if (typeof dataPoint === 'object') {
                  let value = dataPoint.value || 0
                  if (seriesType === 'line' && usePercentageScaleMixed && autoDetectPercentageMixed && value <= 1 && value > 0) {
                    value = value * 100
                  }
                  
                  return {
                    value: value,
                    formattedValue: dataPoint.formattedValue,
                    details: dataPoint.details,
                    originalText: dataPoint.originalText
                  }
                }
                return dataPoint || 0
              }),
              ...(seriesType === 'bar' && {
                tooltip: {
                  valueFormatter: function (value) {
                    return value.toLocaleString('pt-BR')
                  }
                },
                itemStyle: {
                  color: chartColors[index % chartColors.length],
                  borderRadius: [4, 4, 0, 0]
                },
                label: getAntiOverlapLabelConfig(s.data, 'bar', false, index)
              }),
              ...(seriesType === 'line' && {
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                  width: 3,
                  color: chartColors[index % chartColors.length]
                },
                itemStyle: {
                  color: chartColors[index % chartColors.length]
                },
                tooltip: {
                  valueFormatter: function (value) {
                    if (usePercentageScaleMixed) {
                      return value.toFixed(2) + '%'
                    }
                    return value.toLocaleString('pt-BR')
                  }
                },
                label: getAntiOverlapLabelConfig(s.data, 'line', usePercentageScaleMixed, index)
              })
            }
          })
        }
        
      default: // bar, line e outros (barras verticais)
        return {
          ...baseOptions,
          grid: {
            ...responsiveGrid,
            top: 15, // ✅ FIXO: Não precisa mais de espaço para título no ECharts
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: sortedCategories,
            axisLabel: {
              show: finalConfig.showAxes,
              color: theme.palette.text.secondary,
              fontSize: 11,
              rotate: sortedCategories.some(cat => String(cat).length > 8) ? 45 : 0,
              interval: 0
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                width: 1
              }
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              show: finalConfig.showAxes,
              color: theme.palette.text.secondary,
              fontSize: 11
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: theme.palette.divider,
                type: 'dashed',
                opacity: 0.5
              }
            }
          },
          series: sortedSeries.map((s, index) => ({
            ...s,
            type: getEchartsType(finalConfig.tipoGrafico),
            smooth: finalConfig.tipoGrafico === 'line',
            symbol: finalConfig.tipoGrafico === 'line' ? 'circle' : 'none',
            symbolSize: finalConfig.tipoGrafico === 'line' ? 4 : 0,
            lineStyle: finalConfig.tipoGrafico === 'line' ? {
              width: 2
            } : undefined,
            label: {
              show: finalConfig.showDataLabels,
              color: theme.palette.text.primary,
              fontSize: 11,
              fontWeight: 'bold',
              textBorderColor: theme.palette.mode === 'dark' ? '#000000' : '#ffffff',
              textBorderWidth: 2,
              position: finalConfig.tipoGrafico === 'bar' ? 'top' : 'inside',
              formatter: function(params) {
                // ✅ NOVO: Formato específico para dados DUAL() - percentual | quantidade | valor
                const dataPoint = params.data
                if (typeof dataPoint === 'object' && dataPoint.details) {
                  const detalhes = dataPoint.details
                  const partes = []
                  
                  if (detalhes.percentual) partes.push(detalhes.percentual)
                  if (detalhes.quantidade) partes.push(detalhes.quantidade)
                  if (detalhes.valor) partes.push(detalhes.valor)
                  
                  return partes.join(' | ')
                } else if (typeof dataPoint === 'object' && dataPoint.formattedValue) {
                  return dataPoint.formattedValue
                }
                return params.value
              }
            },
            itemStyle: {
              color: chartColors[index % chartColors.length],
              borderRadius: finalConfig.tipoGrafico === 'bar' ? [4, 4, 0, 0] : 0
            },
            // Configurar dados para incluir valores formatados
            data: s.data.map(dataPoint => {
              if (typeof dataPoint === 'object') {
                return {
                  value: dataPoint.value,
                  formattedValue: dataPoint.formattedValue,
                  details: dataPoint.details,
                  originalText: dataPoint.originalText
                }
              }
              return dataPoint
            })
          }))
        }
    }
  }, [data, finalConfig, theme, chartColors, containerSize])
  
  const processedData = processData()
  
  if (!processedData) {
    return (
      <Card sx={{ height: '100%' }}>
        <CardContent sx={{ 
          height: '100%', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}>
          <Typography color="text.secondary">
            Dados do gráfico não disponíveis
          </Typography>
        </CardContent>
      </Card>
    )
  }
  
  // Componente fullscreen renderizado via Portal
  const FullscreenChart = () => {
    if (!isFullScreen) return null

    // Criar overlay de fundo que captura todos os cliques
    const overlayStyle = {
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      zIndex: 99999, // Z-index extremamente alto
      backgroundColor: theme.palette.mode === 'dark' 
        ? theme.palette.background.default 
        : theme.palette.background.paper,
      display: 'flex',
      flexDirection: 'column',
      padding: '20px',
      // Força que este elemento fique acima de tudo
      pointerEvents: 'auto',
      isolation: 'isolate'
    }

    return createPortal(
      <div style={overlayStyle}>
        {/* Botão de fechar */}
        <button
          onClick={() => {
            setIsFullScreen(false)
            document.body.style.overflow = ''
          }}
          style={{
            position: 'absolute',
            top: '15px',
            right: '15px',
            zIndex: 999999,
            backgroundColor: 'rgba(255, 0, 0, 0.8)',
            color: 'white',
            width: '60px',
            height: '60px',
            border: '3px solid white',
            borderRadius: '50%',
            fontSize: '24px',
            fontWeight: 'bold',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 4px 20px rgba(0,0,0,0.5)',
            transition: 'all 0.2s ease'
          }}
          onMouseEnter={(e) => {
            e.target.style.transform = 'scale(1.2)'
            e.target.style.backgroundColor = 'rgba(255, 0, 0, 1)'
          }}
          onMouseLeave={(e) => {
            e.target.style.transform = 'scale(1)'
            e.target.style.backgroundColor = 'rgba(255, 0, 0, 0.8)'
          }}
        >
          ✕
        </button>

        {/* Título do gráfico em fullscreen */}
        {processedData?.title && (
          <div style={{
            color: theme.palette.text.primary,
            marginBottom: '20px',
            textAlign: 'center',
            fontSize: '2.5rem',
            fontWeight: 'normal',
            fontFamily: 'Roboto, Arial, sans-serif'
          }}>
            {processedData.title}
          </div>
        )}

        {/* Container do gráfico em fullscreen */}
        <div style={{ 
          flex: 1,
          width: '100%',
          height: '100%',
          position: 'relative'
        }}>
          <ReactEcharts
            key={`fullscreen-chart-${renderKey}-portal`}
            option={getChartOptions}
            style={{ 
              height: '100%', 
              width: '100%',
              position: 'absolute',
              top: 0,
              left: 0
            }}
            opts={{ 
              renderer: 'canvas',
              devicePixelRatio: window.devicePixelRatio || 1,
              useDirtyRect: false
            }}
            theme="materialTheme"
            notMerge={true}
            lazyUpdate={false}
            onEvents={{
              finished: () => {
                // ECharts renderizado com sucesso
              }
            }}
          />
        </div>
      </div>,
      document.body
    )
  }
  


  return (
    <>
      {/* Componente fullscreen via Portal */}
      <FullscreenChart />
      
      {/* Componente normal */}
      <Card 
        ref={containerRef}
        className="custom-chart-card shadow-transition"
        sx={{ 
          height: '100%', 
          overflow: 'hidden',
          position: 'relative'
        }}
      >
      {/* ✅ NOVO: Título separado do ECharts */}
      {finalConfig.showTitle && processedData?.title && (
        <Box
          sx={{
            px: 2,
            pt: 2,
            pb: 1,
            position: 'relative'
          }}
        >
          <Typography
            variant="h6"
            sx={{
              fontSize: Math.max(Math.min(Math.min(containerSize.width, containerSize.height) * 0.04, 18), 12),
              fontWeight: 'normal', // ✅ FontWeight mais suave
              color: theme.palette.text.primary,
              mb: 1,
              textAlign: 'left' // ✅ Alinhado à esquerda
            }}
          >
            {processedData.title}
          </Typography>
          {/* ✅ NOVO: Linha sutil embaixo do título */}
          <Box
            sx={{
              width: '100%',
              height: '1px',
              background: `linear-gradient(90deg, ${theme.palette.divider} 0%, transparent 100%)`,
              mb: 1
            }}
          />
        </Box>
      )}
      
      {/* ✅ Container do ECharts sem título */}
      <Box sx={{ 
        height: finalConfig.showTitle && processedData?.title ? 'calc(100% - 60px)' : '100%', // ✅ Ajustar altura baseado no título
        p: 1,
        pt: finalConfig.showTitle && processedData?.title ? 0 : 1, // ✅ Remover padding top se há título
        '& .echarts-for-react': {
          height: '100% !important',
          width: '100% !important'
        }
      }}>
        <ReactEcharts
          ref={chartRef}
          option={getChartOptions}
          style={{ 
            height: '100%', 
            width: '100%' 
          }}
          opts={{ 
            renderer: 'canvas',
            devicePixelRatio: window.devicePixelRatio || 1,
            useDirtyRect: false
          }}
          theme="materialTheme"
          notMerge={true}
          lazyUpdate={false}
          onEvents={{
            finished: () => {
              // ECharts renderizado com sucesso
            }
          }}
        />
      </Box>

      {/* 🔧 BOTÃO SEMPRE VISÍVEL - Evita re-renders por hover */}
      <Box
        className="chart-action-button visible"
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          opacity: 1,
          zIndex: 1000
        }}
      >
                  <Tooltip title="Opções do gráfico" placement="left">
          <IconButton
            size="small"
            onClick={handleMenuClick}
            sx={{
              backgroundColor: theme.palette.mode === 'dark' 
                ? 'rgba(255, 255, 255, 0.1)' 
                : 'rgba(0, 0, 0, 0.05)',
              backdropFilter: 'blur(8px)',
              border: `1px solid ${theme.palette.mode === 'dark' 
                ? 'rgba(255, 255, 255, 0.1)' 
                : 'rgba(0, 0, 0, 0.1)'}`,
              '&:hover': {
                backgroundColor: theme.palette.mode === 'dark' 
                  ? 'rgba(255, 255, 255, 0.2)' 
                  : 'rgba(0, 0, 0, 0.1)',
                transform: 'scale(1.05)'
              },
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)', // Transição mais suave
              willChange: 'transform, background-color' // Otimização CSS
            }}
          >
            <MoreVertIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        <Menu
          className="chart-options-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleMenuClose}
          MenuListProps={{
            'aria-labelledby': 'chart-options-button',
          }}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          sx={{
            '& .MuiPaper-root': {
              minWidth: 160,
              boxShadow: theme.shadows[8],
              border: `1px solid ${theme.palette.divider}`,
            }
          }}
        >
          <MenuItem onClick={handleExpand}>
            <ListItemIcon>
              <FullscreenIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Expandir</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={handleRefresh}>
            <ListItemIcon>
              <RefreshIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Atualizar</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={handleDownload}>
            <ListItemIcon>
              <DownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Baixar PNG</ListItemText>
          </MenuItem>
          
          {onSettings && (
            <MenuItem onClick={handleSettings}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Configurações</ListItemText>
            </MenuItem>
          )}
        </Menu>
      </Box>


    </Card>
    </>
  )
}

export default CustomChart 