# 🚀 Guia de Build e Deploy - SCODRO Mashup

Este guia explica como fazer build e deploy do mashup SCODRO para diferentes ambientes Qlik.

## 📋 Resumo Rápido

- **Durante desenvolvimento**: `npm start` (objetos simulados)
- **Para Desktop/Enterprise**: `npm run build:mashup-enterprise`
- **Para Qlik Cloud**: `npm run build:mashup-cloud`

---

## 🖥️ Desktop/Enterprise Build

### Como gerar:
```bash
npm run build:mashup-enterprise
```

### O que é gerado:
- Pasta: `mashup-enterprise/`
- Arquivo: `scodro-mashup.qext`
- Caminhos relativos configurados
- README com instruções

### Deploy no Desktop:
1. Copie a pasta `mashup-enterprise/` para:
   ```
   C:\Users\<USER>\Documents\Qlik\Sense\Extensions\
   ```
2. Renomeie para `scodro-mashup`
3. Acesse: `http://localhost:4848/extensions/scodro-mashup/`

### Deploy no Enterprise:
1. Importe o arquivo `.qext` no QMC
2. Faça upload dos arquivos da pasta
3. Acesse via: `https://seu-servidor/extensions/scodro-mashup/`

---

## ☁️ Qlik Cloud Build

### Como gerar:
```bash
npm run build:mashup-cloud
```

### O que é gerado:
- Pasta: `mashup-cloud/`
- Configurações para Cloud
- URLs absolutas configuradas
- Arquivo `config.js` template

### Configuração necessária:

#### 1. Web Integration ID:
- Acesse Qlik Cloud Management Console
- Settings > Web integrations
- Crie nova integração e copie o ID

#### 2. Editar configuração:
Abra `mashup-cloud/index.html` e substitua:
```javascript
host: 'SEU_TENANT.qlikcloud.com',        // Seu tenant
webIntegrationId: 'SEU_WEB_INTEGRATION_ID' // ID da etapa 1
```

#### 3. Deploy:
- Hospede a pasta em servidor HTTPS
- Adicione o domínio na whitelist do Web Integration
- Acesse: `https://seu-servidor.com/scodro-mashup/`

---

## 🔧 Desenvolvimento

### Ambiente de desenvolvimento:
```bash
npm start
```

- Objetos Qlik são **simulados**
- Perfeito para desenvolvimento da UI
- Não requer configuração Qlik

### Para testar com Qlik real:
1. Faça build para o ambiente desejado
2. Deploy conforme instruções acima
3. Configure apps e objetos nas Configurações

---

## 📁 Estrutura dos Builds

### Enterprise/Desktop:
```
mashup-enterprise/
├── scodro-mashup.qext    # Arquivo de extensão
├── index.html            # Página principal
├── static/               # Assets do React
├── README.md             # Instruções específicas
└── ...                   # Outros arquivos do build
```

### Cloud:
```
mashup-cloud/
├── index.html            # Página com config Cloud
├── config.js             # Template de configuração
├── static/               # Assets do React
├── README.md             # Instruções específicas
└── ...                   # Outros arquivos do build
```

---

## ⚡ Scripts Disponíveis

```bash
# Desenvolvimento (objetos simulados)
npm start

# Build padrão React
npm run build

# Build para Desktop/Enterprise
npm run build:mashup-enterprise

# Build para Qlik Cloud
npm run build:mashup-cloud
```

---

## 🐛 Resolução de Problemas

### Desktop não carrega:
- Verifique se a pasta está em `Documents/Qlik/Sense/Extensions/`
- Confirme que o Qlik Desktop está rodando
- Verifique se o nome da pasta é `scodro-mashup`

### Cloud não conecta:
- Confirme o Web Integration ID
- Verifique se está em HTTPS
- Confirme que o domínio está na whitelist
- Verifique o tenant na configuração

### Objetos não aparecem:
- Configure apps e objetos nas Configurações do sistema
- Verifique os IDs dos objetos no Qlik
- Confirme que a empresa tem apps cadastrados

---

## 📚 Documentação Qlik

- [Mashup Enterprise/Desktop](https://help.qlik.com/en-US/sense-developer/September2023/Subsystems/Mashups/Content/Sense_Mashups/mashups-getting-started.htm)
- [Mashup Cloud](https://help.qlik.com/en-US/cloud-services/Subsystems/Hub/Content/Sense_Hub/Developer/Mashups/mashups-getting-started.htm)
- [Web Integrations](https://help.qlik.com/en-US/cloud-services/Subsystems/Hub/Content/Sense_Hub/Admin/mc-web-integrations.htm) 