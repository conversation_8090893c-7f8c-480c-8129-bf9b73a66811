/* ===================================
   🎨 SCROLLBARS ELEGANTES E FINAS
   ================================== */

/* Scrollbars para todos os elementos */
* {
  /* Firefox */
  scrollbar-width: thin;
  scrollbar-color: #667eea #f1f1f1;
}

/* WebKit browsers (Chrome, Safari, Edge) */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
  margin: 2px;
}

*::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  border: 2px solid #f1f1f1;
  transition: all 0.3s ease;
}

*::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scale(1.05);
  border: 1px solid #f1f1f1;
}

*::-webkit-scrollbar-thumb:active {
  background: linear-gradient(135deg, #4e61cc 0%, #5e377e 100%);
}

*::-webkit-scrollbar-corner {
  background: #f1f1f1;
  border-radius: 10px;
}

/* ===================================
   🌓 MODO ESCURO (Media Query)
   ================================== */

@media (prefers-color-scheme: dark) {
  * {
    scrollbar-color: #667eea #2c2c2c;
  }

  *::-webkit-scrollbar-track {
    background: #2c2c2c;
  }

  *::-webkit-scrollbar-thumb {
    border: 2px solid #2c2c2c;
  }

  *::-webkit-scrollbar-thumb:hover {
    border: 1px solid #2c2c2c;
  }

  *::-webkit-scrollbar-corner {
    background: #2c2c2c;
  }
}

/* ===================================
   📱 SCROLLBARS ESPECÍFICAS
   ================================== */

/* Scrollbars mais finas para componentes pequenos */
.MuiDataGrid-root *::-webkit-scrollbar,
.MuiTableContainer-root *::-webkit-scrollbar,
.MuiList-root *::-webkit-scrollbar,
.MuiSelect-menu *::-webkit-scrollbar,
.MuiAutocomplete-listbox *::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Scrollbars ainda mais finas para gráficos */
.echarts-for-react *::-webkit-scrollbar,
.custom-chart-container *::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.echarts-for-react *::-webkit-scrollbar-track,
.custom-chart-container *::-webkit-scrollbar-track {
  background: transparent;
}

.echarts-for-react *::-webkit-scrollbar-thumb,
.custom-chart-container *::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.6);
  border: none;
  border-radius: 4px;
}

.echarts-for-react *::-webkit-scrollbar-thumb:hover,
.custom-chart-container *::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.8);
}

/* ===================================
   💫 ANIMAÇÕES SUAVES
   ================================== */

*::-webkit-scrollbar-thumb {
  transition: background 0.3s ease, transform 0.2s ease, border 0.2s ease;
}

/* Scrollbar invisível inicialmente, aparece no hover */
.auto-hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.auto-hide-scrollbar::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

.auto-hide-scrollbar:hover {
  scrollbar-width: thin; /* Firefox */
}

.auto-hide-scrollbar:hover::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* ===================================
   🎯 CASOS ESPECIAIS
   ================================== */

/* Scrollbar horizontal fina para tabelas */
.horizontal-scroll::-webkit-scrollbar:horizontal {
  height: 6px;
}

/* Scrollbar com indicador de posição */
.position-indicator::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, 
    #667eea 0%, 
    #667eea 50%, 
    #764ba2 50%, 
    #764ba2 100%
  );
  background-size: 20px 20px;
} 