import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  Fab,
  CircularProgress,
  Snackbar,
  LinearProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Tabs,
  Tab,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  FormControlLabel,
  Tooltip
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Business as BusinessIcon,
  Apps as AppsIcon,
  ViewModule as ObjectIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CloudOff as CloudOffIcon,
  CheckCircle as CheckCircleIcon,
  Settings as SettingsIcon,
  Cloud as CloudIcon,
  Storage as StorageIcon,
  // Ícones para KPI
  AttachMoney,
  Person,
  ShoppingCart,
  Timeline,
  Assessment,
  Star,
  Favorite,
  Business,
  LocalAtm,
  AccountBalance,
  Analytics,
  // Ícones para páginas
  Dashboard,
  TrendingUp,
  Support,
  Assignment,
  People,
  Inventory,
  Receipt,
  BarChart,
  PieChart
} from '@mui/icons-material'
import { 
  empresaService, 
  appService,
  objetoService,
  paginaService,
  configMashupService,
  convertToBackendFormat, 
  convertToFrontendFormat,
  checkApiStatus,
  TIPOS_OBJETO,
  CATEGORIAS_APP,
  FORMATOS_KPI,
  TIPOS_GRAFICO_ECHARTS
} from '../services/api'
import { useEmpresa } from '../context/EmpresaContext'
import { useSnackbar } from '@components/SnackbarProvider';

// Categorias disponíveis para objetos
const CATEGORIAS_OBJETO = [
  { value: 'dashboard', label: 'Dashboard' },
  { value: 'vendas', label: 'Vendas' },
  { value: 'helpdesk', label: 'Helpdesk' },
  { value: 'financeiro', label: 'Financeiro' },
  { value: 'rh', label: 'RH' },
  { value: 'operacional', label: 'Operacional' },
  { value: 'geral', label: 'Geral (aparece em todas as páginas)' }
]

// Cores disponíveis para KPI
const COLOR_PALETTE = [
  '#1976d2', // Azul
  '#388e3c', // Verde
  '#f57c00', // Laranja
  '#d32f2f', // Vermelho
  '#7b1fa2', // Roxo
  '#00796b', // Teal
  '#5d4037', // Marrom
  '#616161', // Cinza
  '#e91e63', // Rosa
  '#ff5722'  // Deep Orange
]

// Cores para fonte
const FONT_COLORS = [
  '#ffffff', // Branco
  '#000000', // Preto
  '#1976d2', // Azul
  '#388e3c', // Verde
  '#f57c00', // Laranja
  '#d32f2f'  // Vermelho
]

// Ícones disponíveis para KPI
const ICON_GALLERY = {
  money: AttachMoney,
  person: Person,
  cart: ShoppingCart,
  timeline: Timeline,
  assessment: Assessment,
  star: Star,
  heart: Favorite,
  business: Business,
  atm: LocalAtm,
  bank: AccountBalance,
  analytics: Analytics
}

// Ícones disponíveis para páginas
const ICONES_PAGINA = [
  { value: 'dashboard', icon: <Dashboard /> },
  { value: 'trending_up', icon: <TrendingUp /> },
  { value: 'support', icon: <Support /> },
  { value: 'account_balance', icon: <AccountBalance /> },
  { value: 'analytics', icon: <Analytics /> },
  { value: 'assignment', icon: <Assignment /> },
  { value: 'people', icon: <People /> },
  { value: 'inventory', icon: <Inventory /> },
  { value: 'shopping_cart', icon: <ShoppingCart /> },
  { value: 'timeline', icon: <Timeline /> },
  { value: 'assessment', icon: <Assessment /> },
  { value: 'business', icon: <Business /> },
  { value: 'local_atm', icon: <LocalAtm /> },
  { value: 'receipt', icon: <Receipt /> },
  { value: 'bar_chart', icon: <BarChart /> },
  { value: 'pie_chart', icon: <PieChart /> }
]

// 🚀 SOLUÇÃO MUI SIMPLIFICADA: Hook simples para inputs em Dialogs
const useStableInput = (value, onChange) => {
  const [localValue, setLocalValue] = useState(value || '')
  const isUpdatingRef = useRef(false)
  
  // Sincronizar apenas quando valor externo muda (ex: abrir dialog)
  useEffect(() => {
    if (!isUpdatingRef.current) {
      setLocalValue(value || '')
    }
  }, [value])
  
  const handleChange = useCallback((event) => {
    const newValue = event.target.value
    isUpdatingRef.current = true
    setLocalValue(newValue)
    onChange(newValue)
    
    // Reset flag após um tempo
    setTimeout(() => {
      isUpdatingRef.current = false
    }, 100)
  }, [onChange])
  
  return [localValue, handleChange]
}

const Configuracoes = () => {
  const { recarregarEmpresas } = useEmpresa() // ? NOVO: Usar contexto da empresa
  const snackbar = useSnackbar(); // ✅ MOVER: Declarar snackbar no início
  const [tabValue, setTabValue] = useState(0)
  const [empresas, setEmpresas] = useState([])
  const [configMashup, setConfigMashup] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [apiStatus, setApiStatus] = useState({ available: false, message: '' })
  
  // Estados para empresa
  const [dialogEmpresaOpen, setDialogEmpresaOpen] = useState(false)
  const [editingEmpresa, setEditingEmpresa] = useState(null)
  const [empresaForm, setEmpresaForm] = useState({
    id: '',
    nome: '',
    cor: '#667eea',
    logo: null, // ✅ NOVO: Campo para logo
    apps: []
  })

  // 🚀 INPUTS ESTÁVEIS EMPRESA: Sem re-renderização excessiva
  const [empresaIdValue, handleEmpresaIdChange] = useStableInput(
    empresaForm.id, 
    useCallback((value) => setEmpresaForm(prev => ({ ...prev, id: value })), [])
  )
  
  const [empresaNomeValue, handleEmpresaNomeChange] = useStableInput(
    empresaForm.nome, 
    useCallback((value) => setEmpresaForm(prev => ({ ...prev, nome: value })), [])
  )

  // ✅ NOVO: Estados para upload de logo
  const [logoPreview, setLogoPreview] = useState(null)
  const [uploadingLogo, setUploadingLogo] = useState(false)

  // Estados para página
  const [dialogPaginaOpen, setDialogPaginaOpen] = useState(false)
  const [editingPagina, setEditingPagina] = useState(null)
  const [currentEmpresaIdPagina, setCurrentEmpresaIdPagina] = useState(null)
  const [paginaForm, setPaginaForm] = useState({
    chave: '',
    titulo: '',
    descricao: '',
    icone: 'dashboard',
    rota: '/',
    ordem: 1,
    ativo: true,
    configuracao: {
      showInSidebar: true
    }
  })

  // 🚀 INPUTS ESTÁVEIS PÁGINA: Sem re-renderização excessiva
  const [paginaChaveValue, handlePaginaChaveChange] = useStableInput(
    paginaForm.chave, 
    useCallback((value) => setPaginaForm(prev => ({ ...prev, chave: value })), [])
  )
  
  const [paginaTituloValue, handlePaginaTituloChange] = useStableInput(
    paginaForm.titulo, 
    useCallback((value) => setPaginaForm(prev => ({ ...prev, titulo: value })), [])
  )
  
  const [paginaDescricaoValue, handlePaginaDescricaoChange] = useStableInput(
    paginaForm.descricao, 
    useCallback((value) => setPaginaForm(prev => ({ ...prev, descricao: value })), [])
  )

  const [paginaRotaValue, handlePaginaRotaChange] = useStableInput(
    paginaForm.rota, 
    useCallback((value) => setPaginaForm(prev => ({ ...prev, rota: value })), [])
  )

  // Estados para app
  const [dialogAppOpen, setDialogAppOpen] = useState(false)
  const [editingApp, setEditingApp] = useState(null)
  const [currentEmpresaId, setCurrentEmpresaId] = useState(null)
  const [appForm, setAppForm] = useState({
    appId: '',
    nome: '',
    categoria: 'dashboard',
    descricao: '',
    objetos: []
  })

  // 🚀 INPUTS ESTÁVEIS APP: Sem re-renderização excessiva
  const [appIdValue, handleAppIdChange] = useStableInput(
    appForm.appId, 
    useCallback((value) => setAppForm(prev => ({ ...prev, appId: value })), [])
  )
  
  const [appNomeValue, handleAppNomeChange] = useStableInput(
    appForm.nome, 
    useCallback((value) => setAppForm(prev => ({ ...prev, nome: value })), [])
  )
  
  const [appDescricaoValue, handleAppDescricaoChange] = useStableInput(
    appForm.descricao, 
    useCallback((value) => setAppForm(prev => ({ ...prev, descricao: value })), [])
  )

  // Estados para objeto
  const [dialogObjetoOpen, setDialogObjetoOpen] = useState(false)
  const [editingObjeto, setEditingObjeto] = useState(null)
  const [currentAppId, setCurrentAppId] = useState(null)
  const [objetoForm, setObjetoForm] = useState({
    chave: '',
    objectId: '',
    nome: '',
    tipo: 'kpi',
    categoria: 'geral',
    descricao: '',
    configuracao: {
      kpiConfig: {
        formato: 'numero',
        casasDecimais: 0,
        prefix: '',
        suffix: '',
        showTrend: false,
        color: '#667eea',
        showTitle: true,
        cor: '#1976d2',
        corFonte: '#ffffff',
        icone: 'analytics',
        showIcon: true,
        gradiente: false
      },
      multiKpiConfig: {
        itemsPerRow: 3,
        spacing: 2,
        showTitle: true,
        showIcon: false,
        layout: 'unified',
        elevation: 1,
        cor: '#1976d2',
        corFonte: '#ffffff',
        icone: 'analytics'
      },
      tableConfig: {
        showTitle: true,
        showSearch: true,
        showPagination: true,
        showExport: false,
        showFilters: true,
        showRowNumbers: false,
        alternateRowColors: true,
        compactView: false,
        stickyHeader: true,
        maxHeight: 600,
        borderRadius: 1,
        elevation: 1,
        headerBackgroundColor: '#1976d2',
        headerTextColor: '#ffffff',
        enableSorting: true,
        defaultRowsPerPage: 5,
        rowsPerPageOptions: [5, 10, 25, 50]
      },
      chartConfig: {
        tipoGrafico: 'bar',
        showLegend: true,
        showDataLabels: false,
        showTitle: true,
        animacao: true,
        color: ['#667eea', '#764ba2'],
        // ✅ NOVO: Configurações de DataZoom
        dataZoom: {
          enabled: false,
          type: 'slider',
          start: 0,
          end: 100,
          showDetail: true,
          realtime: true
        },
        // ✅ NOVO: Configurações para Gráfico Misto
        mixedConfig: {
          barSeries: [0],
          lineSeries: [1]
        }
      }
    }
  })

  // 🚀 INPUTS ESTÁVEIS OBJETO: Sem re-renderização excessiva
  const [chaveValue, handleChaveChange] = useStableInput(
    objetoForm.chave, 
    useCallback((value) => setObjetoForm(prev => ({ ...prev, chave: value })), [])
  )
  
  const [objectIdValue, handleObjectIdChange] = useStableInput(
    objetoForm.objectId, 
    useCallback((value) => setObjetoForm(prev => ({ ...prev, objectId: value })), [])
  )
  
  const [nomeValue, handleNomeChange] = useStableInput(
    objetoForm.nome, 
    useCallback((value) => setObjetoForm(prev => ({ ...prev, nome: value })), [])
  )
  
  const [descricaoValue, handleDescricaoChange] = useStableInput(
    objetoForm.descricao, 
    useCallback((value) => setObjetoForm(prev => ({ ...prev, descricao: value })), [])
  )
  
  const [prefixValue, handlePrefixChange] = useStableInput(
    objetoForm.configuracao?.kpiConfig?.prefix, 
    useCallback((value) => setObjetoForm(prev => ({
      ...prev,
      configuracao: {
        ...prev.configuracao,
        kpiConfig: { ...prev.configuracao.kpiConfig, prefix: value }
      }
    })), [])
  )
  
  const [suffixValue, handleSuffixChange] = useStableInput(
    objetoForm.configuracao?.kpiConfig?.suffix, 
    useCallback((value) => setObjetoForm(prev => ({
      ...prev,
      configuracao: {
        ...prev.configuracao,
        kpiConfig: { ...prev.configuracao.kpiConfig, suffix: value }
      }
    })), [])
  )

  // Estados para configuração do mashup
  const [configForm, setConfigForm] = useState({
    nome: 'Configuração Principal',
    qlikCloud: {
      tenantUrl: '',
      webIntegrationId: ''
    },
    qlikEnterprise: {
      serverUrl: '',
      prefix: '',
      virtualProxy: ''
    },
    configuracoes: {
      ambiente: 'cloud',
      tema: 'light',
      idioma: 'pt-BR',
      debug: false
    }
  })

  // Configurações padrão memoizadas para evitar recriação constante
  const defaultObjetoConfig = useMemo(() => ({
    kpiConfig: {
      formato: 'numero',
      casasDecimais: 0,
      prefix: '',
      suffix: '',
      showTrend: false,
      color: '#667eea',
      showTitle: true,
      cor: '#1976d2',
      corFonte: '#ffffff',
      icone: 'analytics',
      showIcon: true,
      gradiente: false
    },
    multiKpiConfig: {
      itemsPerRow: 3,
      spacing: 2,
      showTitle: true,
      showIcon: false,
      layout: 'unified',
      elevation: 1,
      cor: '#1976d2',
      corFonte: '#ffffff',
      icone: 'analytics'
    },
    tableConfig: {
      showTitle: true,
      showSearch: true,
      showPagination: true,
      showExport: false,
      showFilters: true,
      showRowNumbers: false,
      alternateRowColors: true,
      compactView: false,
      stickyHeader: true,
      maxHeight: 600,
      borderRadius: 1,
      elevation: 1,
      headerBackgroundColor: '#1976d2',
      headerTextColor: '#ffffff',
      enableSorting: true,
      defaultRowsPerPage: 5,
      rowsPerPageOptions: [5, 10, 25, 50]
    },
    chartConfig: {
      tipoGrafico: 'bar',
      showLegend: true,
      showDataLabels: false,
      showTitle: true,
      animacao: true,
      color: ['#667eea', '#764ba2'],
      // ✅ NOVO: Incluir dataZoom e mixedConfig por padrão
      dataZoom: {
        enabled: false,
        type: 'slider',
        start: 0,
        end: 100,
        showDetail: true,
        realtime: true
      },
      mixedConfig: {
        barSeries: [0],
        lineSeries: [1],
        // ✅ NOVO: Incluir configurações de porcentagem
        usePercentageScale: false,
        percentageAxisMin: 0,
        percentageAxisMax: 100,
        autoDetectPercentage: true,
        percentageAxisName: 'Porcentagem (%)',
        valueAxisName: 'Valores'
      }
    }
  }), [])

  // Estados locais para inputs mixed (para permitir digitação livre)
  const [barSeriesInput, setBarSeriesInput] = useState('')
  const [lineSeriesInput, setLineSeriesInput] = useState('')

  // ✅ CORRIGIDO: Sincronizar estados locais apenas quando o dialog abre, não durante edição
  useEffect(() => {
    // Só sincronizar quando o dialog for aberto, não durante a edição
    if (dialogObjetoOpen) {
      if (objetoForm.configuracao?.chartConfig?.mixedConfig?.barSeries) {
        const currentBarValue = objetoForm.configuracao.chartConfig.mixedConfig.barSeries.join(',')
        setBarSeriesInput(currentBarValue)
      } else {
        setBarSeriesInput('0') // Valor padrão
      }
      
      if (objetoForm.configuracao?.chartConfig?.mixedConfig?.lineSeries) {
        const currentLineValue = objetoForm.configuracao.chartConfig.mixedConfig.lineSeries.join(',')
        setLineSeriesInput(currentLineValue)
      } else {
        setLineSeriesInput('1') // Valor padrão
      }
    }
  }, [dialogObjetoOpen]) // ✅ REMOVIDO: objetoForm.configuracao?.chartConfig?.mixedConfig da dependência

  // Verificar status da API e carregar dados
  useEffect(() => {
    const initializeData = async () => {
      try {
        const status = await checkApiStatus()
        setApiStatus(status)

        if (status.available) {
          await Promise.all([
            carregarEmpresas(),
            carregarConfigMashup()
          ])
        } else {
          snackbar.showSnackbar('Backend não está disponível. Verifique se o servidor está rodando.', 'warning')
        }
      } catch (error) {
        setApiStatus({ available: false, message: 'Erro ao conectar com a API' })
        snackbar.showSnackbar('Erro ao conectar com o backend', 'error')
      } finally {
        setLoading(false)
      }
    }

    initializeData()
  }, [])

  const carregarEmpresas = useCallback(async () => {
    try {
      setLoading(true)
      const response = await empresaService.listarEmpresas(true)
      
      if (response.success) {
        // Carregar apps para cada empresa
        const empresasComApps = await Promise.all(
          response.data.map(async (empresa) => {
            try {
              const appsResponse = await appService.listarAppsPorEmpresa(empresa.id, true)
              return {
                ...empresa,
                apps: appsResponse.success ? appsResponse.data : []
              }
            } catch (error) {
              return { ...empresa, apps: [] }
            }
          })
        )
        
        setEmpresas(empresasComApps)
        //snackbar.showSnackbar(`${empresasComApps.length} empresas carregadas`, 'success')
      } else {
        throw new Error(response.error || 'Erro ao carregar empresas')
      }
    } catch (error) {
      snackbar.showSnackbar(`Erro ao carregar empresas: ${error.message}`, 'error')
      setEmpresas([])
    } finally {
      setLoading(false)
    }
  }, [snackbar])

  const carregarConfigMashup = useCallback(async () => {
    try {
      const response = await configMashupService.obterConfigAtiva()
      if (response.success) {
        setConfigMashup(response.data)
        setConfigForm(response.data)
      }
    } catch (error) {
      // Criar configuração padrão se não existir
      setConfigForm({
        nome: 'Configuração Principal',
        qlikCloud: { tenantUrl: '', webIntegrationId: '' },
        qlikEnterprise: { serverUrl: '', prefix: '', virtualProxy: '' },
        configuracoes: { ambiente: 'cloud', tema: 'light', idioma: 'pt-BR', debug: false }
      })
    }
  }, [snackbar])

  // Funções para Empresa - OTIMIZADAS
  const handleOpenEmpresaDialog = useCallback((empresa = null) => {
    if (empresa) {
      setEditingEmpresa(empresa.id)
      setEmpresaForm(empresa)
      // ✅ NOVO: Configurar preview da logo se existir
      setLogoPreview(empresa.logo || null)
    } else {
      setEditingEmpresa(null)
      setEmpresaForm({
        id: '',
        nome: '',
        cor: '#667eea',
        logo: null, // ✅ NOVO: Inicializar logo como null
        apps: []
      })
      // ✅ NOVO: Limpar preview
      setLogoPreview(null)
    }
    setDialogEmpresaOpen(true)
  }, [snackbar])

  const handleSaveEmpresa = useCallback(async () => {
    if (!apiStatus.available) {
      snackbar.showSnackbar('Backend não disponível', 'error')
      return
    }

    try {
      setSaving(true)

      if (!empresaForm.id || !empresaForm.nome) {
        snackbar.showSnackbar('ID e Nome são obrigatórios', 'error')
        return
      }

      // ✅ NOVO: Debug - verificar dados antes de enviar
      console.log('🔍 DEBUG - Dados da empresa antes de enviar:', {
        id: empresaForm.id,
        nome: empresaForm.nome,
        cor: empresaForm.cor,
        logo: empresaForm.logo ? `${empresaForm.logo.substring(0, 50)}...` : 'null',
        logoLength: empresaForm.logo ? empresaForm.logo.length : 0
      })

      const empresaBackend = convertToBackendFormat(empresaForm)
      
      // ✅ NOVO: Debug - verificar dados após conversão
      console.log('🔍 DEBUG - Dados após conversão para backend:', {
        ...empresaBackend,
        logo: empresaBackend.logo ? `${empresaBackend.logo.substring(0, 50)}...` : 'null'
      })

      let response
      if (editingEmpresa) {
        response = await empresaService.atualizarEmpresa(editingEmpresa, empresaBackend)
        snackbar.showSnackbar('Empresa atualizada com sucesso', 'success')
      } else {
        response = await empresaService.criarEmpresa(empresaBackend)
        snackbar.showSnackbar('Empresa criada com sucesso', 'success')
      }

      // ✅ NOVO: Debug - verificar resposta do backend
      //console.log('🔍 DEBUG - Resposta do backend:', response)

      if (response.success) {
        await carregarEmpresas()
        await recarregarEmpresas() // ? NOVO: Recarregar contexto da empresa
        setDialogEmpresaOpen(false)
      }
    } catch (error) {
      // console.error('Erro ao salvar empresa:', error) - REMOVIDO LOG
      //console.error('❌ DEBUG - Erro ao salvar empresa:', error)
      snackbar.showSnackbar(`Erro ao salvar empresa: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }, [apiStatus.available, empresaForm, editingEmpresa, carregarEmpresas, snackbar])

  // Adicionar no início dos estados do componente Configuracoes:
  const [confirmDialog, setConfirmDialog] = useState({ open: false, type: '', item: null, extra: null })

  // Função para abrir o dialog de confirmação
  const openConfirmDialog = (type, item, extra = null) => {
    setConfirmDialog({ open: true, type, item, extra })
  }

  // Função para fechar o dialog de confirmação
  const closeConfirmDialog = () => {
    setConfirmDialog({ open: false, type: '', item: null, extra: null })
  }

  // Função para executar a exclusão após confirmação
  const handleConfirmDelete = async () => {
    if (!apiStatus.available) {
      snackbar.showSnackbar('Backend não disponível', 'error')
      closeConfirmDialog()
      return
    }
    try {
      setSaving(true)
      if (confirmDialog.type === 'empresa') {
        await empresaService.excluirEmpresa(confirmDialog.item, true)
        snackbar.showSnackbar('Empresa excluída com sucesso', 'success')
        await carregarEmpresas()
        await recarregarEmpresas()
      } else if (confirmDialog.type === 'app') {
        await appService.excluirApp(confirmDialog.item.empresaId, confirmDialog.item.appId, true)
        snackbar.showSnackbar('App excluído com sucesso', 'success')
        await carregarEmpresas()
        await recarregarEmpresas()
      } else if (confirmDialog.type === 'objeto') {
        await objetoService.excluirObjeto(confirmDialog.item.empresaId, confirmDialog.item.appId, confirmDialog.item.objetoId, true)
        snackbar.showSnackbar('Objeto excluído com sucesso', 'success')
        await carregarEmpresas()
        await recarregarEmpresas()
      } else if (confirmDialog.type === 'pagina') {
        await paginaService.excluirPagina(confirmDialog.item.empresaId, confirmDialog.item.paginaId, true)
        snackbar.showSnackbar('Página excluída com sucesso', 'success')
        await carregarEmpresas()
        await recarregarEmpresas()
      }
    } catch (error) {
      snackbar.showSnackbar(`Erro ao excluir: ${error.message}`, 'error')
    } finally {
      setSaving(false)
      closeConfirmDialog()
    }
  }

  // Substituir window.confirm nos handlers de exclusão:
  const handleDeleteEmpresa = useCallback(async (empresaId) => {
    openConfirmDialog('empresa', empresaId)
  }, [snackbar])

  const handleDeleteApp = useCallback(async (empresaId, appId) => {
    openConfirmDialog('app', { empresaId, appId })
  }, [snackbar])

  const handleDeleteObjeto = useCallback(async (empresaId, appId, objetoId) => {
    openConfirmDialog('objeto', { empresaId, appId, objetoId })
  }, [snackbar])

  const handleDeletePagina = useCallback(async (empresaId, paginaId) => {
    openConfirmDialog('pagina', { empresaId, paginaId })
  }, [snackbar])

  // Funções para App - OTIMIZADAS
  const handleOpenAppDialog = useCallback((empresaId, app = null) => {
    setCurrentEmpresaId(empresaId)
    if (app) {
      setEditingApp(app._id)
      setAppForm(app)
    } else {
      setEditingApp(null)
      setAppForm({
        appId: '',
        nome: '',
        categoria: 'dashboard',
        descricao: '',
        objetos: []
      })
    }
    setDialogAppOpen(true)
  }, [snackbar])

  const handleSaveApp = useCallback(async () => {
    if (!apiStatus.available || !currentEmpresaId) {
      snackbar.showSnackbar('Backend não disponível', 'error')
      return
    }

    try {
      setSaving(true)

      if (!appForm.appId || !appForm.nome) {
        snackbar.showSnackbar('App ID e Nome são obrigatórios', 'error')
        return
      }

      let response
      if (editingApp) {
        response = await appService.atualizarApp(currentEmpresaId, editingApp, appForm)
        snackbar.showSnackbar('App atualizado com sucesso', 'success')
      } else {
        response = await appService.criarApp(currentEmpresaId, appForm)
        snackbar.showSnackbar('App criado com sucesso', 'success')
      }

      if (response.success) {
        await carregarEmpresas()
        await recarregarEmpresas() // ? NOVO: Recarregar contexto da empresa
        setDialogAppOpen(false)
      }
    } catch (error) {
      // console.error('Erro ao salvar app:', error) - REMOVIDO LOG
      snackbar.showSnackbar(`Erro ao salvar app: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }, [apiStatus.available, currentEmpresaId, appForm, editingApp, carregarEmpresas, snackbar])

  // Funções para Objeto - OTIMIZADAS
  const handleOpenObjetoDialog = useCallback((empresaId, appId, objeto = null) => {
    setCurrentEmpresaId(empresaId)
    setCurrentAppId(appId)
    if (objeto) {
      setEditingObjeto(objeto._id)
      setObjetoForm({
        ...objeto,
        configuracao: objeto.configuracao || defaultObjetoConfig
      })
    } else {
      setEditingObjeto(null)
      setObjetoForm({
        chave: '',
        objectId: '',
        nome: '',
        tipo: 'kpi',
        categoria: 'geral',
        descricao: '',
        configuracao: {
          ...defaultObjetoConfig,
          // ✅ NOVO: Garantir que dataZoom e mixedConfig sejam sempre inicializados
          chartConfig: {
            ...defaultObjetoConfig.chartConfig,
            dataZoom: {
              enabled: false,
              type: 'slider',
              start: 0,
              end: 100,
              showDetail: true,
              realtime: true
            },
            mixedConfig: {
              barSeries: [0],
              lineSeries: [1],
              // ✅ NOVO: Incluir configurações de porcentagem
              usePercentageScale: false,
              percentageAxisMin: 0,
              percentageAxisMax: 100,
              autoDetectPercentage: true,
              percentageAxisName: 'Porcentagem (%)',
              valueAxisName: 'Valores'
            }
          },
          // ✅ NOVO: Garantir que multiKpiConfig tenha o campo icone
          multiKpiConfig: {
            ...defaultObjetoConfig.multiKpiConfig,
            icone: 'analytics'
          }
        }
      })
    }
    setDialogObjetoOpen(true)
  }, [defaultObjetoConfig, snackbar])

  const handleSaveObjeto = useCallback(async () => {
    if (!apiStatus.available || !currentEmpresaId || !currentAppId) {
      snackbar.showSnackbar('Backend não disponível', 'error')
      return
    }

    try {
      setSaving(true)

      if (!objetoForm.chave || !objetoForm.objectId || !objetoForm.nome) {
        snackbar.showSnackbar('Chave, Object ID e Nome são obrigatórios', 'error')
        return
      }

      let response
      if (editingObjeto) {
        response = await objetoService.atualizarObjeto(currentEmpresaId, currentAppId, editingObjeto, objetoForm)
        snackbar.showSnackbar('Objeto atualizado com sucesso', 'success')
      } else {
        response = await objetoService.criarObjeto(currentEmpresaId, currentAppId, objetoForm)
        snackbar.showSnackbar('Objeto criado com sucesso', 'success')
      }

      if (response.success) {
        await carregarEmpresas()
        await recarregarEmpresas() // ? NOVO: Recarregar contexto da empresa
        setDialogObjetoOpen(false)
      }
    } catch (error) {
      // console.error('Erro ao salvar objeto:', error) - REMOVIDO LOG
      snackbar.showSnackbar(`Erro ao salvar objeto: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }, [apiStatus.available, currentEmpresaId, currentAppId, objetoForm, editingObjeto, carregarEmpresas, snackbar])

  // Funções para Páginas - OTIMIZADAS
  const handleOpenPaginaDialog = useCallback((empresaId, pagina = null) => {
    setCurrentEmpresaIdPagina(empresaId)
    setEditingPagina(pagina)
    
    if (pagina) {
      setPaginaForm({
        chave: pagina.chave || '',
        titulo: pagina.titulo || '',
        descricao: pagina.descricao || '',
        icone: pagina.icone || 'dashboard',
        rota: pagina.rota || '/',
        ordem: pagina.ordem || 1,
        ativo: pagina.ativo !== undefined ? pagina.ativo : true,
        configuracao: {
          showInSidebar: pagina.configuracao?.showInSidebar !== undefined ? pagina.configuracao.showInSidebar : true
        }
      })
    } else {
      // Obter próxima ordem
      const empresa = empresas.find(e => (e.id || e._id) === empresaId)
      const proximaOrdem = empresa?.paginas ? Math.max(...empresa.paginas.map(p => p.ordem || 1)) + 1 : 1
      
      setPaginaForm({
        chave: '',
        titulo: '',
        descricao: '',
        icone: 'dashboard',
        rota: '/',
        ordem: proximaOrdem,
        ativo: true,
        configuracao: {
          showInSidebar: true
        }
      })
    }
    
    setDialogPaginaOpen(true)
  }, [empresas, snackbar])

  const handleSavePagina = useCallback(async () => {
    if (!apiStatus.available) {
      snackbar.showSnackbar('Backend não disponível', 'error')
      return
    }

    // Validações básicas
    if (!paginaForm.chave || !paginaForm.titulo) {
      snackbar.showSnackbar('Chave e título são obrigatórios', 'error')
      return
    }

    try {
      setSaving(true)
      let response

      if (editingPagina) {
        // Atualizar página existente
        response = await paginaService.atualizarPagina(
          currentEmpresaIdPagina,
          editingPagina._id,
          paginaForm
        )
      } else {
        // Criar nova página
        response = await paginaService.criarPagina(currentEmpresaIdPagina, paginaForm)
      }

      if (response.success) {
        snackbar.showSnackbar(
          editingPagina ? 'Página atualizada com sucesso' : 'Página criada com sucesso',
          'success'
        )
        await carregarEmpresas()
        await recarregarEmpresas() // ? NOVO: Recarregar contexto da empresa
        setDialogPaginaOpen(false)
      }
    } catch (error) {
      snackbar.showSnackbar(`Erro ao salvar página: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }, [apiStatus.available, currentEmpresaIdPagina, paginaForm, editingPagina, carregarEmpresas, snackbar])

  // Funções para Configuração do Mashup - OTIMIZADAS
  const handleSaveConfigMashup = useCallback(async () => {
    if (!apiStatus.available) {
      snackbar.showSnackbar('Backend não disponível', 'error')
      return
    }

    try {
      setSaving(true)
      const response = await configMashupService.atualizarConfig(configForm)
      
      if (response.success) {
        setConfigMashup(response.data)
        snackbar.showSnackbar('Configuração do mashup salva com sucesso', 'success')
      }
    } catch (error) {
      // console.error('Erro ao salvar configuração:', error) - REMOVIDO LOG
      snackbar.showSnackbar(`Erro ao salvar configuração: ${error.message}`, 'error')
    } finally {
      setSaving(false)
    }
  }, [apiStatus.available, configForm, snackbar])

  const exportConfig = useCallback(() => {
    try {
      const config = {
        empresas,
        configMashup,
        exportedAt: new Date().toISOString(),
        version: '1.0'
      }
      
      const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'configuracao-mashup.json'
      a.click()
      URL.revokeObjectURL(url)
      
      snackbar.showSnackbar('Configuração exportada com sucesso', 'success')
    } catch (error) {
      snackbar.showSnackbar('Erro ao exportar configuração', 'error')
    }
  }, [empresas, configMashup, snackbar])

  // Handlers otimizados para formulários
  const handleEmpresaFormChange = useCallback((field, value) => {
    setEmpresaForm(prev => ({ ...prev, [field]: value }))
  }, [])

  const handleAppFormChange = useCallback((field, value) => {
    setAppForm(prev => ({ ...prev, [field]: value }))
  }, [])

  const handleObjetoFormChange = useCallback((field, value) => {
    setObjetoForm(prev => {
      if (field.startsWith('configuracao.')) {
        const configPath = field.replace('configuracao.', '')
        const keys = configPath.split('.')
        
        if (keys.length === 2) {
          // Ex: kpiConfig.formato, chartConfig.tipoGrafico
          return {
            ...prev,
            configuracao: {
              ...prev.configuracao,
              [keys[0]]: {
                ...prev.configuracao[keys[0]],
                [keys[1]]: value
              }
            }
          }
        } else if (keys.length === 3) {
          // ✅ NOVO: Para 3 níveis - Ex: chartConfig.dataZoom.enabled, chartConfig.mixedConfig.barSeries
          return {
            ...prev,
            configuracao: {
              ...prev.configuracao,
              [keys[0]]: {
                ...prev.configuracao[keys[0]],
                [keys[1]]: {
                  ...prev.configuracao[keys[0]]?.[keys[1]],
                  [keys[2]]: value
                }
              }
            }
          }
        }
      }
      
      // ✅ NOVO: Quando o tipo principal é alterado, sincronizar com chartConfig.tipoGrafico
      if (field === 'tipo') {
        const isChartType = ['bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed', 'gauge'].includes(value)
        
        if (isChartType) {
          console.log(`🔄 SYNC: Atualizando tipo principal "${value}" e chartConfig.tipoGrafico`)
          
          return {
            ...prev,
            [field]: value,
            configuracao: {
              ...prev.configuracao,
              chartConfig: {
                ...prev.configuracao.chartConfig,
                tipoGrafico: value // ✅ SINCRONIZAR: chartConfig.tipoGrafico = tipo principal
              }
            }
          }
        }
      }
      
      return { ...prev, [field]: value }
    })
  }, [])

  const handlePaginaFormChange = useCallback((field, value) => {
    setPaginaForm(prev => {
      if (field.startsWith('configuracao.')) {
        const configPath = field.replace('configuracao.', '')
        return {
          ...prev,
          configuracao: {
            ...prev.configuracao,
            [configPath]: value
          }
        }
      }
      return { ...prev, [field]: value }
    })
  }, [])

  const handleConfigFormChange = useCallback((path, value) => {
    setConfigForm(prev => {
      const keys = path.split('.')
      if (keys.length === 1) {
        return { ...prev, [keys[0]]: value }
      } else if (keys.length === 2) {
        return {
          ...prev,
          [keys[0]]: { ...prev[keys[0]], [keys[1]]: value }
        }
      }
      return prev
    })
  }, [])

  // ✅ NOVO: Função para converter arquivo para base64
  const convertToBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result)
      reader.onerror = error => reject(error)
    })
  }

  // ✅ NOVO: Função para lidar com upload de logo
  const handleLogoUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    // Validar tipo de arquivo
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/svg+xml']
    if (!allowedTypes.includes(file.type)) {
      snackbar.showSnackbar('Tipo de arquivo não suportado. Use PNG, JPG, GIF ou SVG.', 'error')
      return
    }

    // Validar tamanho (máximo 2MB)
    const maxSize = 2 * 1024 * 1024 // 2MB
    if (file.size > maxSize) {
      snackbar.showSnackbar('Arquivo muito grande. Máximo 2MB.', 'error')
      return
    }

    try {
      setUploadingLogo(true)
      const base64 = await convertToBase64(file)
      
      setEmpresaForm(prev => ({ ...prev, logo: base64 }))
      setLogoPreview(base64)
      snackbar.showSnackbar('Logo carregada com sucesso!', 'success')
    } catch (error) {
      snackbar.showSnackbar('Erro ao carregar logo', 'error')
    } finally {
      setUploadingLogo(false)
    }
  }

  // ✅ NOVO: Função para remover logo
  const handleRemoveLogo = () => {
    setEmpresaForm(prev => ({ ...prev, logo: null }))
    setLogoPreview(null)
    snackbar.showSnackbar('Logo removida', 'info')
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Carregando configurações...</Typography>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
            ⚙️ Configurações
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Gerencie empresas, aplicativos e objetos do Qlik Sense
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
            disabled={!apiStatus.available}
          >
            Atualizar
          </Button>
          <Button
            variant="outlined"
            onClick={exportConfig}
            startIcon={<SaveIcon />}
            disabled={empresas.length === 0}
          >
            Exportar Configuração
          </Button>
        </Box>
      </Box>

      {/* Status da API */}
      <Alert 
        severity={apiStatus.available ? 'success' : 'error'} 
        sx={{ mb: 3 }}
        icon={apiStatus.available ? <CheckCircleIcon /> : <CloudOffIcon />}
      >
        <Typography variant="body2">
          <strong>Status do Backend:</strong> {apiStatus.message}
          {!apiStatus.available && (
            <span> - Verifique se o servidor está rodando na porta 3031</span>
          )}
        </Typography>
      </Alert>

      {/* Loading indicator para operações */}
      {saving && <LinearProgress sx={{ mb: 2 }} />}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs 
          value={tabValue} 
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="fullWidth"
        >
          <Tab 
            label="Empresas & Apps" 
            icon={<BusinessIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Páginas" 
            icon={<AppsIcon />} 
            iconPosition="start"
          />
          <Tab 
            label="Configurações Qlik" 
            icon={<SettingsIcon />} 
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {tabValue === 0 && (
        <Box>
          {/* Lista de Empresas */}
          {empresas.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 6 }}>
                <BusinessIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  Nenhuma empresa cadastrada
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {apiStatus.available 
                    ? 'Clique no botão + para adicionar sua primeira empresa'
                    : 'Inicie o backend para gerenciar empresas'
                  }
                </Typography>
                {apiStatus.available && (
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => handleOpenEmpresaDialog()}
                  >
                    Adicionar Primeira Empresa
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {empresas.map((empresa) => (
                <Grid item xs={12} key={empresa.id}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <BusinessIcon sx={{ color: empresa.cor, mr: 2 }} />
                        <Typography variant="h6" sx={{ flexGrow: 1 }}>
                          {empresa.nome}
                        </Typography>
                        <Chip 
                          label={empresa.id} 
                          size="small" 
                          variant="outlined" 
                          sx={{ mr: 1 }}
                        />
                        {empresa.ativo === false && (
                          <Chip 
                            label="Inativo" 
                            size="small" 
                            color="error" 
                            sx={{ mr: 1 }}
                          />
                        )}
                        <IconButton 
                          onClick={() => handleOpenEmpresaDialog(empresa)}
                          color="primary"
                          disabled={!apiStatus.available}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton 
                          onClick={() => handleDeleteEmpresa(empresa.id)}
                          color="error"
                          disabled={!apiStatus.available}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>

                      {/* Apps */}
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <AppsIcon sx={{ mr: 1 }} />
                          <Typography>Apps ({empresa.apps?.length || 0})</Typography>
                          <Button
                            size="small"
                            startIcon={<AddIcon />}
                            onClick={(e) => {
                              e.stopPropagation()
                              handleOpenAppDialog(empresa.id)
                            }}
                            sx={{ ml: 'auto', mr: 2 }}
                            disabled={!apiStatus.available}
                          >
                            Adicionar App
                          </Button>
                        </AccordionSummary>
                        <AccordionDetails>
                          {empresa.apps?.length === 0 ? (
                            <Typography color="text.secondary" sx={{ textAlign: 'center', py: 2 }}>
                              Nenhum app cadastrado
                            </Typography>
                          ) : (
                            <Grid container spacing={2}>
                              {empresa.apps?.map((app) => (
                                <Grid item xs={12} key={app._id}>
                                  <Card variant="outlined">
                                    <CardContent>
                                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                        <AppsIcon sx={{ mr: 1, color: 'primary.main' }} />
                                        <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                                          {app.nome}
                                        </Typography>
                                        <Chip 
                                          label={app.categoria} 
                                          size="small" 
                                          sx={{ mr: 1 }}
                                        />
                                        <IconButton 
                                          size="small"
                                          onClick={() => handleOpenAppDialog(empresa.id, app)}
                                          disabled={!apiStatus.available}
                                        >
                                          <EditIcon fontSize="small" />
                                        </IconButton>
                                        <IconButton 
                                          size="small"
                                          onClick={() => handleDeleteApp(empresa.id, app._id)}
                                          color="error"
                                          disabled={!apiStatus.available}
                                        >
                                          <DeleteIcon fontSize="small" />
                                        </IconButton>
                                      </Box>
                                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        App ID: {app.appId}
                                      </Typography>
                                      
                                      {/* Objetos do App */}
                                      <Accordion size="small">
                                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                          <ObjectIcon sx={{ mr: 1 }} />
                                          <Typography variant="body2">
                                            Objetos ({app.objetos?.length || 0})
                                          </Typography>
                                          <Button
                                            size="small"
                                            startIcon={<AddIcon />}
                                            onClick={(e) => {
                                              e.stopPropagation()
                                              handleOpenObjetoDialog(empresa.id, app._id)
                                            }}
                                            sx={{ ml: 'auto', mr: 2 }}
                                            disabled={!apiStatus.available}
                                          >
                                            Adicionar Objeto
                                          </Button>
                                        </AccordionSummary>
                                        <AccordionDetails>
                                          {app.objetos?.length === 0 ? (
                                            <Typography color="text.secondary" sx={{ textAlign: 'center', py: 1 }}>
                                              Nenhum objeto cadastrado
                                            </Typography>
                                          ) : (
                                            <List dense>
                                              {app.objetos?.map((objeto) => (
                                                <ListItem key={objeto._id}>
                                                  <ListItemText
                                                    primary={objeto.nome}
                                                    secondary={`${objeto.chave} - ${objeto.tipo} - ${objeto.objectId}`}
                                                  />
                                                  <ListItemSecondaryAction>
                                                    <IconButton 
                                                      size="small"
                                                      onClick={() => handleOpenObjetoDialog(empresa.id, app._id, objeto)}
                                                      disabled={!apiStatus.available}
                                                    >
                                                      <EditIcon fontSize="small" />
                                                    </IconButton>
                                                    <IconButton 
                                                      size="small"
                                                      onClick={() => handleDeleteObjeto(empresa.id, app._id, objeto._id)}
                                                      color="error"
                                                      disabled={!apiStatus.available}
                                                    >
                                                      <DeleteIcon fontSize="small" />
                                                    </IconButton>
                                                  </ListItemSecondaryAction>
                                                </ListItem>
                                              ))}
                                            </List>
                                          )}
                                        </AccordionDetails>
                                      </Accordion>
                                    </CardContent>
                                  </Card>
                                </Grid>
                              ))}
                            </Grid>
                          )}
                        </AccordionDetails>
                      </Accordion>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}

          {/* FAB para adicionar empresa */}
          {apiStatus.available && (
            <Fab
              color="primary"
              aria-label="adicionar empresa"
              sx={{ position: 'fixed', bottom: -16, right: -16 }}
              onClick={() => handleOpenEmpresaDialog()}
            >
              <AddIcon />
            </Fab>
          )}
        </Box>
      )}

      {/* Tab Content - Páginas */}
      {tabValue === 1 && (
        <Box>
          {/* Lista de Empresas com suas Páginas */}
          {empresas.length === 0 ? (
            <Card>
              <CardContent sx={{ textAlign: 'center', py: 6 }}>
                <AppsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  Nenhuma empresa cadastrada
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  {apiStatus.available 
                    ? 'Cadastre empresas na aba "Empresas & Apps" primeiro'
                    : 'Inicie o backend para gerenciar páginas'
                  }
                </Typography>
              </CardContent>
            </Card>
          ) : (
            <Grid container spacing={3}>
              {empresas.map((empresa) => (
                <Grid item xs={12} key={empresa.id}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                        <BusinessIcon sx={{ color: empresa.cor, mr: 2 }} />
                        <Typography variant="h6" sx={{ flexGrow: 1 }}>
                          {empresa.nome} - Páginas
                        </Typography>
                        <Chip 
                          label={`${empresa.paginas?.length || 0} páginas`} 
                          size="small" 
                          variant="outlined" 
                          sx={{ mr: 1 }}
                        />

                        <Button
                          size="small"
                          startIcon={<AddIcon />}
                          onClick={() => handleOpenPaginaDialog(empresa.id)}
                          disabled={!apiStatus.available}
                          variant="contained"
                        >
                          Nova Página
                        </Button>
                      </Box>

                      {/* Páginas */}
                      {empresa.paginas?.length === 0 ? (
                        <Alert severity="info" sx={{ mt: 2 }}>
                          <Typography variant="body2">
                            <strong>💡 Nenhuma página configurada!</strong><br />
                            Use "Nova Página" para criar páginas personalizadas para esta empresa.
                          </Typography>
                        </Alert>
                      ) : (
                        <Grid container spacing={2}>
                          {empresa.paginas?.map((pagina) => (
                            <Grid item xs={12} sm={6} md={4} key={pagina._id}>
                              <Card variant="outlined" sx={{ height: '100%' }}>
                                <CardContent>
                                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                    <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
                                      {ICONES_PAGINA.find(i => i.value === pagina.icone)?.icon || <Dashboard />}
                                    </Box>
                                    <Typography variant="subtitle1" sx={{ flexGrow: 1 }}>
                                      {pagina.titulo}
                                    </Typography>
                                    <Chip 
                                      label={pagina.ordem} 
                                      size="small" 
                                      sx={{ mr: 1, minWidth: 30 }}
                                    />
                                  </Box>
                                  
                                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                    {pagina.descricao || 'Sem descrição'}
                                  </Typography>
                                  
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
                                    <Chip label={pagina.chave} size="small" variant="outlined" />
                                    <Chip label={pagina.rota} size="small" />
                                    {!pagina.ativo && (
                                      <Chip label="Inativo" size="small" color="error" />
                                    )}
                                    {!pagina.configuracao?.showInSidebar && (
                                      <Chip label="Oculto" size="small" color="warning" />
                                    )}
                                  </Box>
                                  
                                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                                    <IconButton 
                                      size="small"
                                      onClick={() => handleOpenPaginaDialog(empresa.id, pagina)}
                                      disabled={!apiStatus.available}
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                    <IconButton 
                                      size="small"
                                      onClick={() => handleDeletePagina(empresa.id, pagina._id)}
                                      color="error"
                                      disabled={!apiStatus.available}
                                    >
                                      <DeleteIcon fontSize="small" />
                                    </IconButton>
                                  </Box>
                                </CardContent>
                              </Card>
                            </Grid>
                          ))}
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          )}
        </Box>
      )}

      {tabValue === 2 && (
        <Box>
          {/* Configurações do Mashup */}
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
                <SettingsIcon sx={{ mr: 1 }} />
                Configurações Globais do Mashup
              </Typography>

              <Grid container spacing={3}>
                {/* Configurações Gerais */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 2 }}>
                    Configurações Gerais
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Nome da Configuração"
                        value={configForm.nome}
                        onChange={(e) => handleConfigFormChange('nome', e.target.value)}
                        fullWidth
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Ambiente</InputLabel>
                        <Select
                          value={configForm.configuracoes?.ambiente || 'cloud'}
                          onChange={(e) => handleConfigFormChange('configuracoes.ambiente', e.target.value)}
                        >
                          <MenuItem value="cloud">Qlik Cloud</MenuItem>
                          <MenuItem value="enterprise">Qlik Enterprise</MenuItem>
                          <MenuItem value="hybrid">Híbrido</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12}>
                  <Divider />
                </Grid>

                {/* Qlik Cloud - Só mostrar se ambiente for Cloud */}
                {configForm.configuracoes?.ambiente === 'cloud' && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                        <CloudIcon sx={{ mr: 1 }} />
                        Qlik Cloud
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            label="URL do Tenant"
                            value={configForm.qlikCloud?.tenantUrl || ''}
                            onChange={(e) => handleConfigFormChange('qlikCloud.tenantUrl', e.target.value)}
                            fullWidth
                            required
                            helperText="Ex: https://metricaplus.us.qlikcloud.com"
                            placeholder="https://seu-tenant.qlikcloud.com"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            label="Web Integration ID"
                            value={configForm.qlikCloud?.webIntegrationId || ''}
                            onChange={(e) => handleConfigFormChange('qlikCloud.webIntegrationId', e.target.value)}
                            fullWidth
                            required
                            helperText="Configure no Management Console"
                            placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Alert severity="info" sx={{ mt: 1 }}>
                            <Typography variant="body2">
                              <strong>💡 Como obter o Web Integration ID:</strong><br />
                              1. Acesse o Qlik Cloud Management Console<br />
                              2. Vá em Settings → Web integrations<br />
                              3. Crie uma nova integração com seu domínio<br />
                              4. Copie o ID gerado
                            </Typography>
                          </Alert>
                        </Grid>
                      </Grid>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider />
                    </Grid>
                  </>
                )}

                {/* Qlik Enterprise - Só mostrar se ambiente for Enterprise */}
                {configForm.configuracoes?.ambiente === 'enterprise' && (
                  <>
                    <Grid item xs={12}>
                      <Typography variant="subtitle1" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                        <StorageIcon sx={{ mr: 1 }} />
                        Qlik Enterprise
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="URL do Servidor"
                            value={configForm.qlikEnterprise?.serverUrl || ''}
                            onChange={(e) => handleConfigFormChange('qlikEnterprise.serverUrl', e.target.value)}
                            fullWidth
                            helperText="Ex: https://qlik-server.com"
                            placeholder="https://qlik-server.com"
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="Prefixo"
                            value={configForm.qlikEnterprise?.prefix || ''}
                            onChange={(e) => handleConfigFormChange('qlikEnterprise.prefix', e.target.value)}
                            fullWidth
                            placeholder="/virtual-proxy/"
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="Virtual Proxy"
                            value={configForm.qlikEnterprise?.virtualProxy || ''}
                            onChange={(e) => handleConfigFormChange('qlikEnterprise.virtualProxy', e.target.value)}
                            fullWidth
                            placeholder="header"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Alert severity="info" sx={{ mt: 1 }}>
                            <Typography variant="body2">
                              <strong>💡 Para Enterprise/Desktop:</strong><br />
                              • URL do Servidor: Apenas para Enterprise Server (Desktop detecta automaticamente)<br />
                              • Prefixo e Virtual Proxy: Configurações avançadas (opcional)
                            </Typography>
                          </Alert>
                        </Grid>
                      </Grid>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider />
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                    <Button
                      variant="contained"
                      onClick={handleSaveConfigMashup}
                      startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
                      disabled={saving || !apiStatus.available}
                    >
                      {saving ? 'Salvando...' : 'Salvar Configurações'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Box>
      )}

      {/* Dialog para Empresa */}
      <Dialog 
        open={dialogEmpresaOpen} 
        onClose={() => setDialogEmpresaOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingEmpresa ? 'Editar Empresa' : 'Nova Empresa'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="ID da Empresa"
                  value={empresaIdValue}
                  onChange={handleEmpresaIdChange}
                  fullWidth
                  required
                  disabled={!!editingEmpresa}
                  helperText="Identificador único (sem espaços)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Nome da Empresa"
                  value={empresaNomeValue}
                  onChange={handleEmpresaNomeChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Cor"
                  type="color"
                  value={empresaForm.cor}
                  onChange={(e) => handleEmpresaFormChange('cor', e.target.value)}
                  fullWidth
                />
              </Grid>
              
              {/* ✅ NOVO: Campo de upload de logo */}
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                    Logo da Empresa
                  </Typography>
                  
                  {/* Preview da logo */}
                  {logoPreview && (
                    <Box sx={{ mb: 2, textAlign: 'center' }}>
                      <Box
                        component="img"
                        src={logoPreview}
                        alt="Preview da logo"
                        sx={{
                          maxWidth: '100%',
                          maxHeight: 80,
                          objectFit: 'contain',
                          border: '2px dashed #ccc',
                          borderRadius: 2,
                          p: 1,
                          backgroundColor: '#f9f9f9'
                        }}
                      />
                    </Box>
                  )}
                  
                  {/* Botões de ação */}
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Button
                      variant="outlined"
                      component="label"
                      size="small"
                      disabled={uploadingLogo}
                      startIcon={uploadingLogo ? <CircularProgress size={16} /> : null}
                    >
                      {logoPreview ? 'Alterar Logo' : 'Carregar Logo'}
                      <input
                        type="file"
                        hidden
                        accept="image/png,image/jpeg,image/jpg,image/gif,image/svg+xml"
                        onChange={handleLogoUpload}
                      />
                    </Button>
                    
                    {logoPreview && (
                      <Button
                        variant="outlined"
                        color="error"
                        size="small"
                        onClick={handleRemoveLogo}
                      >
                        Remover
                      </Button>
                    )}
                  </Box>
                  
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                    PNG, JPG, GIF ou SVG. Máximo 2MB.
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDialogEmpresaOpen(false)} 
            startIcon={<CancelIcon />}
            disabled={saving}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleSaveEmpresa} 
            variant="contained"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            disabled={saving || !apiStatus.available}
          >
            {saving ? 'Salvando...' : 'Salvar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para App */}
      <Dialog 
        open={dialogAppOpen} 
        onClose={() => setDialogAppOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingApp ? 'Editar App' : 'Novo App'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="App ID"
                  value={appIdValue}
                  onChange={handleAppIdChange}
                  fullWidth
                  required
                  helperText="ID do app no Qlik Sense"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Nome do App"
                  value={appNomeValue}
                  onChange={handleAppNomeChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Categoria</InputLabel>
                  <Select
                    value={appForm.categoria}
                    onChange={(e) => handleAppFormChange('categoria', e.target.value)}
                  >
                    {CATEGORIAS_APP.map((cat) => (
                      <MenuItem key={cat.value} value={cat.value}>
                        {cat.icon} {cat.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Descrição"
                  value={appDescricaoValue}
                  onChange={handleAppDescricaoChange}
                  fullWidth
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDialogAppOpen(false)} 
            startIcon={<CancelIcon />}
            disabled={saving}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleSaveApp} 
            variant="contained"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            disabled={saving || !apiStatus.available}
          >
            {saving ? 'Salvando...' : 'Salvar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para Objeto */}
      <Dialog 
        open={dialogObjetoOpen} 
        onClose={() => setDialogObjetoOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {editingObjeto ? 'Editar Objeto' : 'Novo Objeto'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Chave"
                  value={chaveValue}
                  onChange={handleChaveChange}
                  fullWidth
                  required
                  helperText="Identificador único no app"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Object ID"
                  value={objectIdValue}
                  onChange={handleObjectIdChange}
                  fullWidth
                  required
                  helperText="ID do objeto no Qlik"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Nome do Objeto"
                  value={nomeValue}
                  onChange={handleNomeChange}
                  fullWidth
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Tipo</InputLabel>
                  <Select
                    value={objetoForm.tipo}
                    onChange={(e) => handleObjetoFormChange('tipo', e.target.value)}
                  >
                    {TIPOS_OBJETO.map((tipo) => (
                      <MenuItem key={tipo.value} value={tipo.value}>
                        {tipo.icon} {tipo.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Categoria</InputLabel>
                  <Select
                    value={objetoForm.categoria}
                    onChange={(e) => handleObjetoFormChange('categoria', e.target.value)}
                    label="Categoria"
                  >
                    {CATEGORIAS_OBJETO.map((categoria) => (
                      <MenuItem key={categoria.value} value={categoria.value}>
                        {categoria.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Descrição"
                  value={descricaoValue}
                  onChange={handleDescricaoChange}
                  fullWidth
                  multiline
                  rows={2}
                />
              </Grid>

              {/* Configurações Visuais */}
              <Grid item xs={12}>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" sx={{ mb: 2 }}>
                  🎨 Configurações Visuais
                </Typography>

                {/* Configurações KPI */}
                {objetoForm.tipo === 'kpi' && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2, color: 'primary.main' }}>
                      📊 Configurações KPI
                    </Typography>
                    
                    {/* Configurações básicas */}
                    <Grid container spacing={2} sx={{ mb: 3 }}>
                      <Grid item xs={12} sm={4}>
                        <FormControl fullWidth>
                          <InputLabel>Formato</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.kpiConfig?.formato || 'numero'}
                            onChange={(e) => handleObjetoFormChange('configuracao.kpiConfig.formato', e.target.value)}
                          >
                            {FORMATOS_KPI.map((formato) => (
                              <MenuItem key={formato.value} value={formato.value}>
                                {formato.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          label="Casas Decimais"
                          type="number"
                          value={objetoForm.configuracao?.kpiConfig?.casasDecimais || 0}
                          onChange={(e) => handleObjetoFormChange('configuracao.kpiConfig.casasDecimais', parseInt(e.target.value) || 0)}
                          fullWidth
                          inputProps={{ min: 0, max: 4 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          label="Cor (Legado)"
                          type="color"
                          value={objetoForm.configuracao?.kpiConfig?.color || '#667eea'}
                          onChange={(e) => handleObjetoFormChange('configuracao.kpiConfig.color', e.target.value)}
                          fullWidth
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Prefixo"
                          value={prefixValue}
                          onChange={handlePrefixChange}
                          fullWidth
                          helperText="Ex: R$, US$"
                          size="small"
                        />
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          label="Sufixo"
                          value={suffixValue}
                          onChange={handleSuffixChange}
                          fullWidth
                          helperText="Ex: %, km, kg"
                          size="small"
                        />
                      </Grid>
                    </Grid>

                    {/* NOVAS Configurações Visuais */}
                    <Paper elevation={2} sx={{ p: 3, backgroundColor: 'grey.50', borderRadius: 3 }}>
                      <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold', color: 'primary.main' }}>
                        🎨 Configurações Visuais Avançadas
                      </Typography>

                      {/* Seleção de Cor de Fundo */}
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                        Cor de Fundo
                      </Typography>
                      <Grid container spacing={1} sx={{ mb: 3 }}>
                        {COLOR_PALETTE.map((cor, index) => (
                          <Grid item key={index}>
                            <Tooltip title={`Cor ${index + 1}`}>
                              <Box
                                onClick={() => handleObjetoFormChange(`configuracao.kpiConfig.cor`, cor)}
                                sx={{
                                  width: 36,
                                  height: 36,
                                  backgroundColor: cor,
                                  borderRadius: '50%',
                                  cursor: 'pointer',
                                  border: objetoForm.configuracao?.kpiConfig?.cor === cor ? '4px solid #000' : '2px solid transparent',
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    transform: 'scale(1.15)',
                                    boxShadow: `0 6px 20px ${cor}60`
                                  }
                                }}
                              />
                            </Tooltip>
                          </Grid>
                        ))}
                      </Grid>

                      {/* Seleção de Cor da Fonte */}
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                        Cor da Fonte
                      </Typography>
                      <Grid container spacing={1} sx={{ mb: 3 }}>
                        {FONT_COLORS.map((cor, index) => (
                          <Grid item key={index}>
                            <Tooltip title={cor === '#ffffff' ? 'Branco' : cor === '#000000' ? 'Preto' : `Cor ${index - 1}`}>
                              <Box
                                onClick={() => handleObjetoFormChange(`configuracao.kpiConfig.corFonte`, cor)}
                                sx={{
                                  width: 36,
                                  height: 36,
                                  backgroundColor: cor,
                                  borderRadius: '50%',
                                  cursor: 'pointer',
                                  border: objetoForm.configuracao?.kpiConfig?.corFonte === cor ? '4px solid #000' : '2px solid #ccc',
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    transform: 'scale(1.15)',
                                    boxShadow: `0 6px 20px ${cor}60`
                                  }
                                }}
                              />
                            </Tooltip>
                          </Grid>
                        ))}
                      </Grid>

                      {/* Seleção de Ícone */}
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                        Ícone do KPI
                      </Typography>
                      <Grid container spacing={1} sx={{ mb: 3 }}>
                        {Object.entries(ICON_GALLERY).map(([key, IconComponent]) => (
                          <Grid item key={key}>
                            <Tooltip title={key.charAt(0).toUpperCase() + key.slice(1)}>
                              <Box
                                onClick={() => handleObjetoFormChange(`configuracao.kpiConfig.icone`, key)}
                                sx={{
                                  width: 44,
                                  height: 44,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  backgroundColor: objetoForm.configuracao?.kpiConfig?.icone === key ? 
                                    (objetoForm.configuracao?.kpiConfig?.cor || '#1976d2') + '20' : 'grey.100',
                                  border: objetoForm.configuracao?.kpiConfig?.icone === key ? 
                                    `3px solid ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'}` : '2px solid transparent',
                                  borderRadius: 3,
                                  cursor: 'pointer',
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    backgroundColor: (objetoForm.configuracao?.kpiConfig?.cor || '#1976d2') + '20',
                                    transform: 'scale(1.1)',
                                    boxShadow: `0 4px 12px ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'}30`
                                  }
                                }}
                              >
                                <IconComponent 
                                  sx={{ 
                                    fontSize: 22, 
                                    color: objetoForm.configuracao?.kpiConfig?.icone === key ? 
                                      (objetoForm.configuracao?.kpiConfig?.cor || '#1976d2') : 'grey.600'
                                  }} 
                                />
                              </Box>
                            </Tooltip>
                          </Grid>
                        ))}
                      </Grid>

                      {/* Opções adicionais */}
                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 3 }}>
                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <InputLabel>Mostrar Ícone</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.kpiConfig?.showIcon ?? true}
                            onChange={(e) => handleObjetoFormChange('configuracao.kpiConfig.showIcon', e.target.value)}
                            label="Mostrar Ícone"
                          >
                            <MenuItem value={true}>✅ Sim</MenuItem>
                            <MenuItem value={false}>❌ Não</MenuItem>
                          </Select>
                        </FormControl>

                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <InputLabel>Fundo Gradiente</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.kpiConfig?.gradiente ?? false}
                            onChange={(e) => handleObjetoFormChange('configuracao.kpiConfig.gradiente', e.target.value)}
                            label="Fundo Gradiente"
                          >
                            <MenuItem value={true}>🌈 Sim</MenuItem>
                            <MenuItem value={false}>⬜ Não</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      {/* Preview do KPI */}
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="body2" sx={{ mb: 2, fontWeight: 600 }}>
                          🔍 Preview do KPI
                        </Typography>
                        <Box 
                          sx={{ 
                            height: 120,
                            background: objetoForm.configuracao?.kpiConfig?.gradiente ? 
                              `linear-gradient(135deg, ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'} 0%, ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'}80 100%)` :
                              objetoForm.configuracao?.kpiConfig?.cor || '#1976d2',
                            border: `2px solid ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'}`,
                            borderRadius: 3,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            position: 'relative',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              border: `2px solid ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'}70`,
                              boxShadow: `0 8px 25px ${objetoForm.configuracao?.kpiConfig?.cor || '#1976d2'}20`
                            }
                          }}
                        >
                          {objetoForm.configuracao?.kpiConfig?.showIcon !== false && (
                            <Box sx={{ position: 'absolute', top: 12, right: 12, opacity: 0.7 }}>
                              {React.createElement(ICON_GALLERY[objetoForm.configuracao?.kpiConfig?.icone || 'analytics'], {
                                sx: { 
                                  fontSize: 24, 
                                  color: objetoForm.configuracao?.kpiConfig?.corFonte || '#ffffff' 
                                }
                              })}
                            </Box>
                          )}
                          <Box sx={{ textAlign: 'center' }}>
                            <Typography 
                              variant="caption"
                              sx={{ 
                                display: 'block',
                                mb: 0.5,
                                color: objetoForm.configuracao?.kpiConfig?.corFonte || '#ffffff',
                                fontSize: '0.7rem',
                                textTransform: 'uppercase',
                                fontWeight: 600,
                                opacity: 0.8
                              }}
                            >
                              {objetoForm.nome || 'Nome do KPI'}
                            </Typography>
                            <Typography 
                              sx={{ 
                                fontWeight: 'bold',
                                color: objetoForm.configuracao?.kpiConfig?.corFonte || '#ffffff',
                                fontSize: '1.8rem',
                                lineHeight: 1
                              }}
                            >
                              R$ 123,456
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Paper>
                  </Box>
                )}

                {/* Configurações Multi KPI */}
                {objetoForm.tipo === 'multiKpi' && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2, color: 'primary.main' }}>
                      📊 Configurações Multi KPI
                    </Typography>
                    
                    <Paper elevation={2} sx={{ p: 3, backgroundColor: 'grey.50', borderRadius: 3 }}>
                      <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold', color: 'primary.main' }}>
                        🎨 Layout Multi KPI
                      </Typography>

                      <Grid container spacing={2} sx={{ mb: 3 }}>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="KPIs por Linha"
                            type="number"
                            value={objetoForm.configuracao?.multiKpiConfig?.itemsPerRow || 3}
                            onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.itemsPerRow', parseInt(e.target.value) || 3)}
                            fullWidth
                            inputProps={{ min: 1, max: 6 }}
                            helperText="Quantos KPIs por linha"
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="Espaçamento"
                            type="number"
                            value={objetoForm.configuracao?.multiKpiConfig?.spacing || 2}
                            onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.spacing', parseInt(e.target.value) || 2)}
                            fullWidth
                            inputProps={{ min: 0, max: 5 }}
                            helperText="Espaço entre KPIs"
                          />
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <TextField
                            label="Elevação"
                            type="number"
                            value={objetoForm.configuracao?.multiKpiConfig?.elevation || 1}
                            onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.elevation', parseInt(e.target.value) || 1)}
                            fullWidth
                            inputProps={{ min: 0, max: 10 }}
                            helperText="Sombra dos cartões"
                          />
                        </Grid>
                      </Grid>

                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', mb: 3 }}>
                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <InputLabel>Mostrar Título</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.multiKpiConfig?.showTitle ?? true}
                            onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.showTitle', e.target.value)}
                            label="Mostrar Título"
                          >
                            <MenuItem value={true}>✅ Sim</MenuItem>
                            <MenuItem value={false}>❌ Não</MenuItem>
                          </Select>
                        </FormControl>

                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <InputLabel>Mostrar Ícones</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.multiKpiConfig?.showIcon ?? true}
                            onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.showIcon', e.target.value)}
                            label="Mostrar Ícones"
                          >
                            <MenuItem value={true}>✅ Sim</MenuItem>
                            <MenuItem value={false}>❌ Não</MenuItem>
                          </Select>
                        </FormControl>

                        <FormControl size="small" sx={{ minWidth: 120 }}>
                          <InputLabel>Layout</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.multiKpiConfig?.layout || 'unified'}
                            onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.layout', e.target.value)}
                            label="Layout"
                          >
                            <MenuItem value="grid">🔲 Grid (Cards separados)</MenuItem>
                            <MenuItem value="unified">🟢 Unificado (Fundo único)</MenuItem>
                            <MenuItem value="horizontal">➡️ Horizontal</MenuItem>
                          </Select>
                        </FormControl>
                      </Box>

                      {/* Configurações Visuais do Multi KPI */}
                      <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold', color: 'primary.main' }}>
                        🎨 Configurações Visuais Multi KPI
                      </Typography>

                      {/* Seleção de Cor de Fundo */}
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                        Cor de Fundo
                      </Typography>
                      <Grid container spacing={1} sx={{ mb: 3 }}>
                        {COLOR_PALETTE.map((cor, index) => (
                          <Grid item key={index}>
                            <Tooltip title={`Cor ${index + 1}`}>
                              <Box
                                onClick={() => handleObjetoFormChange(`configuracao.multiKpiConfig.cor`, cor)}
                                sx={{
                                  width: 36,
                                  height: 36,
                                  backgroundColor: cor,
                                  borderRadius: '50%',
                                  cursor: 'pointer',
                                  border: objetoForm.configuracao?.multiKpiConfig?.cor === cor ? '4px solid #000' : '2px solid transparent',
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    transform: 'scale(1.15)',
                                    boxShadow: `0 6px 20px ${cor}60`
                                  }
                                }}
                              />
                            </Tooltip>
                          </Grid>
                        ))}
                      </Grid>

                      {/* Seleção de Cor da Fonte */}
                      <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                        Cor da Fonte
                      </Typography>
                      <Grid container spacing={1} sx={{ mb: 3 }}>
                        {FONT_COLORS.map((cor, index) => (
                          <Grid item key={index}>
                            <Tooltip title={cor === '#ffffff' ? 'Branco' : cor === '#000000' ? 'Preto' : `Cor ${index - 1}`}>
                              <Box
                                onClick={() => handleObjetoFormChange(`configuracao.multiKpiConfig.corFonte`, cor)}
                                sx={{
                                  width: 36,
                                  height: 36,
                                  backgroundColor: cor,
                                  borderRadius: '50%',
                                  cursor: 'pointer',
                                  border: objetoForm.configuracao?.multiKpiConfig?.corFonte === cor ? '4px solid #000' : '2px solid #ccc',
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    transform: 'scale(1.15)',
                                    boxShadow: `0 6px 20px ${cor}60`
                                  }
                                }}
                              />
                            </Tooltip>
                          </Grid>
                        ))}
                      </Grid>

                      {/* ✅ NOVO: Flag Habilitar Ícone */}
                      <Box sx={{ mb: 3 }}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={objetoForm.configuracao?.multiKpiConfig?.showIcon === true}
                              onChange={(e) => handleObjetoFormChange('configuracao.multiKpiConfig.showIcon', e.target.checked)}
                            />
                          }
                          label="Habilitar Ícone no Multi KPI"
                        />
                        <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mt: 0.5 }}>
                          Quando habilitado, mostra ícone único à esquerda de todo o Multi KPI
                        </Typography>
                      </Box>

                      {/* ✅ NOVO: Seleção de Ícone - só aparece se habilitado */}
                      {objetoForm.configuracao?.multiKpiConfig?.showIcon === true && (
                        <>
                          <Typography variant="body2" sx={{ mb: 1, fontWeight: 600 }}>
                            Ícone do Multi KPI
                          </Typography>
                          <Grid container spacing={1} sx={{ mb: 3 }}>
                            {Object.entries(ICON_GALLERY).map(([key, IconComponent]) => (
                              <Grid item key={key}>
                                <Tooltip title={key.charAt(0).toUpperCase() + key.slice(1)}>
                                  <Box
                                    onClick={() => handleObjetoFormChange(`configuracao.multiKpiConfig.icone`, key)}
                                    sx={{
                                      width: 44,
                                      height: 44,
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      backgroundColor: objetoForm.configuracao?.multiKpiConfig?.icone === key ? 
                                        (objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2') + '20' : 'grey.100',
                                      border: objetoForm.configuracao?.multiKpiConfig?.icone === key ? 
                                        `3px solid ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'}` : '2px solid transparent',
                                      borderRadius: 3,
                                      cursor: 'pointer',
                                      transition: 'all 0.2s ease',
                                      '&:hover': {
                                        backgroundColor: (objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2') + '20',
                                        transform: 'scale(1.1)',
                                        boxShadow: `0 4px 12px ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'}30`
                                      }
                                    }}
                                  >
                                    <IconComponent 
                                      sx={{ 
                                        fontSize: 22, 
                                        color: objetoForm.configuracao?.multiKpiConfig?.icone === key ? 
                                          (objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2') : 'grey.600'
                                      }} 
                                    />
                                  </Box>
                                </Tooltip>
                              </Grid>
                            ))}
                          </Grid>
                        </>
                      )}

                      {/* ✅ NOVO: Preview do Multi KPI */}
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="body2" sx={{ mb: 2, fontWeight: 600 }}>
                          🔍 Preview do Multi KPI
                        </Typography>
                        <Box 
                          sx={{ 
                            minHeight: 200,
                            background: `linear-gradient(135deg, ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'} 0%, ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'}80 100%)`,
                            border: `2px solid ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'}`,
                            borderRadius: 3,
                            p: 3,
                            display: 'flex',
                            transition: 'all 0.3s ease',
                            '&:hover': {
                              border: `2px solid ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'}70`,
                              boxShadow: `0 8px 25px ${objetoForm.configuracao?.multiKpiConfig?.cor || '#1976d2'}20`
                            }
                          }}
                        >
                          {/* ✅ NOVO: Ícone único à esquerda - só aparece se habilitado */}
                          {objetoForm.configuracao?.multiKpiConfig?.showIcon === true && (
                            <Box 
                              sx={{ 
                                display: 'flex',
                                alignItems: 'center', // ✅ Centralizar apenas o ícone
                                mr: 3,
                                flexShrink: 0
                              }}
                            >
                              <Box 
                                sx={{ 
                                  width: 80,
                                  height: 80,
                                  backgroundColor: 'rgba(255, 255, 255, 0.15)',
                                  border: '1px solid rgba(255, 255, 255, 0.3)',
                                  borderRadius: 2, // ✅ Cantos suavemente arredondados
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                                }}
                              >
                                {React.createElement(ICON_GALLERY[objetoForm.configuracao?.multiKpiConfig?.icone || 'analytics'], {
                                  sx: { 
                                    fontSize: 36, 
                                    color: objetoForm.configuracao?.multiKpiConfig?.corFonte || '#ffffff' 
                                  }
                                })}
                              </Box>
                            </Box>
                          )}
                          
                          {/* Conteúdo à direita */}
                          <Box sx={{ flex: 1 }}>
                            {/* Título do Multi KPI */}
                            {objetoForm.configuracao?.multiKpiConfig?.showTitle !== false && (
                              <Typography 
                                variant="h6"
                                sx={{ 
                                  color: objetoForm.configuracao?.multiKpiConfig?.corFonte || '#ffffff',
                                  mb: 2,
                                  fontWeight: 600,
                                  textAlign: 'left'
                                }}
                              >
                                {objetoForm.nome || 'Multi KPI Preview'}
                              </Typography>
                            )}
                            
                            {/* ✅ NOVO: KPIs em layout vertical (um embaixo do outro) */}
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                              {['COM VENDA', 'INADIMPLENTE', 'NO JURÍDICO'].map((label, index) => (
                                <Box 
                                  key={index}
                                  sx={{ 
                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                    borderRadius: 2,
                                    p: 2,
                                    border: '1px solid rgba(255, 255, 255, 0.2)',
                                    textAlign: 'left'
                                  }}
                                >
                                  <Typography 
                                    variant="caption"
                                    sx={{ 
                                      display: 'block',
                                      color: objetoForm.configuracao?.multiKpiConfig?.corFonte || '#ffffff',
                                      fontSize: '0.75rem',
                                      textTransform: 'uppercase',
                                      fontWeight: 600,
                                      opacity: 0.8,
                                      mb: 0.5
                                    }}
                                  >
                                    {label}
                                  </Typography>
                                  <Typography 
                                    sx={{ 
                                      fontWeight: 'bold',
                                      color: objetoForm.configuracao?.multiKpiConfig?.corFonte || '#ffffff',
                                      fontSize: '1.5rem',
                                      lineHeight: 1
                                    }}
                                  >
                                    {index === 0 ? '1,234' : index === 1 ? '5.2%' : '89'}
                                  </Typography>
                                </Box>
                              ))}
                            </Box>
                          </Box>
                        </Box>
                      </Box>

                      {/* Nota sobre Multi KPI */}
                      <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          <strong>💡 Multi KPI:</strong><br />
                          • Use com objetos do Qlik do tipo "Texto e Imagem"<br />
                          • Cada linha do objeto deve ter: Label (dimensão) + Valor (medida)<br />
                          • <strong>Cor de Fundo e Fonte:</strong> Aplicadas ao layout unificado<br />
                          • <strong>Layout Unificado:</strong> Cria um fundo único com KPIs em linha vertical<br />
                          • <strong>Layout Grid:</strong> Cria cards separados para cada KPI
                        </Typography>
                      </Alert>
                    </Paper>
                  </Box>
                )}

                {/* Configurações Gráfico */}
                {objetoForm.tipo !== 'kpi' && objetoForm.tipo !== 'table' && objetoForm.tipo !== 'multiKpi' && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2, color: 'primary.main' }}>
                      📈 Configurações Gráfico
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <FormControl fullWidth>
                          <InputLabel>Tipo de Gráfico</InputLabel>
                          <Select
                            value={objetoForm.configuracao?.chartConfig?.tipoGrafico || objetoForm.tipo}
                            onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.tipoGrafico', e.target.value)}
                          >
                            {TIPOS_GRAFICO_ECHARTS.map((tipo) => (
                              <MenuItem key={tipo.value} value={tipo.value}>
                                {tipo.icon} {tipo.label}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <FormControl component="fieldset">
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.showTitle !== false}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.showTitle', e.target.checked)}
                                  />
                                }
                                label="Mostrar Título"
                                sx={{ fontSize: '0.875rem' }}
                              />
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.showLegend !== false}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.showLegend', e.target.checked)}
                                  />
                                }
                                label="Legenda"
                                sx={{ fontSize: '0.875rem' }}
                              />
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.showDataLabels === true}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.showDataLabels', e.target.checked)}
                                  />
                                }
                                label="Rótulos"
                                sx={{ fontSize: '0.875rem' }}
                              />
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.animacao !== false}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.animacao', e.target.checked)}
                                  />
                                }
                                label="Animação"
                                sx={{ fontSize: '0.875rem' }}
                              />
                            </Box>
                          </FormControl>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                )}

                {/* ✅ NOVO: Configurações de DataZoom para gráficos ECharts */}
                {(objetoForm.configuracao?.chartConfig?.tipoGrafico === 'bar' || 
                  objetoForm.configuracao?.chartConfig?.tipoGrafico === 'line' || 
                  objetoForm.configuracao?.chartConfig?.tipoGrafico === 'area' || 
                  objetoForm.configuracao?.chartConfig?.tipoGrafico === 'column' || 
                  objetoForm.configuracao?.chartConfig?.tipoGrafico === 'scatter' ||
                  objetoForm.configuracao?.chartConfig?.tipoGrafico === 'mixed') && (
                  <Paper sx={{ p: 3, mt: 3, backgroundColor: 'grey.50' }}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: 'primary.main' }}>
                      📊 Configurações de DataZoom
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={objetoForm.configuracao?.chartConfig?.dataZoom?.enabled === true}
                              onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.dataZoom.enabled', e.target.checked)}
                            />
                          }
                          label="Habilitar DataZoom (Zoom e Navegação no Gráfico)"
                        />
                      </Grid>
                      
                      {/* Configurações do DataZoom - só aparece se habilitado */}
                      {objetoForm.configuracao?.chartConfig?.dataZoom?.enabled && (
                        <>
                          <Grid item xs={12} sm={4}>
                            <FormControl fullWidth>
                              <InputLabel>Tipo de DataZoom</InputLabel>
                              <Select
                                value={objetoForm.configuracao?.chartConfig?.dataZoom?.type || 'slider'}
                                onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.dataZoom.type', e.target.value)}
                              >
                                <MenuItem value="slider">📊 Slider (Barra de Navegação)</MenuItem>
                                <MenuItem value="inside">🖱️ Inside (Zoom com Mouse)</MenuItem>
                                <MenuItem value="both">⚡ Ambos (Slider + Mouse)</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          
                          <Grid item xs={12} sm={4}>
                            <TextField
                              fullWidth
                              label="Posição Inicial (%)"
                              type="number"
                              value={objetoForm.configuracao?.chartConfig?.dataZoom?.start || 0}
                              onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.dataZoom.start', parseInt(e.target.value) || 0)}
                              inputProps={{ min: 0, max: 100 }}
                              helperText="0 = início dos dados"
                            />
                          </Grid>
                          
                          <Grid item xs={12} sm={4}>
                            <TextField
                              fullWidth
                              label="Posição Final (%)"
                              type="number"
                              value={objetoForm.configuracao?.chartConfig?.dataZoom?.end || 100}
                              onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.dataZoom.end', parseInt(e.target.value) || 100)}
                              inputProps={{ min: 0, max: 100 }}
                              helperText="100 = final dos dados"
                            />
                          </Grid>
                          
                          <Grid item xs={12}>
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.dataZoom?.showDetail !== false}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.dataZoom.showDetail', e.target.checked)}
                                  />
                                }
                                label="Mostrar Detalhes"
                              />
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.dataZoom?.realtime !== false}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.dataZoom.realtime', e.target.checked)}
                                  />
                                }
                                label="Atualização em Tempo Real"
                              />
                            </Box>
                          </Grid>
                        </>
                      )}
                    </Grid>
                    
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        <strong>💡 DataZoom:</strong><br />
                        • <strong>Slider:</strong> Mostra uma barra de navegação na parte inferior<br />
                        • <strong>Inside:</strong> Permite zoom com scroll do mouse e arrastar para navegar<br />
                        • <strong>Ambos:</strong> Combina as duas opções para máxima flexibilidade<br />
                        • <strong>Posições:</strong> Definem a janela inicial de visualização dos dados
                      </Typography>
                    </Alert>
                  </Paper>
                )}
                
                {/* ✅ NOVO: Configurações específicas para Gráfico Misto */}
                {objetoForm.configuracao?.chartConfig?.tipoGrafico === 'mixed' && (
                  <Paper sx={{ p: 3, mt: 3, backgroundColor: 'warning.lighter' }}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: 'warning.dark' }}>
                      📊📈 Configurações de Gráfico Misto (Barras + Linhas)
                    </Typography>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Séries como Barras (índices separados por vírgula)"
                          value={barSeriesInput}
                          onChange={(e) => {
                            // ✅ CORRIGIDO: Permitir digitação livre com estado local
                            const inputValue = e.target.value
                            setBarSeriesInput(inputValue) // Atualizar estado local imediatamente
                            
                            // Processar apenas valores válidos para lógica interna
                            if (!inputValue.trim()) {
                              handleObjetoFormChange('configuracao.chartConfig.mixedConfig.barSeries', [])
                              return
                            }
                            
                            const barIndices = inputValue
                              .split(',')
                              .map(i => i.trim())
                              .filter(i => i !== '' && !isNaN(parseInt(i)))
                              .map(i => parseInt(i))
                            
                            handleObjetoFormChange('configuracao.chartConfig.mixedConfig.barSeries', barIndices)
                          }}
                          helperText="Ex: 0,2 (primeira e terceira séries como barras)"
                        />
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Séries como Linhas (índices separados por vírgula)"
                          value={lineSeriesInput}
                          onChange={(e) => {
                            // ✅ CORRIGIDO: Permitir digitação livre com estado local
                            const inputValue = e.target.value
                            setLineSeriesInput(inputValue) // Atualizar estado local imediatamente
                            
                            // Processar apenas valores válidos para lógica interna
                            if (!inputValue.trim()) {
                              handleObjetoFormChange('configuracao.chartConfig.mixedConfig.lineSeries', [])
                              return
                            }
                            
                            const lineIndices = inputValue
                              .split(',')
                              .map(i => i.trim())
                              .filter(i => i !== '' && !isNaN(parseInt(i)))
                              .map(i => parseInt(i))
                            
                            handleObjetoFormChange('configuracao.chartConfig.mixedConfig.lineSeries', lineIndices)
                          }}
                          helperText="Ex: 1,3 (segunda e quarta séries como linhas)"
                        />
                      </Grid>
                    </Grid>
                    
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        <strong>⚠️ Gráfico Misto:</strong><br />
                        • Use com objetos do Qlik que tenham múltiplas medidas<br />
                        • <strong>Índices começam em 0:</strong> 0=primeira série, 1=segunda série, etc.<br />
                        • <strong>Exemplo:</strong> Se você tem 3 medidas e quer a primeira e terceira como barras, use "0,2"<br />
                        • <strong>Linhas usam eixo Y direito</strong> para permitir escalas diferentes<br />
                        • Se não especificar, séries pares serão barras e ímpares serão linhas automaticamente
                      </Typography>
                    </Alert>
                    
                    {/* ✅ NOVO: Configurações avançadas de porcentagem para gráfico mixed */}
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: 'success.main' }}>
                        📊 Configurações de Escala de Porcentagem
                      </Typography>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={objetoForm.configuracao?.chartConfig?.mixedConfig?.usePercentageScale === true}
                                onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.mixedConfig.usePercentageScale', e.target.checked)}
                              />
                            }
                            label="Usar Escala de Porcentagem para Linhas"
                          />
                          <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mt: 0.5 }}>
                            Quando habilitado, as linhas usarão eixo Y direito com escala de porcentagem
                          </Typography>
                        </Grid>
                        
                        {/* Configurações específicas de porcentagem - só aparecem se habilitado */}
                        {objetoForm.configuracao?.chartConfig?.mixedConfig?.usePercentageScale && (
                          <>
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                label="Valor Mínimo do Eixo (%)"
                                type="number"
                                value={objetoForm.configuracao?.chartConfig?.mixedConfig?.percentageAxisMin || 0}
                                onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.mixedConfig.percentageAxisMin', parseInt(e.target.value) || 0)}
                                inputProps={{ min: 0, max: 100 }}
                                helperText="Mínimo do eixo de porcentagem"
                              />
                            </Grid>
                            
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                label="Valor Máximo do Eixo (%)"
                                type="number"
                                value={objetoForm.configuracao?.chartConfig?.mixedConfig?.percentageAxisMax || 100}
                                onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.mixedConfig.percentageAxisMax', parseInt(e.target.value) || 100)}
                                inputProps={{ min: 1, max: 500 }}
                                helperText="Máximo do eixo de porcentagem"
                              />
                            </Grid>
                            
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                label="Nome do Eixo de Valores"
                                value={objetoForm.configuracao?.chartConfig?.mixedConfig?.valueAxisName || 'Valores'}
                                onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.mixedConfig.valueAxisName', e.target.value)}
                                helperText="Título do eixo Y esquerdo (barras)"
                              />
                            </Grid>
                            
                            <Grid item xs={12} sm={6}>
                              <TextField
                                fullWidth
                                label="Nome do Eixo de Porcentagem"
                                value={objetoForm.configuracao?.chartConfig?.mixedConfig?.percentageAxisName || 'Porcentagem (%)'}
                                onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.mixedConfig.percentageAxisName', e.target.value)}
                                helperText="Título do eixo Y direito (linhas)"
                              />
                            </Grid>
                            
                            <Grid item xs={12}>
                              <FormControlLabel
                                control={
                                  <Checkbox
                                    checked={objetoForm.configuracao?.chartConfig?.mixedConfig?.autoDetectPercentage !== false}
                                    onChange={(e) => handleObjetoFormChange('configuracao.chartConfig.mixedConfig.autoDetectPercentage', e.target.checked)}
                                  />
                                }
                                label="Detectar Automaticamente Valores Decimais como Porcentagem"
                              />
                              <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mt: 0.5 }}>
                                Converte valores entre 0-1 para porcentagem (ex: 0.25 → 25%)
                              </Typography>
                            </Grid>
                          </>
                        )}
                      </Grid>
                    </Box>
                  </Paper>
                )}

                {/* ✅ NOVO: Configurações de Tabela */}
                {objetoForm.tipo === 'table' && (
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 2, color: 'primary.main' }}>
                      📋 Configurações de Tabela
                    </Typography>
                    
                    <Paper sx={{ p: 3, backgroundColor: 'grey.50' }}>
                      <Grid container spacing={2}>
                        {/* Configurações de Exibição */}
                        <Grid item xs={12}>
                          <Typography variant="subtitle3" sx={{ mb: 1, fontWeight: 'bold', color: 'primary.main' }}>
                            🎨 Configurações de Exibição
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.showTitle !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.showTitle', e.target.checked)}
                                />
                              }
                              label="Mostrar Título"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.showSearch !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.showSearch', e.target.checked)}
                                />
                              }
                              label="Barra de Busca"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.showPagination !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.showPagination', e.target.checked)}
                                />
                              }
                              label="Paginação"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.showFilters !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.showFilters', e.target.checked)}
                                />
                              }
                              label="Filtros"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.showExport === true}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.showExport', e.target.checked)}
                                />
                              }
                              label="Botão Exportar"
                            />
                          </Box>
                        </Grid>

                        {/* Configurações de Layout */}
                        <Grid item xs={12}>
                          <Typography variant="subtitle3" sx={{ mb: 1, fontWeight: 'bold', color: 'secondary.main' }}>
                            📐 Configurações de Layout
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.showRowNumbers === true}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.showRowNumbers', e.target.checked)}
                                />
                              }
                              label="Números das Linhas"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.alternateRowColors !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.alternateRowColors', e.target.checked)}
                                />
                              }
                              label="Cores Alternadas"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.compactView === true}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.compactView', e.target.checked)}
                                />
                              }
                              label="Visualização Compacta"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.stickyHeader !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.stickyHeader', e.target.checked)}
                                />
                              }
                              label="Cabeçalho Fixo"
                            />
                            <FormControlLabel
                              control={
                                <Checkbox
                                  checked={objetoForm.configuracao?.tableConfig?.enableSorting !== false}
                                  onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.enableSorting', e.target.checked)}
                                />
                              }
                              label="Ordenação"
                            />
                          </Box>
                        </Grid>

                        {/* Configurações de Cor e Tamanho */}
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Cor do Cabeçalho"
                            type="color"
                            value={objetoForm.configuracao?.tableConfig?.headerBackgroundColor || '#1976d2'}
                            onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.headerBackgroundColor', e.target.value)}
                            InputProps={{
                              sx: { height: 56 }
                            }}
                          />
                        </Grid>
                        
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Cor do Texto do Cabeçalho"
                            type="color"
                            value={objetoForm.configuracao?.tableConfig?.headerTextColor || '#ffffff'}
                            onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.headerTextColor', e.target.value)}
                            InputProps={{
                              sx: { height: 56 }
                            }}
                          />
                        </Grid>

                        {/* Configurações de Paginação */}
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Linhas por Página (Padrão)"
                            type="number"
                            value={objetoForm.configuracao?.tableConfig?.defaultRowsPerPage || 5}
                            onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.defaultRowsPerPage', parseInt(e.target.value) || 5)}
                            inputProps={{ min: 1, max: 100 }}
                            helperText="Quantidade inicial de linhas por página"
                          />
                        </Grid>

                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="Altura Máxima (px)"
                            type="number"
                            value={objetoForm.configuracao?.tableConfig?.maxHeight || 600}
                            onChange={(e) => handleObjetoFormChange('configuracao.tableConfig.maxHeight', parseInt(e.target.value) || 600)}
                            inputProps={{ min: 200, max: 1200 }}
                            helperText="Altura máxima da tabela em pixels"
                          />
                        </Grid>
                      </Grid>

                      <Alert severity="info" sx={{ mt: 2 }}>
                        <Typography variant="body2">
                          <strong>💡 Dicas para Tabelas:</strong><br />
                          • <strong>Busca:</strong> Procura em todas as colunas automaticamente<br />
                          • <strong>Ordenação:</strong> Clique nos cabeçalhos das colunas para ordenar<br />
                          • <strong>Filtros:</strong> Filtros avançados por coluna (em breve)<br />
                          • <strong>Responsivo:</strong> A tabela se adapta automaticamente ao tamanho da tela<br />
                          • <strong>Tipos de Coluna:</strong> Suporta texto, números, moeda, porcentagem, status, avatar e tendência
                        </Typography>
                      </Alert>
                    </Paper>
                  </Box>
                )}
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDialogObjetoOpen(false)} 
            startIcon={<CancelIcon />}
            disabled={saving}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleSaveObjeto} 
            variant="contained"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            disabled={saving || !apiStatus.available}
          >
            {saving ? 'Salvando...' : 'Salvar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog para Página */}
      <Dialog 
        open={dialogPaginaOpen} 
        onClose={() => setDialogPaginaOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          {editingPagina ? 'Editar Página' : 'Nova Página'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Chave da Página"
                  value={paginaChaveValue}
                  onChange={handlePaginaChaveChange}
                  fullWidth
                  required
                  disabled={!!editingPagina}
                  helperText="Identificador único (sem espaços)"
                  placeholder="ex: vendas, dashboard, relatorios"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Título da Página"
                  value={paginaTituloValue}
                  onChange={handlePaginaTituloChange}
                  fullWidth
                  required
                  helperText="Nome que aparece no menu"
                  placeholder="ex: Vendas, Dashboard, Relatórios"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Descrição"
                  value={paginaDescricaoValue}
                  onChange={handlePaginaDescricaoChange}
                  fullWidth
                  multiline
                  rows={2}
                  helperText="Descrição curta que aparece abaixo do título"
                  placeholder="ex: Análise de vendas e performance"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel>Ícone</InputLabel>
                  <Select
                    value={paginaForm.icone}
                    onChange={(e) => handlePaginaFormChange('icone', e.target.value)}
                    label="Ícone"
                  >
                    {ICONES_PAGINA.map((icone) => (
                      <MenuItem key={icone.value} value={icone.value}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minWidth: 40 }}>
                          {icone.icon}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                  <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', color: 'text.secondary' }}>
                    <Typography variant="caption" sx={{ mr: 1 }}>Preview:</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                      {ICONES_PAGINA.find(i => i.value === paginaForm.icone)?.icon || <Dashboard />}
                    </Box>
                  </Box>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Rota"
                  value={paginaRotaValue}
                  onChange={handlePaginaRotaChange}
                  fullWidth
                  required
                  helperText="URL da página (deve começar com /)"
                  placeholder="ex: /vendas, /dashboard, /relatorios"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Ordem"
                  type="number"
                  value={paginaForm.ordem}
                  onChange={(e) => handlePaginaFormChange('ordem', parseInt(e.target.value) || 1)}
                  fullWidth
                  inputProps={{ min: 1 }}
                  helperText="Posição no menu (menor número = primeira posição)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Box>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={paginaForm.ativo}
                        onChange={(e) => handlePaginaFormChange('ativo', e.target.checked)}
                      />
                    }
                    label="Página Ativa"
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={paginaForm.configuracao?.showInSidebar}
                        onChange={(e) => handlePaginaFormChange('configuracao.showInSidebar', e.target.checked)}
                      />
                    }
                    label="Mostrar no Menu"
                  />
                </Box>
              </Grid>
            </Grid>

            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>💡 Dicas sobre Páginas:</strong><br />
                • <strong>Chave:</strong> Use apenas letras minúsculas, números e hífens<br />
                • <strong>Rota:</strong> Páginas padrão (dashboard, vendas, helpdesk, financeiro) usam rotas fixas<br />
                • <strong>Páginas personalizadas:</strong> Usam a rota <code>/pagina/:chave</code> automaticamente<br />
                • <strong>Ordem:</strong> Define a sequência no menu lateral
              </Typography>
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDialogPaginaOpen(false)} 
            startIcon={<CancelIcon />}
            disabled={saving}
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleSavePagina} 
            variant="contained"
            startIcon={saving ? <CircularProgress size={20} /> : <SaveIcon />}
            disabled={saving || !apiStatus.available}
          >
            {saving ? 'Salvando...' : 'Salvar'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog de confirmação customizado */}
      <Dialog open={confirmDialog.open} onClose={closeConfirmDialog}>
        <DialogTitle>Confirmar Exclusão</DialogTitle>
        <DialogContent>
          <Typography>Tem certeza que deseja excluir este item? Esta ação não pode ser desfeita.</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeConfirmDialog} disabled={saving}>Cancelar</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained" disabled={saving}>
            {saving ? 'Excluindo...' : 'Excluir'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default Configuracoes 
