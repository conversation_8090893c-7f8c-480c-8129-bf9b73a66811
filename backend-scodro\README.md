# 🚀 Backend SCODRO - Mashup Qlik API

Backend Node.js para gerenciamento de empresas e objetos Qlik no projeto Mashup SCODRO. Compatível com Qlik Cloud e Qlik Enterprise.

## 📋 Características

- ✅ **Node.js + Express** - API REST moderna
- ✅ **MongoDB Atlas** - Banco de dados na nuvem
- ✅ **Mongoose** - ODM para MongoDB
- ✅ **Validação Joi** - Validação robusta de dados
- ✅ **Arquitetura MVC** - Código organizado e escalável
- ✅ **Rate Limiting** - Proteção contra spam
- ✅ **CORS** - Configuração de segurança
- ✅ **Error Handling** - Tratamento centralizado de erros
- ✅ **Health Checks** - Monitoramento da API

## 🏗️ Estrutura do Projeto

```
backend-scodro/
├── src/
│   ├── config/
│   │   └── database.js          # Configuração MongoDB
│   ├── controllers/
│   │   ├── empresaController.js # Controller de empresas
│   │   └── objetoController.js  # Controller de objetos
│   ├── models/
│   │   ├── Empresa.js           # Modelo de empresa
│   │   └── Objeto.js            # Modelo de objeto
│   ├── routes/
│   │   ├── empresaRoutes.js     # Rotas de empresas
│   │   ├── objetoRoutes.js      # Rotas de objetos
│   │   └── healthRoutes.js      # Rotas de health check
│   ├── validators/
│   │   ├── empresaValidator.js  # Validações de empresa
│   │   └── objetoValidator.js   # Validações de objeto
│   ├── middleware/
│   │   └── errorHandler.js      # Middleware de erros
│   └── server.js                # Servidor principal
├── package.json
├── env.example                  # Exemplo de variáveis de ambiente
└── README.md
```

## 🚀 Instalação e Configuração

### 1. Instalar Dependências

```bash
cd backend-scodro
npm install
```

### 2. Configurar Variáveis de Ambiente

Copie o arquivo `env.example` para `.env` e configure:

```bash
cp env.example .env
```

Edite o arquivo `.env`:

```env
# Configurações do Servidor
PORT=3031
NODE_ENV=development

# MongoDB Atlas (já configurado)
MONGO_URI=mongodb+srv://scodro_admin:<EMAIL>/scodro_mashup?retryWrites=true&w=majority

# Configurações de CORS
CORS_ORIGIN=http://localhost:3333
```

### 3. Executar o Servidor

```bash
# Desenvolvimento (com nodemon)
npm run dev

# Produção
npm start
```

O servidor estará disponível em: `http://localhost:3031`

## 📊 API Endpoints

### 🏢 Empresas

#### Listar Empresas
```http
GET /api/empresas
Query Parameters:
- ativo: true/false (filtrar por status)
- incluirObjetos: true/false (incluir objetos relacionados)
```

#### Obter Empresa
```http
GET /api/empresas/:id
Query Parameters:
- incluirObjetos: true/false
```

#### Obter Configuração para Frontend
```http
GET /api/empresas/:id/config
```

#### Criar Empresa
```http
POST /api/empresas
Content-Type: application/json

{
  "id": "empresa-exemplo",
  "nome": "Empresa Exemplo",
  "cor": "#667eea",
  "appId": "APP_ID_QLIK",
  "qlikConfig": {
    "webIntegrationId": "WEB_INTEGRATION_ID",
    "serverUrl": "https://servidor.qlik.com"
  }
}
```

#### Atualizar Empresa
```http
PUT /api/empresas/:id
Content-Type: application/json

{
  "nome": "Novo Nome",
  "cor": "#764ba2"
}
```

#### Excluir/Desativar Empresa
```http
DELETE /api/empresas/:id
Query Parameters:
- forceDelete: true (exclusão permanente)
```

#### Reativar Empresa
```http
PATCH /api/empresas/:id/reativar
```

### 📊 Objetos

#### Listar Objetos por Empresa
```http
GET /api/objetos/:empresaId
Query Parameters:
- categoria: dashboard/vendas/helpdesk/financeiro/geral
- tipo: kpi/chart/table/filter/text/image/other
- ativo: true/false
```

#### Obter Objetos por Categoria
```http
GET /api/objetos/:empresaId/categoria/:categoria
```

#### Obter Objeto Específico
```http
GET /api/objetos/:empresaId/:objetoId
```

#### Criar Objeto
```http
POST /api/objetos/:empresaId
Content-Type: application/json

{
  "chave": "kpi1",
  "objectId": "OBJECT_ID_QLIK",
  "nome": "KPI Vendas",
  "tipo": "kpi",
  "categoria": "dashboard",
  "descricao": "KPI de vendas mensais",
  "configuracao": {
    "altura": "200px",
    "largura": "100%"
  },
  "ordem": 1
}
```

#### Criar Múltiplos Objetos
```http
POST /api/objetos/:empresaId/multiplos
Content-Type: application/json

{
  "objetos": [
    {
      "chave": "kpi1",
      "objectId": "OBJECT_ID_1",
      "nome": "KPI 1",
      "tipo": "kpi",
      "categoria": "dashboard"
    },
    {
      "chave": "chart1",
      "objectId": "OBJECT_ID_2",
      "nome": "Gráfico 1",
      "tipo": "chart",
      "categoria": "vendas"
    }
  ]
}
```

#### Atualizar Objeto
```http
PUT /api/objetos/:empresaId/:objetoId
Content-Type: application/json

{
  "nome": "Novo Nome",
  "ordem": 2
}
```

#### Reordenar Objetos
```http
PATCH /api/objetos/:empresaId/reordenar
Content-Type: application/json

{
  "objetos": [
    { "id": "OBJECT_MONGO_ID_1", "ordem": 1 },
    { "id": "OBJECT_MONGO_ID_2", "ordem": 2 }
  ]
}
```

#### Excluir/Desativar Objeto
```http
DELETE /api/objetos/:empresaId/:objetoId
Query Parameters:
- forceDelete: true (exclusão permanente)
```

### 🔍 Health Check

#### Status Básico
```http
GET /api/health
```

#### Status Detalhado
```http
GET /api/health/detailed
```

#### Status do Banco
```http
GET /api/health/database
```

## 🗄️ Modelos de Dados

### Empresa
```javascript
{
  id: String,              // ID único (slug)
  nome: String,            // Nome da empresa
  cor: String,             // Cor em hexadecimal
  ativo: Boolean,          // Status ativo/inativo
  appId: String,           // ID do app Qlik único
  qlikConfig: {
    webIntegrationId: String,  // Para Qlik Cloud
    serverUrl: String,         // Para Qlik Enterprise
    prefix: String             // Prefixo adicional
  },
  createdAt: Date,
  updatedAt: Date,
  createdBy: String
}
```

### Objeto
```javascript
{
  empresaId: String,       // Referência à empresa
  chave: String,           // Chave única (ex: kpi1, chart1)
  objectId: String,        // ID do objeto no Qlik
  nome: String,            // Nome descritivo
  tipo: String,            // kpi/chart/table/filter/text/image/other
  categoria: String,       // dashboard/vendas/helpdesk/financeiro/geral
  descricao: String,       // Descrição opcional
  configuracao: {
    altura: String,        // Altura do objeto
    largura: String,       // Largura do objeto
    exibirTitulo: Boolean, // Exibir título
    permitirSelecao: Boolean, // Permitir seleções
    opcoes: Object         // Configurações específicas
  },
  ordem: Number,           // Ordem de exibição
  ativo: Boolean,          // Status ativo/inativo
  createdAt: Date,
  updatedAt: Date,
  createdBy: String
}
```

## 🔧 Configuração do Frontend

Para conectar o frontend ao backend, atualize as configurações:

### 1. Criar Serviço de API (Frontend)

```javascript
// src/services/api.js
const API_BASE_URL = 'http://localhost:3031/api';

export const empresaService = {
  async listarEmpresas() {
    const response = await fetch(`${API_BASE_URL}/empresas?incluirObjetos=true`);
    return response.json();
  },

  async obterConfigEmpresa(empresaId) {
    const response = await fetch(`${API_BASE_URL}/empresas/${empresaId}/config`);
    return response.json();
  }
};
```

### 2. Atualizar Configuração de Apps (Frontend)

```javascript
// src/config/apps.js
import { empresaService } from '../services/api';

export const carregarEmpresasDoBackend = async () => {
  try {
    const response = await empresaService.listarEmpresas();
    if (response.success) {
      return response.data;
    }
  } catch (error) {
    console.error('Erro ao carregar empresas:', error);
    return [];
  }
};
```

## 🧪 Testando a API

### Usando curl

```bash
# Listar empresas
curl http://localhost:3031/api/empresas

# Criar empresa
curl -X POST http://localhost:3031/api/empresas \
  -H "Content-Type: application/json" \
  -d '{
    "id": "empresa-teste",
    "nome": "Empresa Teste",
    "appId": "APP_ID_TESTE"
  }'

# Health check
curl http://localhost:3031/api/health
```

### Usando Postman

Importe a collection com os endpoints:
- Base URL: `http://localhost:3031`
- Headers: `Content-Type: application/json`

## 🚀 Deploy

### Variáveis de Ambiente para Produção

```env
NODE_ENV=production
PORT=3031
MONGO_URI=sua_mongo_uri_producao
CORS_ORIGIN=https://seu-dominio.com
```

### Docker (Opcional)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src ./src
EXPOSE 3031
CMD ["npm", "start"]
```

## 🔍 Monitoramento

### Logs
- Logs de erro são exibidos no console
- Em produção, considere usar Winston ou similar

### Health Checks
- `/api/health` - Status básico
- `/api/health/detailed` - Status completo
- `/api/health/database` - Status do banco

## 🛠️ Desenvolvimento

### Scripts Disponíveis

```bash
npm run dev      # Desenvolvimento com nodemon
npm start        # Produção
npm run lint     # Verificar código
npm run lint:fix # Corrigir problemas
npm test         # Executar testes (quando implementados)
```

### Estrutura de Resposta Padrão

```javascript
// Sucesso
{
  "success": true,
  "data": {...},
  "message": "Operação realizada com sucesso"
}

// Erro
{
  "success": false,
  "error": "Mensagem de erro",
  "details": ["Detalhes específicos"]
}
```

## 📞 Suporte

Para dúvidas ou problemas:

1. Verifique os logs do servidor
2. Teste os health checks
3. Confirme a conectividade com MongoDB
4. Verifique as configurações de CORS

---

**Versão**: 1.0.0  
**Compatibilidade**: Node.js 16+  
**Banco**: MongoDB Atlas  
**Desenvolvido para**: Projeto Mashup Qlik SCODRO 