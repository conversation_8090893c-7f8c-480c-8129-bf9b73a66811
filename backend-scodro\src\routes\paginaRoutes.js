const express = require('express');
const router = express.Router();
const paginaController = require('../controllers/paginaController');

// Rotas para páginas por empresa
// GET /api/empresas/:empresaId/paginas - Listar páginas da empresa
router.get('/:empresaId/paginas', paginaController.listarPaginas);

// GET /api/empresas/:empresaId/paginas/:id - Obter página específica
router.get('/:empresaId/paginas/:id', paginaController.obterPagina);

// POST /api/empresas/:empresaId/paginas - Criar nova página
router.post('/:empresaId/paginas', paginaController.criarPagina);

// PUT /api/empresas/:empresaId/paginas/:id - Atualizar página
router.put('/:empresaId/paginas/:id', paginaController.atualizarPagina);

// DELETE /api/empresas/:empresaId/paginas/:id - Excluir/desativar página
router.delete('/:empresaId/paginas/:id', paginaController.excluirPagina);

// PATCH /api/empresas/:empresaId/paginas/:id/reativar - Reativar página
router.patch('/:empresaId/paginas/:id/reativar', paginaController.reativarPagina);

// POST /api/empresas/:empresaId/paginas/reordenar - Reordenar páginas
router.post('/:empresaId/paginas/reordenar', paginaController.reordenarPaginas);

// POST /api/empresas/:empresaId/paginas/criar-padrao - Criar páginas padrão
router.post('/:empresaId/paginas/criar-padrao', paginaController.criarPaginasPadrao);

module.exports = router; 