/* CustomChart.css - Estilos específicos para ECharts */

/* Garantir que o container do ECharts funcione corretamente */
.echarts-for-react {
  height: 100% !important;
  width: 100% !important;
}

/* Resetar possíveis interferências em elementos SVG */
.echarts-for-react svg {
  background: transparent !important;
}

/* ✅ NOVO: Estilos para o layout elegante dos gráficos */
.custom-chart-card {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

/* ✅ NOVO: Estilo para títulos à esquerda com linha sutil */
.custom-chart-title {
  position: relative;
  padding-bottom: 8px;
  margin-bottom: 12px;
}

.custom-chart-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    rgba(0, 0, 0, 0.1) 0%, 
    rgba(0, 0, 0, 0.05) 50%, 
    transparent 100%
  );
}

/* ✅ NOVO: Melhorar o visual dos títulos no ECharts */
.echarts-for-react .echarts-title {
  text-align: left !important;
}

/* FORÇAR fontes Roboto sobre estilos do Qlik - PRIORIDADE MÁXIMA */
.echarts-for-react svg text,
.echarts-for-react text,
.echarts-for-react *[style*="font-family"],
.echarts-for-react .echarts-axis-label,
.echarts-for-react .echarts-title,
.echarts-for-react .echarts-legend-item,
.echarts-for-react .echarts-tooltip {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  font-size: 11px !important;
  user-select: none;
}

/* Garantir que textos SVG sigam o tema */
.echarts-for-react svg text {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  font-size: 11px !important;
  user-select: none;
}

/* Resetar estilos que podem interferir com Canvas */
.echarts-for-react canvas {
  background: transparent !important;
  border: none !important;
  outline: none !important;
}

/* Garantir que tooltips funcionem corretamente */
.echarts-tooltip {
  pointer-events: none;
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  z-index: 9999;
}

/* Resetar possíveis estilos de lista para legendas */
.echarts-for-react ul,
.echarts-for-react li {
  list-style: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Garantir que elementos de gráfico não sejam afetados por CSS global */
.echarts-for-react * {
  box-sizing: content-box;
}

/* Prevenir interferência de normalize.css ou reset.css */
.echarts-for-react svg line,
.echarts-for-react svg path,
.echarts-for-react svg circle,
.echarts-for-react svg rect,
.echarts-for-react svg polygon {
  fill: none;
  stroke-width: 1;
}

/* Garantir que animações funcionem */
.echarts-for-react svg * {
  transition: none !important;
  animation: none !important;
}

/* Corrigir possíveis problemas com z-index */
.echarts-for-react {
  position: relative;
  z-index: 1;
}

/* SOBRESCREVER ESTILOS DO QLIK SENSE - MÁXIMA PRIORIDADE */
/* Estes seletores garantem que as fontes Roboto sejam aplicadas mesmo quando o Qlik carrega seus estilos */
body[data-theme="qlik"] .echarts-for-react svg text,
.qlik-embed-object .echarts-for-react svg text,
.qlik-object .echarts-for-react svg text,
.qlik-sense-app .echarts-for-react svg text,
[class*="qlik"] .echarts-for-react svg text {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  font-size: 11px !important;
}

/* Garantir que títulos de gráfico sempre usem Roboto */
.echarts-for-react .echarts-title-text {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  font-weight: 400 !important; /* ✅ NOVO: FontWeight mais suave */
}

/* Garantir que legendas sempre usem Roboto */
.echarts-for-react .echarts-legend-item text {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  font-size: 12px !important;
}

/* Garantir que labels de eixos sempre usem Roboto */
.echarts-for-react .echarts-axis-label text {
  font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
  font-size: 11px !important;
}

/* ✅ NOVO: Scrollbars específicas para gráficos */
.custom-chart-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-chart-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-md);
}

.custom-chart-container::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.custom-chart-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #7c8ef0, #8659b8);
  transform: scale(1.1);
}

/* Estilos para ECharts que podem ter zoom/scroll */
.echarts-for-react::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.echarts-for-react::-webkit-scrollbar-track {
  background: transparent;
}

.echarts-for-react::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.6);
  border-radius: var(--radius-sm);
}

.echarts-for-react::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.8);
}

/* ✅ NOVO: Estilos específicos para diferentes tipos de gráfico */
.chart-item {
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

/* ✅ NOVO: Melhorar o contraste e legibilidade dos gráficos */
.echarts-for-react .echarts-axis-line {
  stroke-opacity: 0.3 !important;
}

.echarts-for-react .echarts-axis-tick {
  stroke-opacity: 0.2 !important;
}

.echarts-for-react .echarts-grid-line {
  stroke-opacity: 0.1 !important;
}

/* ✅ NOVO: Animações suaves para elementos interativos */
.echarts-for-react .echarts-series-item {
  transition: opacity 0.2s ease-in-out !important;
}

.echarts-for-react .echarts-series-item:hover {
  opacity: 0.8 !important;
}

/* ✅ NOVO: Estilos para o botão flutuante dos gráficos */
.chart-action-button {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.chart-action-button.visible {
  opacity: 1;
  transform: scale(1);
}

.chart-action-button.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

/* Estilos específicos para diferentes tamanhos de tela */
@media (max-width: 768px) {
  .chart-action-button {
    top: 4px;
    right: 4px;
  }
}

@media (max-width: 480px) {
  .chart-action-button {
    opacity: 1 !important; /* Sempre visível em telas pequenas */
  }
}

/* Melhorar a visibilidade do botão em diferentes fundos */
.chart-action-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: radial-gradient(circle, rgba(0,0,0,0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-action-button:hover::before {
  opacity: 1;
}

/* Animação suave para o menu */
.chart-options-menu {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ✅ NOVO: Estilos para o botão flutuante dos gráficos */
.chart-action-button {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.chart-action-button.visible {
  opacity: 1;
  transform: scale(1);
}

.chart-action-button.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

/* Estilos específicos para diferentes tamanhos de tela */
@media (max-width: 768px) {
  .chart-action-button {
    top: 4px;
    right: 4px;
  }
}

@media (max-width: 480px) {
  .chart-action-button {
    opacity: 1 !important; /* Sempre visível em telas pequenas */
  }
}

/* Melhorar a visibilidade do botão em diferentes fundos */
.chart-action-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: radial-gradient(circle, rgba(0,0,0,0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-action-button:hover::before {
  opacity: 1;
}

/* Animação suave para o menu */
.chart-options-menu {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ✅ NOVO: Estilos para fullscreen */
.custom-chart-card:fullscreen {
  padding: 20px;
  background: var(--background-color, #ffffff);
  display: flex;
  flex-direction: column;
}

.custom-chart-card:fullscreen .echarts-for-react {
  height: 100% !important;
  width: 100% !important;
  flex: 1;
}

/* Webkit fullscreen (Safari, Chrome) */
.custom-chart-card:-webkit-full-screen {
  padding: 20px;
  background: var(--background-color, #ffffff);
  display: flex;
  flex-direction: column;
}

.custom-chart-card:-webkit-full-screen .echarts-for-react {
  height: 100% !important;
  width: 100% !important;
  flex: 1;
}

/* Mozilla fullscreen (Firefox) */
.custom-chart-card:-moz-full-screen {
  padding: 20px;
  background: var(--background-color, #ffffff);
  display: flex;
  flex-direction: column;
}

.custom-chart-card:-moz-full-screen .echarts-for-react {
  height: 100% !important;
  width: 100% !important;
  flex: 1;
}

/* Microsoft fullscreen (IE/Edge) */
.custom-chart-card:-ms-fullscreen {
  padding: 20px;
  background: var(--background-color, #ffffff);
  display: flex;
  flex-direction: column;
}

.custom-chart-card:-ms-fullscreen .echarts-for-react {
  height: 100% !important;
  width: 100% !important;
  flex: 1;
}

/* Estilos para o botão de ação em fullscreen */
.custom-chart-card:fullscreen .chart-action-button,
.custom-chart-card:-webkit-full-screen .chart-action-button,
.custom-chart-card:-moz-full-screen .chart-action-button,
.custom-chart-card:-ms-fullscreen .chart-action-button {
  top: 30px;
  right: 30px;
  opacity: 1 !important;
  transform: scale(1.2);
}

/* Melhorar contraste do botão em fullscreen */
.custom-chart-card:fullscreen .chart-action-button .MuiIconButton-root,
.custom-chart-card:-webkit-full-screen .chart-action-button .MuiIconButton-root,
.custom-chart-card:-moz-full-screen .chart-action-button .MuiIconButton-root,
.custom-chart-card:-ms-fullscreen .chart-action-button .MuiIconButton-root {
  background-color: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
} 