// Configuração base da API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3031/api';

// Função auxiliar para fazer requisições
const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || data.message || 'Erro na requisição');
    }

    return data;
  } catch (error) {
    console.error(`Erro na API (${endpoint}):`, error);
    throw error;
  }
};

// Serviços de Empresa
export const empresaService = {
  // Listar todas as empresas
  async listarEmpresas(incluirObjetos = true) {
    return apiRequest(`/empresas?incluirObjetos=${incluirObjetos}`);
  },

  // Obter empresa específica
  async obterEmpresa(empresaId, incluirObjetos = true) {
    return apiRequest(`/empresas/${empresaId}?incluirObjetos=${incluirObjetos}`);
  },

  // Obter configuração completa para frontend
  async obterConfigEmpresa(empresaId) {
    return apiRequest(`/empresas/${empresaId}/config`);
  },

  // Criar nova empresa
  async criarEmpresa(empresaData) {
    return apiRequest('/empresas', {
      method: 'POST',
      body: JSON.stringify(empresaData),
    });
  },

  // Atualizar empresa
  async atualizarEmpresa(empresaId, empresaData) {
    return apiRequest(`/empresas/${empresaId}`, {
      method: 'PUT',
      body: JSON.stringify(empresaData),
    });
  },

  // Excluir empresa
  async excluirEmpresa(empresaId, forceDelete = false) {
    return apiRequest(`/empresas/${empresaId}?forceDelete=${forceDelete}`, {
      method: 'DELETE',
    });
  },

  // Reativar empresa
  async reativarEmpresa(empresaId) {
    return apiRequest(`/empresas/${empresaId}/reativar`, {
      method: 'PATCH',
    });
  },

  // ✅ NOVO: Obter objetos da empresa com filtros
  async obterObjetosEmpresa(empresaId, filtros = {}) {
    const params = new URLSearchParams();
    
    if (filtros.tipo) params.append('tipo', filtros.tipo);
    if (filtros.categoria) params.append('categoria', filtros.categoria);
    if (filtros.ativo !== undefined) params.append('ativo', filtros.ativo);

    const queryString = params.toString();
    const endpoint = `/empresas/${empresaId}/objetos${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest(endpoint);
  },
};

// Serviços de App
export const appService = {
  // Listar apps por empresa
  async listarAppsPorEmpresa(empresaId, incluirObjetos = true) {
    return apiRequest(`/apps/${empresaId}?incluirObjetos=${incluirObjetos}`);
  },

  // Obter app específico
  async obterApp(empresaId, appId, incluirObjetos = true) {
    return apiRequest(`/apps/${empresaId}/${appId}?incluirObjetos=${incluirObjetos}`);
  },

  // Criar novo app
  async criarApp(empresaId, appData) {
    return apiRequest(`/apps/${empresaId}`, {
      method: 'POST',
      body: JSON.stringify(appData),
    });
  },

  // Atualizar app
  async atualizarApp(empresaId, appId, appData) {
    return apiRequest(`/apps/${empresaId}/${appId}`, {
      method: 'PUT',
      body: JSON.stringify(appData),
    });
  },

  // Excluir app
  async excluirApp(empresaId, appId, forceDelete = false) {
    return apiRequest(`/apps/${empresaId}/${appId}?forceDelete=${forceDelete}`, {
      method: 'DELETE',
    });
  },

  // Reordenar apps
  async reordenarApps(empresaId, apps) {
    return apiRequest(`/apps/${empresaId}/reordenar`, {
      method: 'PATCH',
      body: JSON.stringify({ apps }),
    });
  },

  // Reativar app
  async reativarApp(empresaId, appId) {
    return apiRequest(`/apps/${empresaId}/${appId}/reativar`, {
      method: 'PATCH',
    });
  },
};

// Serviços de Página
export const paginaService = {
  // Listar páginas por empresa
  async listarPaginas(empresaId, incluirInativas = false) {
    return apiRequest(`/empresas/${empresaId}/paginas?incluirInativas=${incluirInativas}`);
  },

  // Obter página específica
  async obterPagina(empresaId, paginaId) {
    return apiRequest(`/empresas/${empresaId}/paginas/${paginaId}`);
  },

  // Criar nova página
  async criarPagina(empresaId, paginaData) {
    return apiRequest(`/empresas/${empresaId}/paginas`, {
      method: 'POST',
      body: JSON.stringify(paginaData),
    });
  },

  // Atualizar página
  async atualizarPagina(empresaId, paginaId, paginaData) {
    return apiRequest(`/empresas/${empresaId}/paginas/${paginaId}`, {
      method: 'PUT',
      body: JSON.stringify(paginaData),
    });
  },

  // Excluir página
  async excluirPagina(empresaId, paginaId, forceDelete = false) {
    return apiRequest(`/empresas/${empresaId}/paginas/${paginaId}?forceDelete=${forceDelete}`, {
      method: 'DELETE',
    });
  },

  // Reativar página
  async reativarPagina(empresaId, paginaId) {
    return apiRequest(`/empresas/${empresaId}/paginas/${paginaId}/reativar`, {
      method: 'PATCH',
    });
  },

  // Reordenar páginas
  async reordenarPaginas(empresaId, paginas) {
    return apiRequest(`/empresas/${empresaId}/paginas/reordenar`, {
      method: 'POST',
      body: JSON.stringify({ paginas }),
    });
  },

  // Criar páginas padrão
  async criarPaginasPadrao(empresaId) {
    return apiRequest(`/empresas/${empresaId}/paginas/criar-padrao`, {
      method: 'POST',
    });
  },
};

// Serviços de Objeto
export const objetoService = {
  // Listar objetos por app
  async listarObjetosPorApp(empresaId, appId, filtros = {}) {
    const params = new URLSearchParams();
    
    if (filtros.categoria) params.append('categoria', filtros.categoria);
    if (filtros.tipo) params.append('tipo', filtros.tipo);
    if (filtros.ativo !== undefined) params.append('ativo', filtros.ativo);

    const queryString = params.toString();
    const endpoint = `/objetos/${empresaId}/${appId}${queryString ? `?${queryString}` : ''}`;
    
    return apiRequest(endpoint);
  },

  // Obter objetos por categoria
  async obterObjetosPorCategoria(empresaId, appId, categoria) {
    return apiRequest(`/objetos/${empresaId}/${appId}/categoria/${categoria}`);
  },

  // Obter objeto específico
  async obterObjeto(empresaId, appId, objetoId) {
    return apiRequest(`/objetos/${empresaId}/${appId}/${objetoId}`);
  },

  // Criar novo objeto
  async criarObjeto(empresaId, appId, objetoData) {
    return apiRequest(`/objetos/${empresaId}/${appId}`, {
      method: 'POST',
      body: JSON.stringify(objetoData),
    });
  },

  // Criar múltiplos objetos
  async criarMultiplosObjetos(empresaId, appId, objetos) {
    return apiRequest(`/objetos/${empresaId}/${appId}/multiplos`, {
      method: 'POST',
      body: JSON.stringify({ objetos }),
    });
  },

  // Atualizar objeto
  async atualizarObjeto(empresaId, appId, objetoId, objetoData) {
    return apiRequest(`/objetos/${empresaId}/${appId}/${objetoId}`, {
      method: 'PUT',
      body: JSON.stringify(objetoData),
    });
  },

  // Reordenar objetos
  async reordenarObjetos(empresaId, appId, objetos) {
    return apiRequest(`/objetos/${empresaId}/${appId}/reordenar`, {
      method: 'PATCH',
      body: JSON.stringify({ objetos }),
    });
  },

  // Excluir objeto
  async excluirObjeto(empresaId, appId, objetoId, forceDelete = false) {
    return apiRequest(`/objetos/${empresaId}/${appId}/${objetoId}?forceDelete=${forceDelete}`, {
      method: 'DELETE',
    });
  },
};

// Serviços de Configuração do Mashup
export const configMashupService = {
  // Obter configuração ativa
  async obterConfigAtiva() {
    return apiRequest('/config');
  },

  // Atualizar configuração
  async atualizarConfig(configData, configId = 'default') {
    return apiRequest(`/config/${configId}`, {
      method: 'PUT',
      body: JSON.stringify(configData),
    });
  },

  // Validar configuração
  async validarConfig(configData) {
    return apiRequest('/config/validar', {
      method: 'POST',
      body: JSON.stringify(configData),
    });
  },

  // Resetar configuração
  async resetarConfig(configId = 'default') {
    return apiRequest(`/config/${configId}/reset`, {
      method: 'POST',
    });
  },
};

// Serviços de Health Check
export const healthService = {
  // Health check básico
  async healthCheck() {
    return apiRequest('/health');
  },

  // Health check detalhado
  async healthCheckDetailed() {
    return apiRequest('/health/detailed');
  },

  // Status do banco de dados
  async databaseStatus() {
    return apiRequest('/health/database');
  },
};

// Função para verificar se a API está disponível
export const checkApiStatus = async () => {
  try {
    const response = await healthService.healthCheck();
    return {
      available: true,
      status: response.status,
      message: 'API disponível',
    };
  } catch (error) {
    return {
      available: false,
      status: 'offline',
      message: error.message || 'API não disponível',
    };
  }
};

// Tipos de objetos Qlik disponíveis
export const TIPOS_OBJETO = [
  { value: 'kpi', label: 'KPI', icon: '📊' },
  { value: 'multiKpi', label: 'Multi KPI', icon: '📊' },
  { value: 'bar', label: 'Gráfico de Barras', icon: '📊' },
  { value: 'line', label: 'Gráfico de Linhas', icon: '📈' },
  { value: 'area', label: 'Gráfico de Área', icon: '🏔️' },
  { value: 'pie', label: 'Gráfico de Pizza', icon: '🥧' },
  { value: 'donut', label: 'Gráfico de Rosquinha', icon: '🍩' },
  { value: 'column', label: 'Gráfico de Colunas', icon: '📊' },
  { value: 'scatter', label: 'Gráfico de Dispersão', icon: '⚪' },
  { value: 'mixed', label: 'Gráfico Misto (Barras + Linhas)', icon: '📊📈' },
  { value: 'table', label: 'Tabela', icon: '📋' },
  { value: 'filter', label: 'Filtro', icon: '🔍' },
  { value: 'gauge', label: 'Medidor', icon: '⌚' }
];

// Formatos de KPI disponíveis
export const FORMATOS_KPI = [
  { value: 'numero', label: 'Número' },
  { value: 'moeda', label: 'Moeda (R$)' },
  { value: 'porcentagem', label: 'Porcentagem (%)' }
];

// Tipos de gráfico para ECharts
export const TIPOS_GRAFICO_ECHARTS = [
  { value: 'bar', label: 'Barras Verticais', icon: '📊' },
  { value: 'column', label: 'Barras Horizontais', icon: '📊' },
  { value: 'line', label: 'Linha', icon: '📈' },
  { value: 'area', label: 'Área', icon: '🏔️' },
  { value: 'pie', label: 'Pizza', icon: '🥧' },
  { value: 'donut', label: 'Rosquinha', icon: '🍩' },
  { value: 'scatter', label: 'Dispersão', icon: '⚪' },
  { value: 'mixed', label: 'Misto (Barras + Linhas)', icon: '📊📈' }
];

// Categorias de app disponíveis
export const CATEGORIAS_APP = [
  { value: 'dashboard', label: 'Dashboard', icon: '📊' },
  { value: 'vendas', label: 'Vendas', icon: '💰' },
  { value: 'financeiro', label: 'Financeiro', icon: '💳' },
  { value: 'helpdesk', label: 'Helpdesk', icon: '🎧' },
  { value: 'rh', label: 'Recursos Humanos', icon: '👥' },
  { value: 'operacional', label: 'Operacional', icon: '⚙️' },
  { value: 'outros', label: 'Outros', icon: '📁' }
];

// Função para converter dados do frontend para o formato do backend (empresa)
export const convertToBackendFormat = (empresaData) => {
  const backendData = {
    id: empresaData.id,
    nome: empresaData.nome,
    cor: empresaData.cor,
    logo: empresaData.logo,
    ativo: empresaData.ativo !== undefined ? empresaData.ativo : true,
  };

  return backendData;
};

// Função para converter dados do backend para o formato do frontend (empresa)
export const convertToFrontendFormat = (empresaData) => {
  return {
    id: empresaData.id,
    nome: empresaData.nome,
    cor: empresaData.cor,
    logo: empresaData.logo,
    ativo: empresaData.ativo,
    apps: empresaData.apps || [],
    objetos: empresaData.objetos || {},
  };
};

// Função para criar objetos a partir dos dados do frontend (compatibilidade)
export const createObjectsFromFrontendData = async (empresaId, appId, objetosData) => {
  const objetos = [];
  
  // Converter objetos do formato frontend para backend
  Object.entries(objetosData).forEach(([chave, objectId]) => {
    if (objectId && objectId.trim()) {
      // Determinar tipo baseado na chave
      let tipo = 'other';
      
      if (chave.includes('kpi')) {
        tipo = 'kpi';
      } else if (chave.includes('bar') || chave.includes('Bar')) {
        tipo = 'bar';
      } else if (chave.includes('line') || chave.includes('Line')) {
        tipo = 'line';
      } else if (chave.includes('area') || chave.includes('Area')) {
        tipo = 'area';
      } else if (chave.includes('pie') || chave.includes('Pie')) {
        tipo = 'pie';
      } else if (chave.includes('donut') || chave.includes('Donut')) {
        tipo = 'donut';
      } else if (chave.includes('column') || chave.includes('Column')) {
        tipo = 'column';
      } else if (chave.includes('scatter') || chave.includes('Scatter')) {
        tipo = 'scatter';
      } else if (chave.includes('gauge') || chave.includes('Gauge')) {
        tipo = 'gauge';
      } else if (chave.includes('table')) {
        tipo = 'table';
      }

      objetos.push({
        chave,
        objectId: objectId.trim(),
        nome: `${chave.charAt(0).toUpperCase() + chave.slice(1)} - ${empresaId}`,
        tipo,
        categoria: 'geral',
        descricao: `Objeto ${chave} da empresa ${empresaId}`,
      });
    }
  });

  // Criar objetos no backend se houver algum
  if (objetos.length > 0) {
    try {
      return await objetoService.criarMultiplosObjetos(empresaId, appId, objetos);
    } catch (error) {
      console.error('Erro ao criar objetos:', error);
      throw error;
    }
  }

  return { success: true, data: { objetosCriados: [], totalCriados: 0 } };
};

export default {
  empresaService,
  appService,
  objetoService,
  configMashupService,
  healthService,
  checkApiStatus,
  convertToBackendFormat,
  convertToFrontendFormat,
  createObjectsFromFrontendData,
  TIPOS_OBJETO,
  CATEGORIAS_APP,
  FORMATOS_KPI,
  TIPOS_GRAFICO_ECHARTS,
}; 