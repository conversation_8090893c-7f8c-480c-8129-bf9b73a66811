const express = require('express');
const router = express.Router();
const {
  listarAppsPorEmpresa,
  obterApp,
  criarApp,
  atualizarApp,
  excluirApp,
  reordenarApps,
  reativarApp
} = require('../controllers/appController');

// Middleware para verificar conexão com banco
const checkDatabase = require('../middleware/checkDatabase');

// Aplicar middleware de verificação do banco em todas as rotas
router.use(checkDatabase);

// GET /api/apps/:empresaId - Listar apps por empresa
router.get('/:empresaId', listarAppsPorEmpresa);

// GET /api/apps/:empresaId/:appId - Obter app específico
router.get('/:empresaId/:appId', obterApp);

// POST /api/apps/:empresaId - Criar novo app
router.post('/:empresaId', criarApp);

// PUT /api/apps/:empresaId/:appId - Atualizar app
router.put('/:empresaId/:appId', atualizarApp);

// DELETE /api/apps/:empresaId/:appId - Excluir app
router.delete('/:empresaId/:appId', excluirApp);

// PATCH /api/apps/:empresaId/reordenar - Reordenar apps
router.patch('/:empresaId/reordenar', reordenarApps);

// PATCH /api/apps/:empresaId/:appId/reativar - Reativar app
router.patch('/:empresaId/:appId/reativar', reativarApp);

module.exports = router; 