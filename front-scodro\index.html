<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Mashup React moderno para Qlik Sense - compatível com Cloud e Enterprise" />
    <meta name="keywords" content="qlik, sense, mashup, react, dashboard, analytics" />
    <meta name="author" content="Seu Nome" />
    
    <!-- Roboto Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet" />
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    
    <title>Qlik Sense Mashup React</title>
    
    <style>
      /* Reset básico */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Roboto', sans-serif;
        background-color: #f5f5f5;
      }
      
      /* Loading inicial */
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: 'Roboto', sans-serif;
      }
      
      .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Loading inicial -->
    <div id="loading">
      <div style="text-align: center;">
        <div class="spinner"></div>
        <h2>Carregando Qlik Mashup...</h2>
        <p style="opacity: 0.8; margin-top: 10px;">Inicializando aplicação React</p>
      </div>
    </div>
    
    <!-- Container principal da aplicação -->
    <div id="root"></div>
    
    <!-- Script para remover loading quando React carregar -->
    <script>
      // Remover loading após um tempo máximo
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.opacity = '0';
          setTimeout(() => loading.remove(), 300);
        }
      }, 3000);
      
      // Remover loading quando React estiver pronto
      window.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.opacity = '0';
            setTimeout(() => loading.remove(), 300);
          }
        }, 1000);
      });
    </script>
    
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html> 