const express = require('express');
const router = express.Router();
const Objeto = require('../models/Objeto');
const {
  listarObjetosPorApp,
  obterObjeto,
  criarObjeto,
  criarMultiplosObjetos,
  atualizarObjeto,
  reordenarObjetos,
  excluirObjeto,
  obterObjetosPorCategoria
} = require('../controllers/objetoController');

// Middleware para verificar conexão com banco
const checkDatabase = require('../middleware/checkDatabase');

// Aplicar middleware de verificação do banco em todas as rotas
router.use(checkDatabase);

// GET /api/objetos/:empresaId/:appId - Listar objetos por app
router.get('/:empresaId/:appId', listarObjetosPorApp);

// GET /api/objetos/:empresaId/:appId/categoria/:categoria - Obter objetos por categoria
router.get('/:empresaId/:appId/categoria/:categoria', obterObjetosPorCategoria);

// GET /api/objetos/:empresaId/:appId/:objetoId - Obter objeto específico
router.get('/:empresaId/:appId/:objetoId', obterObjeto);

// POST /api/objetos/:empresaId/:appId - Criar novo objeto
router.post('/:empresaId/:appId', criarObjeto);

// POST /api/objetos/:empresaId/:appId/multiplos - Criar múltiplos objetos
router.post('/:empresaId/:appId/multiplos', criarMultiplosObjetos);

// PUT /api/objetos/:empresaId/:appId/:objetoId - Atualizar objeto
router.put('/:empresaId/:appId/:objetoId', atualizarObjeto);

// PATCH /api/objetos/:empresaId/:appId/reordenar - Reordenar objetos
router.patch('/:empresaId/:appId/reordenar', reordenarObjetos);

// DELETE /api/objetos/:empresaId/:appId/:objetoId - Excluir objeto
router.delete('/:empresaId/:appId/:objetoId', excluirObjeto);

// ✅ NOVA ROTA: Corrigir configurações de objetos mixed
router.post('/fix-mixed-configs', async (req, res) => {
  try {
    console.log('🔍 Procurando objetos do tipo "mixed"...')
    
    // Buscar todos os objetos do tipo mixed
    const objetosMixed = await Objeto.find({ tipo: 'mixed' })
    
    console.log(`📊 Encontrados ${objetosMixed.length} objetos mixed`)
    
    const resultados = []
    
    for (const objeto of objetosMixed) {
      console.log(`\n🔧 Corrigindo objeto: ${objeto.nome} (ID: ${objeto._id})`)
      
      // Garantir que a configuração chartConfig existe
      if (!objeto.configuracao) {
        objeto.configuracao = {}
      }
      
      if (!objeto.configuracao.chartConfig) {
        objeto.configuracao.chartConfig = {}
      }
      
      // Corrigir tipoGrafico
      objeto.configuracao.chartConfig.tipoGrafico = 'mixed'
      
      // Corrigir mixedConfig baseado no nome das medidas (se disponível)
      if (!objeto.configuracao.chartConfig.mixedConfig) {
        objeto.configuracao.chartConfig.mixedConfig = {
          barSeries: [0, 1], // Primeiras duas séries como barras (valores)
          lineSeries: [2, 3] // Últimas duas séries como linhas (porcentagens)
        }
      }
      
      // Garantir dataZoom
      if (!objeto.configuracao.chartConfig.dataZoom) {
        objeto.configuracao.chartConfig.dataZoom = {
          enabled: false,
          type: 'slider',
          start: 0,
          end: 100,
          showDetail: true,
          realtime: true
        }
      }
      
      // Configurações padrão para mixed
      objeto.configuracao.chartConfig.showLegend = true
      objeto.configuracao.chartConfig.showTitle = true
      objeto.configuracao.chartConfig.showAxes = true
      objeto.configuracao.chartConfig.animacao = true
      
      const configCorrigida = {
        tipoGrafico: objeto.configuracao.chartConfig.tipoGrafico,
        mixedConfig: objeto.configuracao.chartConfig.mixedConfig,
        dataZoom: objeto.configuracao.chartConfig.dataZoom
      }
      
      console.log(`📝 Configuração corrigida:`, configCorrigida)
      
      // Salvar as mudanças
      await objeto.save()
      
      resultados.push({
        id: objeto._id,
        nome: objeto.nome,
        configCorrigida
      })
      
      console.log(`✅ Objeto "${objeto.nome}" corrigido com sucesso!`)
    }
    
    res.json({
      success: true,
      message: `Correção concluída! ${objetosMixed.length} objetos processados.`,
      data: resultados
    })
    
  } catch (error) {
    console.error('❌ Erro ao corrigir configurações:', error)
    res.status(500).json({
      success: false,
      error: 'Erro ao corrigir configurações mixed',
      message: error.message
    })
  }
})

module.exports = router; 