import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './styles/scrollbar.css'
import './styles/shadows.css'

// CSS de fontes removidos para melhor performance dos inputs

// Verificar se require.js está disponível globalmente (necessário para Qlik)
if (typeof window !== 'undefined' && !window.require) {
  // Em desenvolvimento, criar um mock básico do require
  if (process.env.NODE_ENV === 'development') {
    window.require = {
      config: (config) => {
        console.log('🔧 Mock require.config:', config)
      }
    }
  }
}

// Remover loading ao carregar o React
setTimeout(() => {
  const loading = document.getElementById('loading')
  if (loading) {
    loading.style.opacity = '0'
    setTimeout(() => loading.remove(), 300)
  }
}, 100)

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
) 