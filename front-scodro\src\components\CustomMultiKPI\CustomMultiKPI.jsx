import React, { useState, useRef, useEffect, useMemo } from 'react'
import { Box, Typography, Grid, Card, CardContent, useTheme } from '@mui/material'
import {
  AttachMoney,
  Person,
  ShoppingCart,
  Timeline,
  Assessment,
  Star,
  Favorite,
  Business,
  LocalAtm,
  AccountBalance,
  Analytics,
  TrendingUp,
  TrendingDown,
  People,
  Work,
  Assignment
} from '@mui/icons-material'

// Ícones disponíveis para Multi KPI
const MULTI_KPI_ICONS = {
  money: AttachMoney,
  person: Person,
  people: People,
  cart: ShoppingCart,
  timeline: Timeline,
  assessment: Assessment,
  star: Star,
  heart: Favorite,
  business: Business,
  atm: LocalAtm,
  bank: AccountBalance,
  analytics: Analytics,
  trending_up: TrendingUp,
  trending_down: TrendingDown,
  work: Work,
  assignment: Assignment
}

const CustomMultiKPI = ({ data, config = {}, objectId }) => {
  const theme = useTheme()
  const containerRef = useRef(null)
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })
  
  // Configurações padrão para Multi KPI
  const defaultConfig = {
    showTitle: true,
    showIcon: true,
    layout: 'unified', // 'grid', 'horizontal' ou 'unified' (novo)
    itemsPerRow: 3,
    spacing: 2,
    backgroundColor: theme.palette.primary.main,
    borderRadius: 2,
    elevation: 1,
    unifiedBackground: true // Nova opção para fundo único
  }
  
  const finalConfig = { ...defaultConfig, ...config.multiKpiConfig, ...config }

  // Detectar tamanho do container para redimensionamento automático
  useEffect(() => {
    if (containerRef.current) {
      let resizeTimer
      
      const updateSize = () => {
        const rect = containerRef.current.getBoundingClientRect()
        
        // Só atualizar se o tamanho mudou significativamente
        const newWidth = Math.round(rect.width)
        const newHeight = Math.round(rect.height)
        
        if (Math.abs(newWidth - containerSize.width) > 5 || 
            Math.abs(newHeight - containerSize.height) > 5) {
          
          // console.log(`📏 CustomMultiKPI tamanho atualizado:`, {
          //   objectId: objectId || 'simulado',
          //   width: newWidth,
          //   height: newHeight,
          //   anterior: containerSize,
          //   numKPIs: data?.kpis?.length || 0
          // })
          
          setContainerSize({ width: newWidth, height: newHeight })
        }
      }
      
      const handleResize = () => {
        // Usar debounce para evitar muitas atualizações
        clearTimeout(resizeTimer)
        resizeTimer = setTimeout(updateSize, 50) // Reduzir delay para resposta mais rápida
      }
      
      // Detectar tamanho inicial
      updateSize()
      
      // Detectar redimensionamento da janela
      window.addEventListener('resize', handleResize)
      
      // Adicionar MutationObserver para detectar mudanças no DOM
      const observer = new MutationObserver(() => {
        handleResize()
      })
      
      observer.observe(containerRef.current, {
        attributes: true,
        attributeFilter: ['style'],
        subtree: true
      })
      
      return () => {
        window.removeEventListener('resize', handleResize)
        observer.disconnect()
        clearTimeout(resizeTimer)
      }
    }
  }, [objectId, containerSize.width, containerSize.height, data?.kpis?.length])

  // Funções para calcular tamanhos responsivos - OTIMIZADAS COM USEMEMO

  // ✅ NOVO: Calcular tamanho do ícone baseado no container
  const iconContainerSize = useMemo(() => {
    const { width, height } = containerSize
    if (width === 0 || height === 0) return 80
    
    const minDimension = Math.min(width, height)
    let size = Math.min(minDimension * 0.15, 80) // Máximo 80px
    size = Math.max(size, 40) // Mínimo 40px
    
    return Math.round(size)
  }, [containerSize.width, containerSize.height])

  // ✅ NOVO: Calcular tamanho do ícone SVG baseado no container
  const iconSvgSize = useMemo(() => {
    const calculatedSize = Math.round(iconContainerSize * 0.45) // 45% do container do ícone
    return Math.max(calculatedSize, 30) // ✅ NOVO: Tamanho mínimo de 30px
  }, [iconContainerSize])

  // ✅ NOVO: Calcular margem esquerda baseada no tamanho do ícone
  const contentMarginLeft = useMemo(() => {
    return iconContainerSize + 30 // Tamanho do ícone + 30px de margem
  }, [iconContainerSize])

  // ✅ NOVO: Calcular posição esquerda do ícone baseada no container
  const iconLeftPosition = useMemo(() => {
    const { width } = containerSize
    if (width === 0) return 16
    
    // Para containers pequenos, reduzir a margem
    if (width < 300) return 8
    if (width < 400) return 12
    return 16
  }, [containerSize.width])

  // Calcular tamanho da fonte principal (valores dos KPIs) - OTIMIZADO
  const mainValueFontSize = useMemo(() => {
    const { width } = containerSize
    const numKpis = data?.kpis?.length || 1
    
    if (width === 0) return '1.5rem'
    
    let fontSize = width < 300 ? '1rem' : 
                   width < 400 ? '1.2rem' : 
                   numKpis >= 3 ? '1.3rem' : '1.5rem'
    
    return fontSize
  }, [containerSize.width, data?.kpis?.length])

  // Calcular tamanho da fonte dos títulos/labels - OTIMIZADO
  const labelFontSize = useMemo(() => {
    const { width } = containerSize
    const numKpis = data?.kpis?.length || 1
    
    if (width === 0) return '0.75rem'
    
    let fontSize = width < 300 ? '0.6rem' : 
                   width < 400 ? '0.65rem' : 
                   numKpis >= 3 ? '0.7rem' : '0.75rem'
    
    return fontSize
  }, [containerSize.width, data?.kpis?.length])

  // ✅ NOVO: Calcular padding do container baseado no tamanho
  const containerPadding = useMemo(() => {
    const { width } = containerSize
    if (width === 0) return 3
    
    if (width < 300) return 1.5
    if (width < 400) return 2
    return 3
  }, [containerSize.width])

  // ✅ NOVO: Calcular gap entre KPIs baseado no tamanho - REDUZIDO E RESPONSIVO
  const kpiGap = useMemo(() => {
    const { width, height } = containerSize
    const numKpis = data?.kpis?.length || 1
    
    if (width === 0 || height === 0) return 0.5
    
    const minDimension = Math.min(width, height)
    
    // Para containers muito pequenos ou muitos KPIs, reduzir ou remover gap
    if (minDimension < 150 || numKpis > 5) return 0 // Sem gap em containers muito pequenos ou 5+ KPIs
    if (minDimension < 200 || numKpis > 4) return 0.15 // Gap mínimo para 4+ KPIs
    if (minDimension < 300 || numKpis > 3) return 0.25 // Gap pequeno para 3+ KPIs
    return 0.5 // Gap normal para poucos KPIs
  }, [containerSize.width, containerSize.height, data?.kpis?.length])

  // Calcular tamanho da fonte do título principal - OTIMIZADO
  const titleFontSize = useMemo(() => {
    const { width, height } = containerSize
    if (width === 0 || height === 0) return '1rem'
    
    const minDimension = Math.min(width, height)
    let fontSize = Math.min(minDimension * 0.07, 18) // Máximo 18px
    fontSize = Math.max(fontSize, 12) // Mínimo 12px
    
    return `${Math.round(fontSize)}px`
  }, [containerSize.width, containerSize.height])

  // ✅ NOVO: Calcular altura dinâmica para KPIs
  const kpiHeight = useMemo(() => {
    const { height } = containerSize
    const numKpis = data?.kpis?.length || 1
    
    if (height === 0) return 'auto'
    
    const titleSpace = finalConfig.showTitle && data?.titulo ? 35 : 0 // Reduzir espaço do título
    const paddingTotal = containerPadding * 16 * 2 // Top + Bottom padding (Convert to px)
    const spacingBetweenKpis = (numKpis - 1) * (kpiGap * 8) // Convert gap to px
    const bottomMargin = 16 // Margem bottom adicional para distanciar da borda
    
    const availableHeight = height - titleSpace - paddingTotal - spacingBetweenKpis - bottomMargin
    const calculatedHeight = availableHeight / numKpis
    
    return Math.max(calculatedHeight, 35) // Altura mínima de 35px
  }, [containerSize.height, data?.kpis?.length, data?.titulo, finalConfig.showTitle, containerPadding, kpiGap])

  // ✅ NOVO: Calcular padding dos KPIs individuais baseado no tamanho - FORMATO "0px 5px"
  const kpiPadding = useMemo(() => {
    const { width, height } = containerSize
    if (width === 0 || height === 0) return '0px 5px'
    
    const minDimension = Math.min(width, height)
    if (minDimension < 200) return '0px 3px'
    if (minDimension < 300) return '0px 4px'
    return '0px 5px'
  }, [containerSize.width, containerSize.height])

  // ✅ NOVO: Função para detectar se é um título informativo
  const isTituloInformativo = (kpi) => {
    const valor = kpi.valor
    const valorFormatado = kpi.valorFormatado
    const titulo = kpi.titulo
    
    // ✅ NOVO: Detectar títulos informativos baseado no valorFormatado
    if (valorFormatado && typeof valorFormatado === 'string') {
      const valorLimpo = valorFormatado.trim()
      
      // Se valorFormatado não contém números E não contém símbolos monetários/percentuais
      const hasNumbers = /\d/.test(valorLimpo)
      const hasMoneySymbols = /[R$%,.]/.test(valorLimpo)
      
      // Se é apenas texto sem números e sem símbolos financeiros, é título informativo
      if (!hasNumbers && !hasMoneySymbols && valorLimpo.length > 0) {
        return true
      }
      
      // ✅ NOVO: Casos específicos detectados nos logs
      if (valorLimpo === 'A Receber' || valorLimpo === 'Recebido') {
        return true
      }
    }
    
    // Se valor é um número válido, é KPI normal
    if (typeof valor === 'number' && !isNaN(valor)) {
      return false
    }
    
    // Se valor é string não numérica, null, undefined ou vazio, é título
    const isTitulo = valor === null || valor === undefined || valor === '' || 
           (typeof valor === 'string' && isNaN(parseFloat(valor)))
    
    return isTitulo
  }

  // ✅ NOVO: Função para obter o título real (pode vir do valorFormatado)
  const obterTituloReal = (kpi) => {
    const valorFormatado = kpi.valorFormatado
    let titulo = kpi.titulo
    
    // Se é título informativo e valorFormatado contém o título real
    if (isTituloInformativo(kpi) && valorFormatado && typeof valorFormatado === 'string') {
      const valorLimpo = valorFormatado.trim()
      
      // ✅ CORRIGIDO: Verificar se valorLimpo não está vazio e não contém apenas números
      if (valorLimpo.length > 0) {
        titulo = valorLimpo;
      }
    }
    
    return titulo
  }

  // ✅ NOVO: Calcular altura específica para títulos informativos
  const tituloHeight = useMemo(() => {
    const { height } = containerSize
    if (height === 0) return 25
    
    // Títulos informativos têm altura fixa menor
    return Math.min(height * 0.12, 30) // Máximo 30px para títulos
  }, [containerSize.height])

  // Validar se temos dados
  if (!data || !data.kpis || data.kpis.length === 0) {
    return (
      <Card 
        ref={containerRef}
        sx={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
      >
        <CardContent>
          <Typography color="text.secondary" align="center">
            Dados Multi KPI não disponíveis
          </Typography>
        </CardContent>
      </Card>
    )
  }

  // Se layout é 'unified', usar o primeiro KPI como base para cor de fundo
  const corFundoUnificado = finalConfig.cor || data.kpis[0]?.cor || theme.palette.primary.main
  const corFonteUnificada = finalConfig.corFonte || data.kpis[0]?.corFonte || '#ffffff'

  // Renderizar cada KPI individual para layout em grid/cards separados
  const renderKPI = (kpi, index) => {
    const IconComponent = MULTI_KPI_ICONS[kpi.icone] || Analytics
    const cor = kpi.cor || theme.palette.primary.main
    const corFonte = kpi.corFonte || '#ffffff'
    
    // ✅ CORRIGIDO: Verificar apenas se o valor é null/undefined/vazio (não zero real)
    const valorOriginal = kpi.valor
    const isValorVazioOuNulo = valorOriginal === null || 
                              valorOriginal === undefined || 
                              valorOriginal === '' ||
                              isNaN(valorOriginal)
    
    // Usar valorFormatado se disponível, senão formatar o valor (ou deixar vazio se for null/undefined)
    const valorExibido = isValorVazioOuNulo ? '' : (kpi.valorFormatado || formatarValor(kpi.valor, 
      kpi.formato || 'numero',
      kpi.prefix || '',
      kpi.suffix || '',
      kpi.casasDecimais || ''
    ))
    
    return (
      <Grid item xs={12} sm={6} md={12/finalConfig.itemsPerRow} key={index}>
        <Card 
          sx={{ 
            height: '100%',
            minHeight: 120,
            background: kpi.gradiente ? 
              `linear-gradient(135deg, ${cor} 0%, ${cor}80 100%)` : cor,
            color: corFonte,
            transition: 'all 0.3s ease',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: `0 8px 25px ${cor}30`
            }
          }}
          elevation={finalConfig.elevation}
        >
          <CardContent sx={{ 
            display: 'flex', 
            flexDirection: 'column', 
            height: '100%',
            position: 'relative',
            p: kpiPadding
          }}>
            {/* Ícone no canto superior direito */}
            {finalConfig.showIcon && (
              <Box sx={{ position: 'absolute', top: 12, right: 12, opacity: 0.7 }}>
                <IconComponent sx={{ fontSize: iconSvgSize, color: corFonte }} />
              </Box>
            )}
            
            {/* Conteúdo principal */}
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              textAlign: 'center'
            }}>
              {/* Título/Label */}
              {finalConfig.showTitle && (
                <Typography 
                  variant="caption"
                  sx={{ 
                    color: corFonte,
                    fontSize: labelFontSize,
                    textTransform: 'uppercase',
                    fontWeight: 600,
                    opacity: 0.8,
                    mb: 0.5,
                    lineHeight: 1.2,
                    // Permitir quebra de texto em vez de cortar
                    wordBreak: 'break-word',
                    hyphens: 'auto',
                    textAlign: 'center',
                    width: '100%'
                  }}
                >
                  {kpi.titulo}
                </Typography>
              )}
              
              {/* Valor Principal */}
              <Typography 
                sx={{ 
                  fontWeight: 'bold',
                  color: corFonte,
                  fontSize: mainValueFontSize,
                  lineHeight: 1,
                  mb: 0.5,
                  wordBreak: 'break-word',
                  textAlign: 'center'
                }}
              >
                {valorExibido}
              </Typography>
              
              {/* Descrição adicional (se houver) */}
              {kpi.descricao && (
                <Typography 
                  variant="caption"
                  sx={{ 
                    color: corFonte,
                    fontSize: `${parseInt(labelFontSize) * 0.8}px`,
                    opacity: 0.7,
                    mt: 0.5
                  }}
                >
                  {kpi.descricao}
                </Typography>
              )}
            </Box>
          </CardContent>
        </Card>
      </Grid>
    )
  }

  // Layout unificado (fundo único)
  if (finalConfig.layout === 'unified' || finalConfig.unifiedBackground) {
    // ✅ NOVO: Obter ícone único para todo o Multi KPI
    const IconComponent = MULTI_KPI_ICONS[finalConfig.icone] || Analytics
    
    return (
      <Card 
        ref={containerRef}
        sx={{ 
          height: '100%', 
          width: '100%',
          minHeight: 200, // ✅ IGUAL AO PREVIEW
          background: `linear-gradient(135deg, ${corFundoUnificado} 0%, ${corFundoUnificado}80 100%)`,
          border: `2px solid ${corFundoUnificado}`,
          borderRadius: 3,
          color: corFonteUnificada,
          overflow: 'hidden',
          display: 'flex',
          position: 'relative', // ✅ NOVO: Para permitir position absolute do ícone
          transition: 'all 0.3s ease',
          '&:hover': {
            border: `2px solid ${corFundoUnificado}70`,
            boxShadow: `0 8px 25px ${corFundoUnificado}20`
          }
        }}
        elevation={finalConfig.elevation}
      >
        <CardContent sx={{ 
          height: '100%',
          width: '100%',
          p: containerPadding, // ✅ NOVO: Padding responsivo baseado no tamanho
          display: 'flex',
          flexDirection: 'column', // ✅ VOLTA PARA COLUNA (sem layout horizontal)
          position: 'relative', // ✅ NOVO: Para o ícone absolute
          '&:last-child': { pb: `${containerPadding * 8}px !important` } // ✅ NOVO: Padding bottom responsivo
        }}>
          {/* ✅ NOVO: Ícone com position absolute */}
          {finalConfig.showIcon === true && (
            <Box 
              sx={{ 
                position: 'absolute',
                top: '50%', // ✅ NOVO: 50% da altura
                left: `${iconLeftPosition}px`, // ✅ NOVO: Posição ajustável em pixels
                transform: 'translateY(-50%)', // ✅ NOVO: Centralizar verticalmente
                zIndex: 1 // ✅ NOVO: Ficar por cima
              }}
            >
              <Box 
                sx={{ 
                  width: iconContainerSize, // ✅ RESPONSIVO
                  height: iconContainerSize, // ✅ RESPONSIVO
                  backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  borderRadius: 2, // ✅ IGUAL AO PREVIEW
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                }}
              >
                <IconComponent 
                  sx={{ 
                    fontSize: iconSvgSize, // ✅ RESPONSIVO
                    color: corFonteUnificada 
                  }} 
                />
              </Box>
            </Box>
          )}
          
          {/* ✅ NOVO: Conteúdo com margin-left para espaço do ícone */}
          <Box sx={{ 
            marginLeft: finalConfig.showIcon === true ? `${contentMarginLeft}px` : '0px', // ✅ NOVO: Margem responsiva em pixels
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Título Principal (opcional) */}
            {finalConfig.showTitle && data.titulo && (
              <Typography 
                variant="h6" 
                sx={{ 
                  color: corFonteUnificada,
                  mb: kpiGap, // ✅ NOVO: Margem responsiva
                  fontWeight: 600,
                  textAlign: 'left',
                  fontSize: titleFontSize // ✅ NOVO: Tamanho responsivo
                }}
              >
                {data.titulo}
              </Typography>
            )}
            
            {/* ✅ Container dos KPIs - VOLTA PARA VERTICAL */}
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              gap: kpiGap, // ✅ NOVO: Gap responsivo baseado no tamanho do container
              flex: 1, // ✅ NOVO: Ocupa todo espaço disponível
              mr: 1.25, // ✅ NOVO: Margem right para distanciar da borda (10px)
              minHeight: 0 // ✅ NOVO: Permite que flex funcione corretamente
            }}>
              {data.kpis.map((kpi, index) => {
                // ✅ NOVO: Detectar se é título informativo
                const isTitulo = isTituloInformativo(kpi)
                const tituloReal = obterTituloReal(kpi) // ✅ NOVO: Obter título real
                
                // ✅ CORRIGIDO: Verificar apenas se o valor é null/undefined/vazio (não zero real)
                const valorOriginal = kpi.valor
                const isValorVazioOuNulo = valorOriginal === null || 
                                          valorOriginal === undefined || 
                                          valorOriginal === '' ||
                                          isNaN(valorOriginal)
                
                // ✅ NOVO: Para títulos informativos, não exibir valores
                const valorExibido = isTitulo ? '' : (isValorVazioOuNulo ? '' : (kpi.valorFormatado || formatarValor(kpi.valor, 
                  kpi.formato || 'numero',
                  kpi.prefix || '',
                  kpi.suffix || '',
                  kpi.casasDecimais || ''
                )))
                
                return (
                  <Box 
                    key={index}
                    sx={{ 
                      backgroundColor: isTitulo ? 'rgba(255, 255, 255, 0.05)' : 'rgba(255, 255, 255, 0.1)', // ✅ NOVO: Cor mais sutil para títulos
                      borderRadius: 2,
                      p: kpiPadding, // ✅ NOVO: Padding responsivo em formato "0px 5px"
                      border: isTitulo ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(255, 255, 255, 0.2)', // ✅ NOVO: Borda mais sutil para títulos
                      textAlign: 'left',
                      height: isTitulo ? tituloHeight : undefined, // ✅ NOVO: Altura fixa menor para títulos
                      flex: isTitulo ? 0 : 1, // ✅ NOVO: Títulos não expandem, KPIs normais sim
                      minHeight: isTitulo ? tituloHeight : 35, // ✅ NOVO: Altura mínima diferente
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center' // ✅ NOVO: Centralizar conteúdo verticalmente
                    }}
                  >
                    <Typography 
                      variant="caption"
                      sx={{ 
                        display: 'block',
                        color: corFonteUnificada,
                        fontSize: isTitulo ? `${Math.max(parseFloat(labelFontSize) * 1.2, 0.8)}rem` : labelFontSize, // ✅ CORRIGIDO: usar parseFloat e manter em rem
                        textTransform: 'uppercase',
                        fontWeight: isTitulo ? 700 : 600, // ✅ NOVO: Mais bold para títulos
                        opacity: isTitulo ? 0.95 : 0.8, // ✅ NOVO: Mais opaco para títulos
                        textAlign: isTitulo ? 'center' : 'left', // ✅ NOVO: Centralizar títulos
                        letterSpacing: isTitulo ? '0.5px' : 'normal' // ✅ NOVO: Espaçamento entre letras para títulos
                      }}
                    >
                      {tituloReal}
                    </Typography>
                    
                    {/* ✅ NOVO: Só exibir valor se não for título informativo */}
                    {!isTitulo && (
                      <Typography 
                        variant="h6" 
                        sx={{ 
                          fontWeight: 'bold',
                          color: corFonteUnificada,
                          fontSize: mainValueFontSize,
                          lineHeight: 1.2
                        }}
                      >
                        {valorExibido}
                      </Typography>
                    )}
                  </Box>
                )
              })}
            </Box>
          </Box>
        </CardContent>
      </Card>
    )
  }
  
  // Layout em grid (original)
  return (
    <Box 
      ref={containerRef}
      sx={{ 
        height: '100%', 
        width: '100%',
        p: 1
      }}
    >
      {/* Título Principal (opcional) */}
      {finalConfig.showTitle && data.titulo && (
        <Typography 
          variant="h6" 
          sx={{ 
            mb: 2, 
            fontWeight: 'bold',
            color: theme.palette.text.primary,
            fontSize: titleFontSize
          }}
        >
          {data.titulo}
        </Typography>
      )}
      
      {/* Grid de KPIs */}
      <Grid container spacing={finalConfig.spacing} sx={{ height: '100%' }}>
        {data.kpis.map((kpi, index) => renderKPI(kpi, index))}
      </Grid>
    </Box>
  )
}

// Função auxiliar para formatar valores
const formatarValor = (valor, formato = 'numero', prefix = '', suffix = '', casasDecimais = 0) => {
  // ✅ CORRIGIDO: Não exibir apenas valores null, undefined, NaN ou string vazia (zero real deve aparecer)
  if (valor === null || valor === undefined || valor === '' || isNaN(valor)) {
    return ''
  }
  
  const num = parseFloat(valor)
  
  // ✅ REMOVIDO: Verificação que impedia exibição de zero real
  // Zero é um valor válido e deve ser exibido
  
  switch (formato) {
    case 'moeda':
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
        minimumFractionDigits: casasDecimais,
        maximumFractionDigits: casasDecimais
      }).format(num)
      
    case 'porcentagem':
      return new Intl.NumberFormat('pt-BR', {
        style: 'percent',
        minimumFractionDigits: casasDecimais,
        maximumFractionDigits: casasDecimais
      }).format(num / 100)
      
    case 'numero':
    default:
      const formatted = new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: casasDecimais,
        maximumFractionDigits: casasDecimais
      }).format(num)
      
      return `${prefix}${formatted}${suffix}`
  }
}

export default CustomMultiKPI 