/* ===================================
   🎨 TRANSIÇÕES SUAVES DO SIDEBAR
   ================================== */

/* Transições principais do sidebar */
.sidebar-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Transição específica para largura do drawer */
.drawer-width-transition {
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Transição para o conteúdo principal */
.main-content-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Transição para o AppBar */
.appbar-transition {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* ===================================
   🎭 ANIMAÇÕES DOS ELEMENTOS INTERNOS
   ================================== */

/* Fade in/out para elementos que aparecem/desaparecem */
.sidebar-fade {
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-fade.show {
  opacity: 1;
  transform: translateX(0);
}

/* Controle de visibilidade durante transição */
.sidebar-elements-hidden {
  opacity: 0 !important;
  transition: opacity 0.2s ease-in-out !important;
  pointer-events: none !important;
}

.sidebar-elements-visible {
  opacity: 1 !important;
  transition: opacity 0.2s ease-in-out !important;
  pointer-events: auto !important;
}

/* ===================================
   🎯 PREVENÇÃO DE DESLOCAMENTO
   ================================== */

/* Container para elementos que não devem se deslocar */
.sidebar-stable-container {
  overflow: hidden !important;
  position: relative !important;
  will-change: transform !important;
  transform: translateZ(0) !important; /* Force hardware acceleration */
}

/* Texto estável que não se desloca */
.sidebar-stable-text {
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform: translateZ(0) !important;
}

/* Imagem estável que não se desloca */
.sidebar-stable-image {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
}

/* ===================================
   🎪 ANIMAÇÕES ESPECÍFICAS
   ================================== */

/* Animação para ícones quando o sidebar colapsa */
.icon-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animação para texto quando o sidebar colapsa */
.text-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

/* ===================================
   🎪 ANIMAÇÕES ESPECÍFICAS
   ================================== */

/* Animação de entrada para itens do menu */
.menu-item-enter {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* Animação de saída para itens do menu */
.menu-item-exit {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-exit-active {
  opacity: 0;
  transform: translateY(-10px);
}

/* ===================================
   🌊 EFEITOS DE HOVER MELHORADOS
   ================================== */

/* Hover suave para itens do menu */
.menu-item-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-item-hover:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* ===================================
   📱 RESPONSIVIDADE COM ANIMAÇÕES
   ================================== */

/* Animações específicas para mobile */
@media (max-width: 960px) {
  .sidebar-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
  
  .drawer-width-transition {
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }
}

/* ===================================
   ⚡ PERFORMANCE OPTIMIZATIONS
   ================================== */

/* Usar transform ao invés de propriedades que causam reflow */
.sidebar-transform {
  will-change: transform, opacity;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* ===================================
   🎨 CUSTOMIZAÇÕES ESPECÍFICAS
   ================================== */

/* Transição suave para o botão de toggle */
.sidebar-toggle-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sidebar-toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

/* Transição para o header do sidebar */
.sidebar-header {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Transição para o footer do sidebar */
.sidebar-footer {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* ===================================
   🎭 ANIMAÇÕES DO CONTEÚDO PRINCIPAL
   ================================== */

/* Animação para o conteúdo quando o sidebar muda */
.animated-content {
  will-change: transform, opacity, filter;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* ===================================
   🌟 EFEITOS ESPECIAIS
   ================================== */

/* Efeito de ripple para botões */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::after {
  width: 300px;
  height: 300px;
}

/* ===================================
   🎪 ANIMAÇÕES DE ENTRADA
   ================================== */

/* Animação de entrada para elementos que aparecem */
.fade-in {
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animação de entrada para elementos que deslizam */
.slide-in {
  animation: slideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* ===================================
   🎯 MELHORIAS ESPECÍFICAS
   ================================== */

/* Melhorar a transição do drawer paper */
.MuiDrawer-paper {
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Melhorar a transição do AppBar */
.MuiAppBar-root {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Melhorar a transição do conteúdo principal */
.MuiBox-root[component="main"] {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
} 