import React from 'react'
import { Box, Paper, Typography, Alert } from '@mui/material'
import CustomChart from '../CustomChart/CustomChart'

const ChartTest = () => {
  // Dados de teste com datas desordenadas (simulando o problema)
  const testDataDatesUnordered = {
    tipo: 'chart',
    titulo: 'Teste Ordenação de Datas',
    dados: {
      categories: ['Mar 2025', 'Fev 2025', 'Mar 2024', 'Set 2024', 'Nov 2024', 'Jan 2023', 'Fev 2023'],
      series: [
        {
          name: '<PERSON><PERSON><PERSON> (Barras)',
          data: [120, 200, 150, 80, 270, 230, 180]
        },
        {
          name: '<PERSON><PERSON> (Linha)',
          data: [100, 180, 160, 90, 250, 220, 200]
        }
      ]
    }
  }

  // Dados de teste simples para mixed
  const testDataMixed = {
    tipo: 'chart',
    titulo: 'Teste de Gráfico Misto',
    dados: {
      categories: ['Jan', 'Fev', 'Mar', 'Abr', '<PERSON>', 'Jun'],
      series: [
        {
          name: '<PERSON><PERSON><PERSON> (Barras)',
          data: [120, 200, 150, 80, 270, 230]
        },
        {
          name: '<PERSON><PERSON> (Lin<PERSON>)',
          data: [100, 180, 160, 90, 250, 220]
        },
        {
          name: '<PERSON><PERSON><PERSON>ncia (<PERSON>ha)',
          data: [110, 190, 155, 85, 260, 225]
        }
      ]
    }
  }

  const configs = [
    { 
      tipo: 'mixed', 
      titulo: '🔥 Gráfico Misto (mixed)',
      config: {
        tipoGrafico: 'mixed',
        showTitle: true,
        showLegend: true,
        showDataLabels: false,
        animacao: true,
        mixedConfig: { 
          barSeries: [0], 
          lineSeries: [1, 2] 
        }
      },
      data: testDataMixed
    },
    { 
      tipo: 'barLine', 
      titulo: '🔥 Gráfico barLine (alias)',
      config: {
        tipoGrafico: 'barLine',
        showTitle: true,
        showLegend: true,
        showDataLabels: false,
        animacao: true,
        mixedConfig: { 
          barSeries: [0], 
          lineSeries: [1, 2] 
        }
      },
      data: testDataMixed
    },
    { 
      tipo: 'dates', 
      titulo: '📅 Teste Ordenação por Data',
      config: {
        tipoGrafico: 'line',
        showTitle: true,
        showLegend: true,
        showDataLabels: false,
        animacao: true
      },
      data: testDataDatesUnordered
    },
    { 
      tipo: 'mixed-with-datazoom', 
      titulo: '🔥📊 Misto + DataZoom',
      config: {
        tipoGrafico: 'mixed',
        showTitle: true,
        showLegend: true,
        showDataLabels: false,
        animacao: true,
        dataZoom: { 
          enabled: true, 
          type: 'both', 
          start: 10, 
          end: 80,
          showDetail: true,
          realtime: true
        },
        mixedConfig: { 
          barSeries: [0], 
          lineSeries: [1, 2] 
        }
      },
      data: testDataMixed
    }
  ]

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" gutterBottom>
        🔧 Debug: Gráficos Mistos e Ordenação por Data
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Testando especificamente os problemas reportados
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>🔍 Instruções de Teste:</strong><br/>
          1. <strong>Gráfico Misto:</strong> Deve mostrar barras + linhas no mesmo gráfico<br/>
          2. <strong>Ordenação:</strong> Datas devem aparecer em ordem cronológica crescente<br/>
          3. <strong>DataZoom:</strong> Deve aparecer controles de zoom quando habilitado<br/>
          4. Verifique os logs do console para ver o debug detalhado
        </Typography>
      </Alert>
      
      <Box sx={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))',
        gap: 3,
        mb: 3
      }}>
        {configs.map((testConfig, index) => (
          <Paper key={index} sx={{ p: 2, height: 450 }}>
            <Typography variant="h6" gutterBottom color="primary.main">
              {testConfig.titulo}
            </Typography>
            
            <Typography variant="caption" display="block" color="text.secondary" sx={{ mb: 1 }}>
              <strong>Tipo:</strong> {testConfig.config.tipoGrafico}
            </Typography>
            
            {testConfig.config.dataZoom?.enabled && (
              <Typography variant="caption" display="block" color="warning.main" sx={{ mb: 1 }}>
                <strong>DataZoom:</strong> {testConfig.config.dataZoom.type} ({testConfig.config.dataZoom.start}% - {testConfig.config.dataZoom.end}%)
              </Typography>
            )}
            
            {testConfig.config.mixedConfig && (
              <Typography variant="caption" display="block" color="success.main" sx={{ mb: 1 }}>
                <strong>Mixed:</strong> Barras={testConfig.config.mixedConfig.barSeries.join(',')} | Linhas={testConfig.config.mixedConfig.lineSeries.join(',')}
              </Typography>
            )}
            
            <Box sx={{ height: 350 }}>
              <CustomChart 
                data={testConfig.data}
                config={testConfig.config}
              />
            </Box>
          </Paper>
        ))}
      </Box>
      
      <Alert severity="warning" sx={{ mt: 4 }}>
        <Typography variant="h6" gutterBottom color="warning.dark">
          🐛 O que verificar no Console:
        </Typography>
        <Typography variant="body2">
          <strong>1. 🔍 DEBUG_CHART_CONFIG:</strong> Verificar se tipoGrafico está correto<br/>
          <strong>2. 🔍 DEBUG_getEchartsType:</strong> Ver se o tipo está sendo reconhecido<br/>
          <strong>3. 📅 DEBUG: Ordenando dados por data:</strong> Se a ordenação está sendo aplicada<br/>
          <strong>4. 📊 DEBUG_CHART_PROCESSED_DATA:</strong> Ver dados finais processados<br/>
          <strong>5. 📊 CustomChart configuração responsiva:</strong> Ver tipo final aplicado
        </Typography>
      </Alert>
    </Box>
  )
}

export default ChartTest 