const Joi = require('joi');

// Schema para criação de empresa
const empresaSchema = Joi.object({
  id: Joi.string()
    .pattern(/^[a-z0-9-_]+$/)
    .min(3)
    .max(50)
    .required()
    .messages({
      'string.pattern.base': 'ID deve conter apenas letras minúsculas, números, hífens e underscores',
      'string.min': 'ID deve ter pelo menos 3 caracteres',
      'string.max': 'ID deve ter no máximo 50 caracteres',
      'any.required': 'ID é obrigatório'
    }),
  
  nome: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.min': 'Nome deve ter pelo menos 2 caracteres',
      'string.max': 'Nome deve ter no máximo 100 caracteres',
      'any.required': 'Nome é obrigatório'
    }),
  
  cor: Joi.string()
    .pattern(/^#[0-9A-Fa-f]{6}$/)
    .default('#667eea')
    .messages({
      'string.pattern.base': 'Cor deve estar no formato hexadecimal (#RRGGBB)'
    }),
  
  logo: Joi.string()
    .trim()
    .max(100000)
    .allow(null, '')
    .optional()
    .custom((value, helpers) => {
      if (!value) return value;
      
      const urlRegex = /^https?:\/\/.+/;
      const base64Regex = /^data:image\/(png|jpg|jpeg|gif|svg\+xml);base64,/;
      
      if (!urlRegex.test(value) && !base64Regex.test(value)) {
        return helpers.error('logo.invalid');
      }
      
      return value;
    })
    .messages({
      'string.max': 'Logo deve ter no máximo 100KB',
      'logo.invalid': 'Logo deve ser uma URL válida ou imagem em base64'
    }),
  
  ativo: Joi.boolean()
    .default(true),
  
  createdBy: Joi.string()
    .trim()
    .default('system')
});

// Schema para atualização de empresa (todos os campos opcionais exceto validações)
const empresaUpdateSchema = Joi.object({
  nome: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .messages({
      'string.min': 'Nome deve ter pelo menos 2 caracteres',
      'string.max': 'Nome deve ter no máximo 100 caracteres'
    }),
  
  cor: Joi.string()
    .pattern(/^#[0-9A-Fa-f]{6}$/)
    .messages({
      'string.pattern.base': 'Cor deve estar no formato hexadecimal (#RRGGBB)'
    }),
  
  logo: Joi.string()
    .trim()
    .max(100000)
    .allow(null, '')
    .optional()
    .custom((value, helpers) => {
      if (!value) return value;
      
      const urlRegex = /^https?:\/\/.+/;
      const base64Regex = /^data:image\/(png|jpg|jpeg|gif|svg\+xml);base64,/;
      
      if (!urlRegex.test(value) && !base64Regex.test(value)) {
        return helpers.error('logo.invalid');
      }
      
      return value;
    })
    .messages({
      'string.max': 'Logo deve ter no máximo 100KB',
      'logo.invalid': 'Logo deve ser uma URL válida ou imagem em base64'
    }),
  
  ativo: Joi.boolean()
});

// Função para validar criação de empresa
const validarEmpresa = (data) => {
  return empresaSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

// Função para validar atualização de empresa
const validarEmpresaUpdate = (data) => {
  return empresaUpdateSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

// Validar ID de empresa
const validarEmpresaId = (id) => {
  const schema = Joi.string()
    .pattern(/^[a-z0-9-_]+$/)
    .min(3)
    .max(50)
    .required();
  
  return schema.validate(id);
};

module.exports = {
  validarEmpresa,
  validarEmpresaUpdate,
  validarEmpresaId,
  empresaSchema,
  empresaUpdateSchema
}; 