const Joi = require('joi');

// Schema para criação de objeto
const objetoSchema = Joi.object({
  chave: Joi.string()
    .pattern(/^[a-zA-Z0-9_]+$/)
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.pattern.base': 'Chave deve conter apenas letras, números e underscores',
      'string.min': 'Chave deve ter pelo menos 2 caracteres',
      'string.max': 'Chave deve ter no máximo 50 caracteres',
      'any.required': 'Chave é obrigatória'
    }),
  
  objectId: Joi.string()
    .trim()
    .min(1)
    .required()
    .messages({
      'string.min': 'Object ID não pode estar vazio',
      'any.required': 'Object ID é obrigatório'
    }),
  
  nome: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.min': 'Nome deve ter pelo menos 2 caracteres',
      'string.max': 'Nome deve ter no máximo 100 caracteres',
      'any.required': 'Nome é obrigatório'
    }),
  
  tipo: Joi.string()
    .valid('kpi', 'multiKpi', 'bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'table', 'filter', 'listbox', 'text', 'image', 'button', 'gauge', 'other')
    .default('other')
    .messages({
      'any.only': 'Tipo deve ser um dos valores permitidos: kpi, multiKpi, bar, line, area, pie, donut, column, scatter, table, filter, listbox, text, image, button, gauge, other'
    }),
  
  categoria: Joi.string()
    .valid('dashboard', 'vendas', 'helpdesk', 'financeiro', 'rh', 'operacional', 'geral')
    .default('geral')
    .messages({
      'any.only': 'Categoria deve ser um dos valores: dashboard, vendas, helpdesk, financeiro, rh, operacional, geral'
    }),
  
  descricao: Joi.string()
    .trim()
    .max(500)
    .allow('')
    .messages({
      'string.max': 'Descrição deve ter no máximo 500 caracteres'
    }),
  
  configuracao: Joi.object({
    altura: Joi.string()
      .default('auto'),
    
    largura: Joi.string()
      .default('100%'),
    
    showTitle: Joi.boolean()
      .default(true),
    
    showToolbar: Joi.boolean()
      .default(true),
    
    allowInteraction: Joi.boolean()
      .default(true),
    
    allowExport: Joi.boolean()
      .default(false),
    
    // Configurações de KPI
    kpiConfig: Joi.object({
      formato: Joi.string()
        .valid('numero', 'moeda', 'percentual', 'personalizado')
        .default('numero'),
      casasDecimais: Joi.number()
        .integer()
        .min(0)
        .max(10)
        .default(0),
      prefix: Joi.string()
        .allow('')
        .default(''),
      suffix: Joi.string()
        .allow('')
        .default(''),
      showTrend: Joi.boolean()
        .default(false),
      color: Joi.string()
        .allow('')
        .default(''),
      backgroundColor: Joi.string()
        .default('transparent'),
      textAlign: Joi.string()
        .valid('left', 'center', 'right')
        .default('center'),
      fontSize: Joi.string()
        .default('auto'),
      // ✅ NOVO: Campos visuais avançados para KPI
      cor: Joi.string()
        .default('#1976d2'),
      corFonte: Joi.string()
        .default('#ffffff'),
      icone: Joi.string()
        .valid('money', 'person', 'cart', 'timeline', 'assessment', 'star', 'heart', 'business', 'atm', 'bank', 'analytics')
        .default('analytics'),
      showIcon: Joi.boolean()
        .default(true),
      gradiente: Joi.boolean()
        .default(false),
      showTitle: Joi.boolean()
        .default(true)
    }),
    
    // Configurações de Multi KPI
    multiKpiConfig: Joi.object({
      itemsPerRow: Joi.number()
        .integer()
        .min(1)
        .max(6)
        .default(3),
      spacing: Joi.number()
        .integer()
        .min(0)
        .max(5)
        .default(2),
      showTitle: Joi.boolean()
        .default(true),
      showIcon: Joi.boolean()
        .default(false),
      layout: Joi.string()
        .valid('grid', 'horizontal', 'unified')
        .default('unified'),
      elevation: Joi.number()
        .integer()
        .min(0)
        .max(10)
        .default(1),
      // Configurações visuais do Multi KPI
      cor: Joi.string()
        .default('#1976d2'),
      corFonte: Joi.string()
        .default('#ffffff'),
      // ✅ NOVO: Campo ícone para Multi KPI
      icone: Joi.string()
        .valid('money', 'person', 'cart', 'timeline', 'assessment', 'star', 'heart', 'business', 'atm', 'bank', 'analytics')
        .default('analytics'),
      // Configurações específicas para filtros
      filterConfig: Joi.object({
        showTitle: Joi.boolean()
          .default(true),
        showSearchBox: Joi.boolean()
          .default(true),
        showClearButton: Joi.boolean()
          .default(true),
        allowMultiSelect: Joi.boolean()
          .default(true),
        maxHeight: Joi.string()
          .default('300px'),
        compact: Joi.boolean()
          .default(false),
        backgroundColor: Joi.string()
          .default('transparent'),
        borderRadius: Joi.string()
          .default('8px'),
        showSelectionCount: Joi.boolean()
          .default(true),
        itemsPerRow: Joi.number()
          .integer()
          .min(1)
          .max(6)
          .default(1)
      })
    }),
    
    // Configurações de gráficos
    chartConfig: Joi.object({
      tipoGrafico: Joi.string()
        .valid('bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed', 'gauge')
        .default('bar'),
      showLegend: Joi.boolean()
        .default(true),
      showDataLabels: Joi.boolean()
        .default(false),
      color: Joi.array()
        .items(Joi.string())
        .default([]),
      backgroundColor: Joi.string()
        .default('transparent'),
      animacao: Joi.boolean()
        .default(true),
      // ✅ NOVO: Configurações para gráfico misto
      mixedConfig: Joi.object({
        barSeries: Joi.array()
          .items(Joi.number().integer().min(0))
          .default([0]),
        lineSeries: Joi.array()
          .items(Joi.number().integer().min(0))
          .default([1]),
        // ✅ NOVO: Configurações de porcentagem
        usePercentageScale: Joi.boolean()
          .default(false),
        percentageAxisMin: Joi.number()
          .default(0),
        percentageAxisMax: Joi.number()
          .default(100),
        autoDetectPercentage: Joi.boolean()
          .default(true),
        valueAxisName: Joi.string()
          .allow('')
          .default('Valores'),
        percentageAxisName: Joi.string()
          .allow('')
          .default('Porcentagem (%)')
      }).default({
        barSeries: [0],
        lineSeries: [1],
        usePercentageScale: false,
        percentageAxisMin: 0,
        percentageAxisMax: 100,
        autoDetectPercentage: true,
        valueAxisName: 'Valores',
        percentageAxisName: 'Porcentagem (%)'
      }),
      // ✅ NOVO: Configurações do DataZoom
      dataZoom: Joi.object({
        enabled: Joi.boolean().default(false),
        type: Joi.string()
          .valid('slider', 'inside', 'both')
          .default('slider'),
        start: Joi.number()
          .min(0)
          .max(100)
          .default(0),
        end: Joi.number()
          .min(0)
          .max(100)
          .default(100),
        showDetail: Joi.boolean().default(true),
        realtime: Joi.boolean().default(true)
      }).default({
        enabled: false,
        type: 'slider',
        start: 0,
        end: 100,
        showDetail: true,
        realtime: true
      }),
      grid: Joi.object({
        top: Joi.number().default(60),
        right: Joi.number().default(30),
        bottom: Joi.number().default(60),
        left: Joi.number().default(60)
      }),
      xAxis: Joi.object({
        show: Joi.boolean().default(true),
        name: Joi.string().allow('').default('')
      }),
      yAxis: Joi.object({
        show: Joi.boolean().default(true),
        name: Joi.string().allow('').default('')
      })
    }),
    
    // Layouts por página
    paginas: Joi.array()
      .items(Joi.string().valid('dashboard', 'vendas', 'financeiro', 'helpdesk', 'rh', 'operacional'))
      .default([]),
    
    layouts: Joi.object()
      .pattern(
        Joi.string(),
        Joi.object({
          x: Joi.number().min(0).required(),
          y: Joi.number().min(0).required(),
          w: Joi.number().min(1).required(),
          h: Joi.number().min(1).required()
        })
      )
      .default({})
  }).default({}),
  
  ordem: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.integer': 'Ordem deve ser um número inteiro',
      'number.min': 'Ordem deve ser maior ou igual a 0'
    }),
  
  ativo: Joi.boolean()
    .default(true),
  
  createdBy: Joi.string()
    .trim()
    .default('system')
});

// Schema para atualização de objeto (todos os campos opcionais exceto validações)
const objetoUpdateSchema = Joi.object({
  chave: Joi.string()
    .pattern(/^[a-zA-Z0-9_]+$/)
    .min(2)
    .max(50)
    .messages({
      'string.pattern.base': 'Chave deve conter apenas letras, números e underscores',
      'string.min': 'Chave deve ter pelo menos 2 caracteres',
      'string.max': 'Chave deve ter no máximo 50 caracteres'
    }),
  
  objectId: Joi.string()
    .trim()
    .min(1)
    .messages({
      'string.min': 'Object ID não pode estar vazio'
    }),
  
  nome: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .messages({
      'string.min': 'Nome deve ter pelo menos 2 caracteres',
      'string.max': 'Nome deve ter no máximo 100 caracteres'
    }),
  
  tipo: Joi.string()
    .valid('kpi', 'multiKpi', 'bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'table', 'filter', 'listbox', 'text', 'image', 'button', 'gauge', 'other')
    .messages({
      'any.only': 'Tipo deve ser um dos valores permitidos: kpi, multiKpi, bar, line, area, pie, donut, column, scatter, table, filter, listbox, text, image, button, gauge, other'
    }),
  
  categoria: Joi.string()
    .valid('dashboard', 'vendas', 'helpdesk', 'financeiro', 'rh', 'operacional', 'geral')
    .messages({
      'any.only': 'Categoria deve ser um dos valores: dashboard, vendas, helpdesk, financeiro, rh, operacional, geral'
    }),
  
  descricao: Joi.string()
    .trim()
    .max(500)
    .allow('')
    .messages({
      'string.max': 'Descrição deve ter no máximo 500 caracteres'
    }),
  
  configuracao: Joi.object({
    altura: Joi.string(),
    largura: Joi.string(),
    showTitle: Joi.boolean(),
    showToolbar: Joi.boolean(),
    allowInteraction: Joi.boolean(),
    allowExport: Joi.boolean(),
    kpiConfig: Joi.object({
      formato: Joi.string().valid('numero', 'moeda', 'percentual', 'personalizado'),
      casasDecimais: Joi.number().integer().min(0).max(10),
      prefix: Joi.string().allow(''),
      suffix: Joi.string().allow(''),
      showTrend: Joi.boolean(),
      color: Joi.string().allow(''),
      backgroundColor: Joi.string(),
      textAlign: Joi.string().valid('left', 'center', 'right'),
      fontSize: Joi.string()
    }),
    multiKpiConfig: Joi.object({
      itemsPerRow: Joi.number().integer().min(1).max(6),
      spacing: Joi.number().integer().min(0).max(5),
      showTitle: Joi.boolean(),
      showIcon: Joi.boolean(),
      layout: Joi.string().valid('grid', 'horizontal', 'unified'),
      elevation: Joi.number().integer().min(0).max(10),
      // Configurações visuais do Multi KPI
      cor: Joi.string(),
      corFonte: Joi.string(),
      // ✅ NOVO: Campo ícone para Multi KPI
      icone: Joi.string()
        .valid('money', 'person', 'cart', 'timeline', 'assessment', 'star', 'heart', 'business', 'atm', 'bank', 'analytics')
        .default('analytics'),
      // Configurações específicas para filtros
      filterConfig: Joi.object({
        showTitle: Joi.boolean(),
        showSearchBox: Joi.boolean(),
        showClearButton: Joi.boolean(),
        allowMultiSelect: Joi.boolean(),
        maxHeight: Joi.string(),
        compact: Joi.boolean(),
        backgroundColor: Joi.string(),
        borderRadius: Joi.string(),
        showSelectionCount: Joi.boolean(),
        itemsPerRow: Joi.number().integer().min(1).max(6)
      })
    }),
    chartConfig: Joi.object({
      tipoGrafico: Joi.string().valid('bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed', 'gauge'),
      showLegend: Joi.boolean(),
      showDataLabels: Joi.boolean(),
      color: Joi.array().items(Joi.string()),
      backgroundColor: Joi.string(),
      animacao: Joi.boolean(),
      mixedConfig: Joi.object({
        barSeries: Joi.array()
          .items(Joi.number().integer().min(0))
          .default([0]),
        lineSeries: Joi.array()
          .items(Joi.number().integer().min(0))
          .default([1]),
        // ✅ NOVO: Configurações de porcentagem
        usePercentageScale: Joi.boolean()
          .default(false),
        percentageAxisMin: Joi.number()
          .default(0),
        percentageAxisMax: Joi.number()
          .default(100),
        autoDetectPercentage: Joi.boolean()
          .default(true),
        valueAxisName: Joi.string()
          .allow('')
          .default('Valores'),
        percentageAxisName: Joi.string()
          .allow('')
          .default('Porcentagem (%)')
      }).default({
        barSeries: [0],
        lineSeries: [1],
        usePercentageScale: false,
        percentageAxisMin: 0,
        percentageAxisMax: 100,
        autoDetectPercentage: true,
        valueAxisName: 'Valores',
        percentageAxisName: 'Porcentagem (%)'
      }),
      dataZoom: Joi.object({
        enabled: Joi.boolean().default(false),
        type: Joi.string()
          .valid('slider', 'inside', 'both')
          .default('slider'),
        start: Joi.number()
          .min(0)
          .max(100)
          .default(0),
        end: Joi.number()
          .min(0)
          .max(100)
          .default(100),
        showDetail: Joi.boolean().default(true),
        realtime: Joi.boolean().default(true)
      }).default({
        enabled: false,
        type: 'slider',
        start: 0,
        end: 100,
        showDetail: true,
        realtime: true
      }),
      grid: Joi.object({
        top: Joi.number(),
        right: Joi.number(),
        bottom: Joi.number(),
        left: Joi.number()
      }),
      xAxis: Joi.object({
        show: Joi.boolean(),
        name: Joi.string().allow('')
      }),
      yAxis: Joi.object({
        show: Joi.boolean(),
        name: Joi.string().allow('')
      })
    }),
    paginas: Joi.array().items(Joi.string().valid('dashboard', 'vendas', 'financeiro', 'helpdesk', 'rh', 'operacional')),
    layouts: Joi.object().pattern(
      Joi.string(),
      Joi.object({
        x: Joi.number().min(0).required(),
        y: Joi.number().min(0).required(),
        w: Joi.number().min(1).required(),
        h: Joi.number().min(1).required()
      })
    )
  }),
  
  ordem: Joi.number()
    .integer()
    .min(0)
    .messages({
      'number.integer': 'Ordem deve ser um número inteiro',
      'number.min': 'Ordem deve ser maior ou igual a 0'
    }),
  
  ativo: Joi.boolean()
});

// Schema para múltiplos objetos
const multiplosObjetosSchema = Joi.object({
  objetos: Joi.array()
    .items(objetoSchema)
    .min(1)
    .required()
    .messages({
      'array.min': 'É necessário fornecer pelo menos um objeto',
      'any.required': 'Array de objetos é obrigatório'
    })
});

// Schema para reordenação
const reordenacaoSchema = Joi.object({
  objetos: Joi.array()
    .items(
      Joi.object({
        id: Joi.string().required(),
        ordem: Joi.number().integer().min(0).required()
      })
    )
    .min(1)
    .required()
    .messages({
      'array.min': 'É necessário fornecer pelo menos um objeto para reordenar',
      'any.required': 'Array de objetos é obrigatório'
    })
});

// Função para validar criação de objeto
const validarObjeto = (data) => {
  return objetoSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

// Função para validar atualização de objeto
const validarObjetoUpdate = (data) => {
  return objetoUpdateSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

// Função para validar múltiplos objetos
const validarMultiplosObjetos = (data) => {
  return multiplosObjetosSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

// Função para validar reordenação
const validarReordenacao = (data) => {
  return reordenacaoSchema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  });
};

// Validar chave de objeto
const validarChaveObjeto = (chave) => {
  const schema = Joi.string()
    .pattern(/^[a-zA-Z0-9_]+$/)
    .min(2)
    .max(50)
    .required();
  
  return schema.validate(chave);
};

module.exports = {
  validarObjeto,
  validarObjetoUpdate,
  validarMultiplosObjetos,
  validarReordenacao,
  validarChaveObjeto,
  objetoSchema,
  objetoUpdateSchema,
  multiplosObjetosSchema,
  reordenacaoSchema
};