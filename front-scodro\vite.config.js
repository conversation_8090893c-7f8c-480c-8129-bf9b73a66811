import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// Plugin personalizado para remover diretivas "use client" 
const removeUseClientPlugin = () => {
  return {
    name: 'remove-use-client',
    transform(code, id) {
      // Aplicar apenas em arquivos do @mui/icons-material
      if (id.includes('@mui/icons-material') || id.includes('node_modules/@mui/icons-material')) {
        // Remover linha "use client" do início do arquivo
        return code.replace(/^["']use client["'];?\s*\n?/m, '')
      }
    }
  }
}

export default defineConfig(({ mode }) => {
  const isEnterprise = process.env.VITE_TARGET === 'enterprise'
  const isProduction = mode === 'production'

  return {
    plugins: [
      react(),
      removeUseClientPlugin()
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@config': resolve(__dirname, 'src/config'),
        '@components': resolve(__dirname, 'src/components'),
        '@services': resolve(__dirname, 'src/services'),
        '@pages': resolve(__dirname, 'src/pages'),
        '@context': resolve(__dirname, 'src/context'),
        '@contexts': resolve(__dirname, 'src/contexts'),
        '@utils': resolve(__dirname, 'src/utils')
      }
    },
    define: {
      'process.env.VITE_TARGET': JSON.stringify(process.env.VITE_TARGET || 'development'),
      'process.env.NODE_ENV': JSON.stringify(mode)
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      rollupOptions: {
        // Ignorar avisos sobre "use client" directives
        onwarn(warning, warn) {
          // Ignorar avisos sobre module level directives (use client)
          if (warning.code === 'MODULE_LEVEL_DIRECTIVE') {
            return
          }
          
          // Ignorar avisos específicos dos ícones MUI
          if (warning.message && warning.message.includes('"use client"') && 
              warning.message.includes('@mui/icons-material')) {
            return
          }
          
          // Ignorar avisos sobre "use client" em geral
          if (warning.message && warning.message.includes('Module level directives cause errors when bundled')) {
            return
          }
          
          // Para outros avisos, usar o comportamento padrão
          warn(warning)
        },
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            mui: ['@mui/material', '@mui/icons-material'],
            grid: ['react-grid-layout']
          },
          // Para Enterprise, não usar hash nos nomes dos arquivos
          ...(isEnterprise && {
            entryFileNames: 'assets/[name].js',
            chunkFileNames: 'assets/[name].js',
            assetFileNames: 'assets/[name].[ext]'
          })
        }
      }
    },
    server: {
      port: 3333,
      host: true,
      cors: true,
      allowedHosts: [
        'localhost',
        '127.0.0.1',
        '8a6b797f14d6.ngrok-free.app'
      ]
    },
    preview: {
      port: 3333,
      host: true
    }
  }
}) 