# 🆕 Novas Funcionalidades - Qlik Mashup React

## ✅ Funcionalidades Implementadas

### 1. 📱 Sidebar Colapsável
- **Botão de colapsar/expandir** no canto superior direito do sidebar
- **Ícone hamburger** que alterna entre expandido e colapsado
- **Transições suaves** com animações Material-UI
- **Tooltips informativos** quando colapsado
- **Estado persistente** salvo no localStorage
- **Layout responsivo** que se adapta ao tamanho do sidebar

### 2. 🏢 Sistema Hierárquico de Empresas
- **Estrutura hierárquica**: Empresa → Dashboard/Vendas/Helpdesk/Financeiro
- **Dropdown expansível** para cada empresa
- **Seleção de empresa ativa** com destaque visual
- **Cores personalizadas** para cada empresa
- **Estado de expansão persistente** no localStorage

### 3. ⚙️ Gerenciamento de Configurações
- **Interface completa** para cadastro de empresas
- **Formulário modal** para edição/criação
- **Campos organizados** por seções (Apps, Objetos)
- **Validação de dados** e feedback visual
- **Exportação de configurações** em JSON
- **Accordion expansível** para visualização

### 4. 🎨 Melhorias de Interface
- **Layout moderno** com gradientes e sombras
- **Cards coloridos** para diferentes seções
- **Ícones temáticos** para cada funcionalidade
- **Tipografia aprimorada** com hierarquia clara
- **Responsividade completa** para mobile/desktop

### 5. 📈 Novas Funcionalidades de Gráficos

#### 📊📈 Gráfico Misto (Barras + Linhas)

Implementado suporte para gráficos combinados que permitem mostrar barras e linhas no mesmo gráfico, similar ao Qlik Sense.

##### Características:
- **Dois eixos Y**: Barras usam eixo esquerdo, linhas usam eixo direito
- **Configuração flexível**: Defina quais séries devem ser barras ou linhas
- **Escalas independentes**: Cada eixo pode ter escala diferente
- **Visual profissional**: Segue as melhores práticas de visualização

##### Como Usar:
1. Na tela de **Configurações**, ao criar/editar um objeto
2. Selecione **Tipo de Gráfico**: "Misto (Barras + Linhas)"
3. Configure as séries:
   - **Séries como Barras**: Digite os índices separados por vírgula (ex: `0,2`)
   - **Séries como Linhas**: Digite os índices separados por vírgula (ex: `1,3`)
4. Índices começam em 0 (primeira série = 0, segunda = 1, etc.)

##### Exemplo de Configuração:
- Se você tem 3 medidas: Vendas, Meta, Tendência
- Para Vendas como barra e Meta+Tendência como linhas:
  - Séries como Barras: `0`
  - Séries como Linhas: `1,2`

#### 📊 DataZoom - Navegação e Zoom em Gráficos

Implementado controles de zoom e navegação para gráficos ECharts, permitindo explorar grandes volumes de dados.

##### Tipos de DataZoom:

###### 📊 Slider (Barra de Navegação)
- Mostra uma barra na parte inferior do gráfico
- Permite arrastar as extremidades para definir o período visível
- Ideal para navegação visual e intuitiva

###### 🖱️ Inside (Controle por Mouse)
- Zoom com scroll do mouse
- Arrastar para navegar pelos dados
- Mais discreto, não ocupa espaço visual

###### ⚡ Both (Ambos)
- Combina slider + controle por mouse
- Máxima flexibilidade de navegação
- Recomendado para análises detalhadas

#### Configurações Disponíveis:

- **Posição Inicial (%)**: Define onde o zoom começa (0-100%)
- **Posição Final (%)**: Define onde o zoom termina (0-100%)
- **Mostrar Detalhes**: Exibe informações durante a navegação
- **Atualização em Tempo Real**: Atualiza o gráfico durante o movimento

#### Como Configurar:
1. Na tela de **Configurações**, edite um objeto gráfico
2. Na seção **"📊 Configurações de DataZoom"**
3. Marque **"Habilitar DataZoom"**
4. Escolha o tipo e configure as posições
5. Ajuste as opções conforme necessário

#### Exemplo de Uso:
- Para mostrar apenas os últimos 6 meses de um ano:
  - Posição Inicial: `50%`
  - Posição Final: `100%`
  - Tipo: `Slider`

## 📁 Estrutura de Arquivos Atualizada

```
src/
├── config/
│   └── apps.js              # Configuração hierárquica de empresas
├── context/
│   └── EmpresaContext.jsx   # Contexto React para estado global
├── components/
│   ├── Layout.jsx           # Layout com sidebar colapsável
│   └── QlikObject.jsx       # Componente Qlik (inalterado)
├── pages/
│   ├── Dashboard.jsx        # Dashboard principal
│   ├── Vendas.jsx          # Análise de vendas
│   ├── Helpdesk.jsx        # Dashboard de tickets
│   ├── Financeiro.jsx      # Análise financeira (NOVO)
│   └── Configuracoes.jsx   # Gerenciamento de empresas (NOVO)
└── services/
    └── qlik.js             # Serviço Qlik (inalterado)
```

## 🔧 Como Usar

### Colapsar/Expandir Sidebar
1. Clique no ícone de seta no canto superior direito do sidebar
2. O sidebar alternará entre expandido (280px) e colapsado (72px)
3. O estado é salvo automaticamente no localStorage

### Gerenciar Empresas
1. Acesse **Configurações** no menu inferior
2. Clique no botão **+** (FAB) para adicionar nova empresa
3. Preencha os dados da empresa e IDs dos apps/objetos
4. Use os botões de editar/excluir para gerenciar empresas existentes

### Alternar Entre Empresas
1. No sidebar, clique no nome da empresa para expandir/colapsar
2. Clique nos itens do menu (Dashboard, Vendas, etc.) para navegar
3. A empresa selecionada fica destacada em azul

## 🎯 Configuração de IDs

### Estrutura de Empresa
```javascript
{
  id: 'empresa-exemplo',
  nome: 'Empresa Exemplo',
  cor: '#667eea',
  apps: {
    dashboard: 'APP_ID_DASHBOARD',
    vendas: 'APP_ID_VENDAS',
    financeiro: 'APP_ID_FINANCEIRO',
    helpdesk: 'APP_ID_HELPDESK'
  },
  objetos: {
    kpi1: 'OBJECT_ID_KPI_1',
    kpi2: 'OBJECT_ID_KPI_2',
    // ... outros objetos
  }
}
```

### Onde Encontrar IDs
- **App IDs**: URL do app `/sense/app/[APP_ID]`
- **Object IDs**: Botão direito no objeto → Propriedades → ID
- **Web Integration ID**: Management Console do Qlik Cloud

## 🚀 Comandos de Desenvolvimento

```bash
# Desenvolvimento com placeholders
npm run dev

# Build para Qlik Cloud
npm run build:cloud

# Build para Qlik Enterprise
npm run build:enterprise
```

## 📱 Responsividade

### Desktop (>= 960px)
- Sidebar fixo lateral
- Layout em grid completo
- Todas as funcionalidades visíveis

### Mobile (< 960px)
- Sidebar como drawer temporário
- Layout em coluna única
- Menu hamburger no AppBar

## 🎨 Temas e Cores

### Cores Principais
- **Primary**: `#667eea` → `#764ba2` (gradiente)
- **Success**: `#4CAF50` (KPIs positivos)
- **Warning**: `#FF9800` (alertas)
- **Error**: `#F44336` (erros)

### Gradientes
- **Header**: `135deg, #667eea 0%, #764ba2 100%`
- **Cards**: Gradientes específicos por seção
- **Backgrounds**: `#f5f5f5` (cinza claro)

## 🔄 Estado Persistente

### localStorage
- `empresaSelecionada`: ID da empresa ativa
- `sidebarCollapsed`: Estado do sidebar (true/false)
- `empresasExpandidas`: Objeto com estado de cada empresa

### Contexto React
- **EmpresaProvider**: Provedor global de estado
- **useEmpresa**: Hook para acessar estado/funções
- **Sincronização**: Automática com localStorage

## 🛠️ Próximos Passos

### Funcionalidades Futuras
1. **Sistema de Permissões**: Filtrar empresas por usuário
2. **Temas Personalizados**: Dark mode e cores customizáveis
3. **Favoritos**: Marcar dashboards favoritos
4. **Notificações**: Alertas em tempo real
5. **Exportação**: PDF/Excel dos dashboards

### Melhorias Técnicas
1. **Cache**: Otimização de carregamento
2. **Lazy Loading**: Carregamento sob demanda
3. **PWA**: Progressive Web App
4. **Testes**: Unit tests e E2E
5. **TypeScript**: Migração para TS

## 📞 Suporte

Para dúvidas ou problemas:
1. Consulte a documentação no `README.md`
2. Verifique os logs do console do navegador
3. Teste em modo desenvolvimento primeiro
4. Valide os IDs de apps e objetos no Qlik Sense

---

**Versão**: 2.0.0  
**Data**: Dezembro 2024  
**Compatibilidade**: Qlik Cloud + Enterprise 