const App = require('../models/App');
const Objeto = require('../models/Objeto');

// Listar apps por empresa
const listarAppsPorEmpresa = async (req, res) => {
  try {
    const { empresaId } = req.params;
    const { incluirObjetos = 'true' } = req.query;

    let query = App.find({ empresaId, ativo: true }).sort({ ordem: 1 });

    if (incluirObjetos === 'true') {
      query = query.populate({
        path: 'objetos',
        match: { ativo: true },
        options: { sort: { ordem: 1 } }
      });
    }

    const apps = await query;

    res.json({
      success: true,
      data: apps,
      total: apps.length
    });
  } catch (error) {
    console.error('Erro ao listar apps:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Obter app específico
const obterApp = async (req, res) => {
  try {
    const { empresaId, appId } = req.params;
    const { incluirObjetos = 'true' } = req.query;

    let query = App.findOne({ _id: appId, empresaId });

    if (incluirObjetos === 'true') {
      query = query.populate({
        path: 'objetos',
        match: { ativo: true },
        options: { sort: { ordem: 1 } }
      });
    }

    const app = await query;

    if (!app) {
      return res.status(404).json({
        success: false,
        error: 'App não encontrado'
      });
    }

    res.json({
      success: true,
      data: app
    });
  } catch (error) {
    console.error('Erro ao obter app:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Criar novo app
const criarApp = async (req, res) => {
  try {
    const { empresaId } = req.params;
    const dadosApp = req.body;

    // Obter próxima ordem
    const proximaOrdem = await App.getProximaOrdem(empresaId);

    const novoApp = new App({
      empresaId,
      ...dadosApp,
      ordem: proximaOrdem
    });

    await novoApp.save();

    res.status(201).json({
      success: true,
      data: novoApp,
      message: 'App criado com sucesso'
    });
  } catch (error) {
    console.error('Erro ao criar app:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        detalhes: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Atualizar app
const atualizarApp = async (req, res) => {
  try {
    const { empresaId, appId } = req.params;
    const dadosApp = req.body;

    const app = await App.findOne({ _id: appId, empresaId });

    if (!app) {
      return res.status(404).json({
        success: false,
        error: 'App não encontrado'
      });
    }

    Object.assign(app, dadosApp);
    await app.save();

    res.json({
      success: true,
      data: app,
      message: 'App atualizado com sucesso'
    });
  } catch (error) {
    console.error('Erro ao atualizar app:', error);
    
    if (error.name === 'ValidationError') {
      return res.status(400).json({
        success: false,
        error: 'Dados inválidos',
        detalhes: Object.values(error.errors).map(err => err.message)
      });
    }

    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Excluir app
const excluirApp = async (req, res) => {
  try {
    const { empresaId, appId } = req.params;
    const { forceDelete = 'false' } = req.query;

    const app = await App.findOne({ _id: appId, empresaId });

    if (!app) {
      return res.status(404).json({
        success: false,
        error: 'App não encontrado'
      });
    }

    if (forceDelete === 'true') {
      // Excluir também todos os objetos relacionados
      await Objeto.deleteMany({ appId: app._id, empresaId });
      await App.deleteOne({ _id: appId, empresaId });
      
      res.json({
        success: true,
        message: 'App e objetos relacionados excluídos com sucesso'
      });
    } else {
      // Apenas desativar
      app.ativo = false;
      await app.save();
      
      res.json({
        success: true,
        message: 'App desativado com sucesso'
      });
    }
  } catch (error) {
    console.error('Erro ao excluir app:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Reordenar apps
const reordenarApps = async (req, res) => {
  try {
    const { empresaId } = req.params;
    const { apps } = req.body;

    if (!Array.isArray(apps)) {
      return res.status(400).json({
        success: false,
        error: 'Lista de apps deve ser um array'
      });
    }

    await App.reordenarApps(empresaId, apps);

    res.json({
      success: true,
      message: 'Apps reordenados com sucesso'
    });
  } catch (error) {
    console.error('Erro ao reordenar apps:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

// Reativar app
const reativarApp = async (req, res) => {
  try {
    const { empresaId, appId } = req.params;

    const app = await App.findOne({ _id: appId, empresaId });

    if (!app) {
      return res.status(404).json({
        success: false,
        error: 'App não encontrado'
      });
    }

    app.ativo = true;
    await app.save();

    res.json({
      success: true,
      data: app,
      message: 'App reativado com sucesso'
    });
  } catch (error) {
    console.error('Erro ao reativar app:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
};

module.exports = {
  listarAppsPorEmpresa,
  obterApp,
  criarApp,
  atualizarApp,
  excluirApp,
  reordenarApps,
  reativarApp
}; 