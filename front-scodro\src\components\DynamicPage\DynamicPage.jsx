import React, { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { Box, Typography, Alert, Switch, FormControlLabel, Tooltip } from '@mui/material'
import { Edit as EditIcon, Visibility as VisibilityIcon } from '@mui/icons-material'
import GridLayout from '@components/GridLayout/GridLayout'
import { useEmpresa } from '@context/EmpresaContext'

// Importar as páginas específicas para fallback
import Dashboard from '@pages/Dashboard'
import Vendas from '@pages/Vendas'
import Helpdesk from '@pages/Helpdesk'
import Financeiro from '@pages/Financeiro'

const DynamicPage = () => {
  const { empresaId, chave } = useParams()
  const navigate = useNavigate();
  const { empresaSelecionada, empresaAtual, loading, error, selecionarEmpresa, empresas } = useEmpresa()
  const [editMode, setEditMode] = useState(process.env.NODE_ENV === 'development' ? false : false)

  // Se há empresaId na URL e é diferente da empresa selecionada, selecionar automaticamente
  useEffect(() => {
    if (empresaId && empresaId !== empresaSelecionada && empresas.length > 0) {
      // Verificar se a empresa existe
      const empresaExiste = empresas.some(emp => (emp.id === empresaId || emp._id === empresaId))
      if (empresaExiste) {
        selecionarEmpresa(empresaId)
      }
    }
  }, [empresaId, empresaSelecionada, empresas, selecionarEmpresa])

  // Redirecionar para a primeira página dinâmica se não houver chave na URL
  useEffect(() => {
    // empresaParaUsar é definido depois, então precisamos replicar a lógica aqui
    const empresaParaUsar = empresaId ? empresas.find(emp => (emp.id === empresaId || emp._id === empresaId)) : empresaAtual;
    if (
      empresaParaUsar &&
      (!chave || chave === '') &&
      empresaParaUsar.paginas &&
      empresaParaUsar.paginas.length > 0
    ) {
      // Redireciona para a primeira página dinâmica
      navigate(`/${empresaParaUsar.id || empresaParaUsar._id}/${empresaParaUsar.paginas[0].chave}`, { replace: true });
    }
  }, [empresaId, chave, empresas, empresaAtual, navigate]);

  // Determinar qual empresa usar (URL tem prioridade)
  const empresaParaUsar = empresaId ? empresas.find(emp => (emp.id === empresaId || emp._id === empresaId)) : empresaAtual

  // Encontrar a página atual baseada na chave
  const paginaAtual = empresaParaUsar?.paginas?.find(p => p.chave === chave)

  // Fallback para páginas específicas se existirem
  const renderSpecificPage = () => {
    switch (chave) {
      case 'dashboard':
        return <Dashboard />
      case 'vendas':
        return <Vendas />
      case 'helpdesk':
        return <Helpdesk />
      case 'financeiro':
        return <Financeiro />
      default:
        return null
    }
  }

  // Obter appId baseado na página
  const getAppId = () => {
    if (!empresaParaUsar) return null
    
    // Se tem apps como array
    if (Array.isArray(empresaParaUsar.apps)) {
      // Procurar app que corresponda à categoria da página
      const appParaPagina = empresaParaUsar.apps.find(app => 
        app.categoria === chave || 
        app.nome?.toLowerCase().includes(chave)
      )
      
      if (appParaPagina && appParaPagina._id) {
        return appParaPagina._id
      }
      
      // Se não encontrou específico, usar o primeiro app
      if (empresaParaUsar.apps.length > 0 && empresaParaUsar.apps[0]._id) {
        return empresaParaUsar.apps[0]._id
      }
    }
    
    // Fallback: usar ID genérico da empresa
    const empresaIdParaUsar = empresaId || empresaSelecionada
    return `${chave}-${empresaIdParaUsar}`
  }

  const appId = getAppId()

  if (loading) {
    return (
      <Box sx={{ p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          Carregando dados da empresa...
        </Typography>
      </Box>
    )
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error" sx={{ mt: 2 }}>
          Erro ao carregar dados: {error}
        </Alert>
      </Box>
    )
  }

  if (!empresaParaUsar) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="warning" sx={{ mt: 2 }}>
          Empresa não encontrada. Verifique se a empresa existe e está ativa.
        </Alert>
      </Box>
    )
  }

  // Se existe uma página específica implementada, usar ela
  const specificPage = renderSpecificPage()
  if (specificPage) {
    return specificPage
  }

  // Caso contrário, renderizar página dinâmica
  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toggle de Habilitação de Edição - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <Box sx={{ 
          p: 2, 
          pb: 1,
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          minHeight: '60px'
        }}>
          
          <Tooltip title={editMode ? "Desabilitar modo de edição" : "Habilitar modo de edição"}>
            <FormControlLabel
              control={
                <Switch
                  checked={editMode}
                  onChange={(e) => setEditMode(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {editMode ? <EditIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
                  <Typography variant="body2">
                    {editMode ? 'Editando' : 'Visualização'}
                  </Typography>
                </Box>
              }
              sx={{ m: 0 }}
            />
          </Tooltip>
        </Box>
      )}

      {/* Aviso sobre modo de edição quando ativo - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && editMode && (
        <Alert 
          severity="info" 
          sx={{ 
            mx: 2, 
            mt: 1, 
            borderRadius: 2,
            '& .MuiAlert-message': { py: 0.5 }
          }}
        >
          <Typography variant="body2">
            <strong>✏️ Modo de Edição Ativo:</strong> Você pode adicionar, mover, redimensionar e remover objetos.
          </Typography>
        </Alert>
      )}

      {/* Grid Layout Dinâmico - ocupa o espaço restante */}
      <Box sx={{ flex: 1, p: 2, pt: 1}}>
        <GridLayout 
          pagina={chave || 'default'} 
          appId={appId}
          editMode={process.env.NODE_ENV === 'development' ? editMode : false}
        />
      </Box>

      {/* Informações adicionais - apenas em modo de edição e desenvolvimento */}
      {process.env.NODE_ENV === 'development' && editMode && (
        <Box sx={{ p: 2, pt: 0 }}>
          <Alert severity="info" sx={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
            color: 'white',
            '& .MuiAlert-icon': { color: 'white' }
          }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
              💡 Como usar a Página Dinâmica - {paginaAtual?.titulo || chave}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9, fontSize: '0.875rem' }}>
              • <strong>Adicionar:</strong> Use o botão "+" para adicionar objetos Qlik •{' '}
              <strong>Mover:</strong> Arraste pelo ícone de mover •{' '}
              <strong>Redimensionar:</strong> Arraste as bordas •{' '}
              <strong>Remover:</strong> Use o ícone de lixeira
            </Typography>
          </Alert>
        </Box>
      )}
    </Box>
  )
}

export default DynamicPage 