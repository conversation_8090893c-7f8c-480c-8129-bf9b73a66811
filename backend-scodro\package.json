{"name": "backend-scodro", "version": "1.0.0", "description": "Backend para Mashup Qlik - Gerenciamento de Empresas e Objetos", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "seed": "node src/scripts/seedDatabase.js", "seed:clear": "node src/scripts/seedDatabase.js --clear", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["qlik", "mashup", "mongodb", "express", "api"], "author": "SCODRO Team", "license": "MIT", "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "mongoose": "^8.15.1", "morgan": "^1.10.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}