# 🚀 <PERSON><PERSON><PERSON> - Backend SCODRO

## ⚡ Configuração em 5 minutos

### 1. Instalar Dependências
```bash
npm install
```

### 2. Configurar Variáveis de Ambiente
```bash
# Copiar arquivo de exemplo
copy env.example .env

# Editar .env com suas configurações
# Principalmente a MONGO_URI se necessário
```

### 3. Popular Banco com Dados de Exemplo
```bash
# Primeira vez (criar dados)
npm run seed

# Limpar e recriar dados
npm run seed:clear
```

### 4. Iniciar Servidor
```bash
# Desenvolvimento (com auto-reload)
npm run dev

# Produção
npm start
```

## 🔗 Endpoints Principais

### Health Check
```
GET http://localhost:3031/api/health
```

### Empresas
```
GET    http://localhost:3031/api/empresas
POST   http://localhost:3031/api/empresas
GET    http://localhost:3031/api/empresas/:id/config
```

### Objetos
```
GET    http://localhost:3031/api/objetos/:empresaId
POST   http://localhost:3031/api/objetos/:empresaId
```

## 🧪 Teste Rápido

```bash
# Health check
curl http://localhost:3031/api/health

# Listar empresas
curl http://localhost:3031/api/empresas

# Criar empresa
curl -X POST http://localhost:3031/api/empresas \
  -H "Content-Type: application/json" \
  -d '{
    "id": "minha-empresa",
    "nome": "Minha Empresa",
    "appId": "APP_ID_QLIK"
  }'
```

## 🔧 Configuração do MongoDB

Se houver problemas de conexão, verifique:

1. **String de conexão**: Confirme se a MONGO_URI está correta
2. **Credenciais**: Verifique usuário e senha
3. **Nome do cluster**: Pode ser diferente de "cluster0"
4. **Whitelist IP**: Adicione seu IP no MongoDB Atlas

### Formato da String de Conexão
```
mongodb+srv://usuario:<EMAIL>/nome_banco?retryWrites=true&w=majority
```

## 📞 Problemas Comuns

### ❌ Erro de conexão MongoDB
- Verifique a string MONGO_URI
- Confirme se o IP está na whitelist
- Teste a conexão no MongoDB Compass

### ❌ Porta já em uso
- Mude a PORT no .env
- Ou pare outros serviços na porta 3031

### ❌ Dependências não instaladas
```bash
rm -rf node_modules package-lock.json
npm install
```

## 🎯 Próximos Passos

1. ✅ Configure a MONGO_URI correta
2. ✅ Execute o seed para ter dados de exemplo
3. ✅ Teste os endpoints principais
4. ✅ Integre com o frontend
5. ✅ Configure IDs reais do Qlik

---

**Documentação completa**: `README.md` 