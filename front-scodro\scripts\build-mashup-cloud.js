const fs = require('fs-extra')
const path = require('path')

async function buildMashupCloud() {
  console.log('☁️ Gerando build do mashup para Qlik Cloud...')
  
  try {
    // Diretórios
    const buildDir = path.join(__dirname, '..', 'dist')
    const mashupDir = path.join(__dirname, '..', 'mashup-cloud')
    
    // Limpar diretório anterior
    await fs.remove(mashupDir)
    await fs.ensureDir(mashupDir)
    
    // Copiar arquivos do build
    await fs.copy(buildDir, mashupDir)
    
    // Modificar index.html para Cloud
    const indexPath = path.join(mashupDir, 'index.html')
    let indexContent = await fs.readFile(indexPath, 'utf8')
    
    // Adicionar configuração específica para Cloud que carrega do backend
    const cloudScript = `    <!-- Estilos CSS do Qlik Cloud (será preenchido dinamicamente) -->
    <link rel="stylesheet" id="qlik-styles" href="#">
    
    <!-- CSS de sobrescrita de fontes - DEVE VIR APÓS qlik-styles.css -->
    <style>
      /* FORÇAR ROBOTO - SOBRESCREVER QLIK STYLES CLOUD */
      /* Garantir que Roboto seja aplicada mesmo após o Qlik Cloud carregar seus estilos */
      body, body *, html, html * {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
      
      /* Sobrescrever especificamente os seletores do Qlik Cloud */
      [class*="qlik"], [class*="qlik"] *,
      [id*="qlik"], [id*="qlik"] *,
      .qlik-embed-object, .qlik-embed-object *,
      .qlik-object, .qlik-object * {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
      
      /* Forçar em elementos SVG (gráficos) */
      svg, svg *, svg text, svg tspan {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
      
      /* Material-UI específico */
      .MuiTypography-root, .MuiCard-root, .MuiButton-root,
      .MuiTextField-root, .MuiMenuItem-root, .MuiTab-root {
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif !important;
      }
    </style>
    
    <!-- RequireJS para Qlik Cloud (será preenchido dinamicamente) -->
    <script id="qlik-require" src="#"></script>
    
    <script>
    // Função para carregar configuração do backend e aplicar recursos do Qlik
    async function loadQlikCloudConfig() {
      try {
        // Carregar configuração do backend
        const response = await fetch('/api/config-mashup/ativo');
        const data = await response.json();
        
        if (data.success && data.data) {
          const config = data.data;
          
          // Verificar se é ambiente Cloud e se tem configuração
          if (config.configuracoes.ambiente === 'cloud' && config.qlikCloud.tenantUrl) {
            const tenantUrl = config.qlikCloud.tenantUrl;
            const webIntegrationId = config.qlikCloud.webIntegrationId;
            
            // Atualizar links CSS e RequireJS dinamicamente
            const stylesLink = document.getElementById('qlik-styles');
            const requireScript = document.getElementById('qlik-require');
            
            stylesLink.href = tenantUrl + '/resources/autogenerated/qlik-styles.css';
            requireScript.src = tenantUrl + '/resources/assets/external/requirejs/require.js';
            
            // Configurar variáveis globais
            window.QLIK_MASHUP_CONFIG = {
              environment: 'cloud',
              cloudConfig: {
                host: tenantUrl.replace(/^https?:\\/\\//, ''),
                prefix: '/',
                port: 443,
                isSecure: true,
                webIntegrationId: webIntegrationId
              }
            };
            
            // Aguardar carregamento do RequireJS e configurar
            requireScript.onload = function() {
              if (window.require) {
                require.config({
                  baseUrl: tenantUrl + '/resources',
                  webIntegrationId: webIntegrationId
                });
              }
            };
            
            console.log('✅ Configuração Qlik Cloud carregada:', {
              tenant: tenantUrl,
              webIntegrationId: webIntegrationId ? webIntegrationId.substring(0, 8) + '...' : 'não configurado'
            });
          } else {
            console.warn('⚠️ Configuração Cloud não encontrada ou incompleta');
          }
        }
      } catch (error) {
        console.error('❌ Erro ao carregar configuração Cloud:', error);
        // Fallback para configuração manual
        console.log('💡 Configure manualmente editando os recursos Qlik neste arquivo');
      }
    }
    
    // Função para forçar fonte Roboto em todos os elementos (Cloud)
    /*function forceRobotoFontCloud() {
      console.log('🔤 [Cloud] Forçando fonte Roboto em todos os elementos...');
      
      const robotoFamily = "'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif";
      
      // Aplicar Roboto em todos os elementos existentes
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        if (el.style) {
          el.style.setProperty('font-family', robotoFamily, 'important');
        }
      });
      
      // Aplicar especificamente em elementos SVG (gráficos)
      const svgTexts = document.querySelectorAll('svg text, svg tspan');
      svgTexts.forEach(el => {
        if (el.style) {
          el.style.setProperty('font-family', robotoFamily, 'important');
        }
      });
      
    }*/
    
    // Carregar configuração quando o DOM estiver pronto
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function() {
        loadQlikCloudConfig();
        // Aplicar fontes após carregar configuração
        //setTimeout(forceRobotoFontCloud, 1000);
      });
    } else {
      loadQlikCloudConfig();
      // Aplicar fontes imediatamente
      //forceRobotoFontCloud();
    }
    
    // Executar força de fonte periodicamente para capturar elementos dinâmicos do Qlik Cloud
    //setInterval(forceRobotoFontCloud, 3000);
    
    // Observer para novos elementos DOM (criados pelo Qlik Cloud dinamicamente)
    /*const observer = new MutationObserver((mutations) => {
      let shouldForceFont = false;
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === 1) { // Element node
              shouldForceFont = true;
            }
          });
        }
      });
      
      if (shouldForceFont) {
        setTimeout(forceRobotoFontCloud, 100);
      }
    });*/
    
    // Iniciar observação quando o body estiver disponível
    if (document.body) {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    } else {
      document.addEventListener('DOMContentLoaded', function() {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
      });
    }
    </script>
  `
    
    // Adicionar antes do </head>
    indexContent = indexContent.replace('</head>', cloudScript + '</head>')
    
    await fs.writeFile(indexPath, indexContent)
    
    // Função recursiva para listar todos os arquivos
    async function getAllFiles(dir, basePath = '') {
      const files = []
      const items = await fs.readdir(dir)
      
      for (const item of items) {
        const fullPath = path.join(dir, item)
        const relativePath = basePath ? `${basePath}/${item}` : item
        const stat = await fs.stat(fullPath)
        
        if (stat.isDirectory()) {
          const subFiles = await getAllFiles(fullPath, relativePath)
          files.push(...subFiles)
        } else {
          files.push(relativePath)
        }
      }
      
      return files
    }
    
    // Gerar arquivo wbfolder.wbl
    const allFiles = await getAllFiles(mashupDir)
    // Filtrar apenas arquivos relevantes
    const relevantFiles = allFiles.filter(file => 
      !file.includes('README.md') && 
      !file.includes('wbfolder.wbl') &&
      !file.includes('config.js')
    )
    
    // Adicionar ./ para arquivos em subpastas
    const wbfolderContent = relevantFiles
      .map(file => {
        if (file.includes('/')) {
          return `./${file};`
        }
        return `${file};`
      })
      .join('\\n')
    
    await fs.writeFile(path.join(mashupDir, 'wbfolder.wbl'), wbfolderContent)
    
    // Criar arquivo de configuração de exemplo (para configuração manual se necessário)
    const configTemplate = `// config-manual.js - Use apenas se não conseguir carregar do backend
// Configure na página de Configurações ao invés de usar este arquivo

window.QLIK_CLOUD_CONFIG_MANUAL = {
  tenant: 'https://seu-tenant.qlikcloud.com',
  webIntegrationId: 'seu-web-integration-id'
};

// Aplicar configuração manual (descomente se necessário):
/*
document.addEventListener('DOMContentLoaded', function() {
  const config = window.QLIK_CLOUD_CONFIG_MANUAL;
  
  // Atualizar links
  document.getElementById('qlik-styles').href = config.tenant + '/resources/autogenerated/qlik-styles.css';
  document.getElementById('qlik-require').src = config.tenant + '/resources/assets/external/requirejs/require.js';
  
  // Configurar variáveis globais
  window.QLIK_MASHUP_CONFIG = {
    environment: 'cloud',
    cloudConfig: {
      host: config.tenant.replace(/^https?:\\/\\//, ''),
      prefix: '/',
      port: 443,
      isSecure: true,
      webIntegrationId: config.webIntegrationId
    }
  };
});
*/`
    
    await fs.writeFile(path.join(mashupDir, 'config-manual.js'), configTemplate)
    
    // Criar arquivo de instruções detalhadas
    const readme = `# SCODRO Mashup - Qlik Cloud

## ✨ Configuração Automática (Recomendado)

Este build usa **configuração automática** do backend!

### Pré-requisitos:
1. ✅ Backend SCODRO rodando e acessível
2. ✅ Configuração Cloud preenchida na página de Configurações
3. ✅ Web Integration ID configurado no Qlik Cloud

### Passos:
1. **Configure no sistema**: Acesse as Configurações do mashup e preencha:
   - Ambiente: "Qlik Cloud"
   - URL do Tenant: Ex: https://metricaplus.us.qlikcloud.com
   - Web Integration ID: (obtido no Management Console)

2. **Hospede os arquivos**: Faça upload desta pasta para seu servidor HTTPS

3. **Configure CORS**: Adicione seu domínio na whitelist do Web Integration

4. **Teste**: Acesse https://seu-servidor.com/scodro-mashup/

---

## 🔧 Configuração Manual (Fallback)

Se a configuração automática não funcionar:

1. **Edite config-manual.js** com suas configurações
2. **Inclua no index.html**: Adicione \`<script src="./config-manual.js"></script>\`
3. **Descomente** o código de aplicação manual

---

## 📋 Web Integration ID - Como obter:

1. Acesse o **Qlik Cloud Management Console**
2. Vá em **Settings > Web integrations** 
3. Clique em **Create new**
4. Configure:
   - **Name**: Nome do seu mashup
   - **Whitelist origins**: https://seu-dominio.com
   - **CSP origins**: https://seu-dominio.com
5. **Copie o ID gerado** e configure no sistema

---

## 🌐 Recursos incluídos:

✅ **Carregamento automático** de CSS e RequireJS do Qlik
✅ **Configuração dinâmica** baseada no backend
✅ **Fallback manual** para casos especiais
✅ **Logs detalhados** para debug

## 📁 Estrutura:
- **index.html**: Página principal com carregamento automático
- **config-manual.js**: Configuração manual (backup)
- **assets/**: Arquivos CSS e JS do React
- **wbfolder.wbl**: Lista de arquivos do mashup

## 🐛 Resolução de problemas:

**Erro "Failed to load resource":**
- Verifique se o backend está rodando
- Confirme a configuração na página de Configurações

**Objetos não carregam:**
- Verifique o Web Integration ID
- Confirme que o domínio está na whitelist
- Verifique se está em HTTPS

**Console mostra warnings:**
- Normal durante o carregamento inicial
- Verifique os logs para configuração aplicada

Gerado em: ${new Date().toLocaleString()}
`
    
    await fs.writeFile(path.join(mashupDir, 'README.md'), readme)
    
    console.log('✅ Build do mashup Cloud gerado com sucesso!')
    console.log('📁 Pasta: ' + mashupDir)
    console.log('📋 Recursos aplicados:')
    console.log('   ✅ CSS de sobrescrita de fontes Roboto')
    console.log('   ✅ Scripts de força de fonte JavaScript')
    console.log('   ✅ Observer para elementos dinâmicos')
    console.log('   ✅ Configuração automática do backend')
    console.log('')
    console.log('📝 Próximos passos:')
    console.log('   1. Configure Qlik Cloud nas Configurações do sistema')
    console.log('   2. Configure Web Integration ID no Qlik Cloud')
    console.log('   3. Hospede em servidor HTTPS')
    console.log('   4. Adicione domínio na whitelist')
    
  } catch (error) {
    console.error('❌ Erro ao gerar build do mashup:', error)
    process.exit(1)
  }
}

buildMashupCloud() 