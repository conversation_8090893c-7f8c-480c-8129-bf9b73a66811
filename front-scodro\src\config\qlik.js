// Configurações de conexão para Qlik Cloud e Enterprise

// Configuração para Qlik Cloud
export const cloudConfig = {
  host: 'SEU_QSE_CLOUD_DOMAIN.qlikcloud.com', // Ex: 'your-tenant.us.qlikcloud.com'
  prefix: '/',
  port: 443,
  isSecure: true,
  webIntegrationId: 'SEU_WEB_INTEGRATION_ID' // Obtenha no Management Console do Qlik Cloud
}

// Configuração para Qlik Enterprise/Desktop (será detectada automaticamente)
export const getEnterpriseConfig = () => {
  const location = window.location
  return {
    host: location.hostname,
    prefix: location.pathname.substring(0, location.pathname.toLowerCase().lastIndexOf('/extensions') + 1),
    port: location.port || (location.protocol === 'https:' ? 443 : 80),
    isSecure: location.protocol === 'https:'
  }
}

// Detecção automática do ambiente
export const detectEnvironment = () => {
  // Verifica se está em ambiente Enterprise/Desktop (presença de /extensions na URL)
  if (window.location.pathname.toLowerCase().includes('/extensions')) {
    return 'enterprise'
  }
  
  // Verifica se está em ambiente Cloud (presença de webIntegrationId configurado)
  if (cloudConfig.webIntegrationId && cloudConfig.webIntegrationId !== 'SEU_WEB_INTEGRATION_ID') {
    return 'cloud'
  }
  
  // Ambiente de desenvolvimento
  return 'development'
}

// Obter configuração baseada no ambiente
export const getQlikConfig = () => {
  const environment = detectEnvironment()
  
  switch (environment) {
    case 'cloud':
      return { ...cloudConfig, environment }
    case 'enterprise':
      return { ...getEnterpriseConfig(), environment }
    default:
      return {
        environment: 'development',
        host: 'localhost',
        port: 3333,
        isSecure: false,
        prefix: '/'
      }
  }
}

// URLs base para recursos do Qlik
export const getQlikResourcesUrl = (config) => {
  const protocol = config.isSecure ? 'https' : 'http'
  return `${protocol}://${config.host}:${config.port}${config.prefix}resources`
}

// Configuração do require.config para cada ambiente
export const getRequireConfig = (config) => {
  const baseConfig = {
    baseUrl: getQlikResourcesUrl(config),
    paths: {
      qlik: 'js/qlik'
    }
  }

  // Adiciona webIntegrationId para Cloud
  if (config.environment === 'cloud') {
    baseConfig.webIntegrationId = config.webIntegrationId
  }

  return baseConfig
}

// Funções para alternar ambiente
export const setQlikEnvironment = (environment) => {
  window.localStorage.setItem('qlik-env', environment)
  console.log(`🔄 Ambiente Qlik alterado para: ${environment}`)
  // Recarregar página para aplicar mudanças
  window.location.reload()
}

export const getQlikEnvironment = () => {
  return detectEnvironment()
}

export const clearQlikEnvironment = () => {
  window.localStorage.removeItem('qlik-env')
  console.log('🗑️ Configuração de ambiente Qlik removida')
} 