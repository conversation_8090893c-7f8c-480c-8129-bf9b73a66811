import React, { useState, useEffect } from 'react'
import { Responsive, WidthProvider } from 'react-grid-layout'
import {
  Box,
  Card,
  CardContent,
  IconButton,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Tooltip,
  Alert,
  CircularProgress,
  Grid,
  Paper,
  Divider
} from '@mui/material'
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  DragIndicator as DragIcon,
  ViewModule as ObjectIcon
} from '@mui/icons-material'
import QlikObject from '../QlikObject/QlikObject'
import { objetoService } from '@services/api'
import { useEmpresa } from '@context/EmpresaContext'
import { ICON_GALLERY, COLOR_PALETTE } from '../CustomKPI/CustomKPI'
import 'react-grid-layout/css/styles.css'
import 'react-resizable/css/styles.css'
import './GridLayout.css'

const ResponsiveGridLayout = WidthProvider(Responsive)

// Categorias disponíveis para objetos (mesmo que em Configuracoes.jsx)
const CATEGORIAS_OBJETO = [
  { value: 'dashboard', label: 'Dashboard' },
  { value: 'vendas', label: 'Vendas' },
  { value: 'helpdesk', label: 'Helpdesk' },
  { value: 'financeiro', label: 'Financeiro' },
  { value: 'rh', label: 'RH' },
  { value: 'operacional', label: 'Operacional' },
  { value: 'geral', label: 'Geral (aparece em todas as páginas)' }
]

const GridLayout = ({ pagina, appId, editMode = false }) => {
  const { empresaSelecionada, empresaAtual } = useEmpresa()
  const [layouts, setLayouts] = useState({})
  const [items, setItems] = useState([])
  const [loading, setLoading] = useState(true)
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [availableObjects, setAvailableObjects] = useState([])
  const [newItem, setNewItem] = useState({
    nome: '',
    objectId: '',
    tipo: 'kpi',
    categoria: pagina
  })

  // Detectar se está em modo de desenvolvimento
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Configurações do grid melhoradas
  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }
  const cols = { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }

  // Estado para forçar re-render dos KPIs após resize
  const [kpiRenderKey, setKpiRenderKey] = useState(0)
  const [currentBreakpoint, setCurrentBreakpoint] = useState('lg')

  // Função para obter o appId real do Qlik baseado no _id do MongoDB
  const getQlikAppId = () => {
    // Se não há empresa atual ou appId, retornar null
    if (!empresaAtual || !appId) return null
    
    // Verificar se appId é um ObjectId válido do MongoDB (24 caracteres hex)
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
    
    if (isValidObjectId && Array.isArray(empresaAtual.apps)) {
      // Procurar o app pelo _id
      const app = empresaAtual.apps.find(app => app._id === appId)
      if (app && app.appId) {
        return app.appId
      }
    }
    
    // Se não é um ObjectId válido ou não foi encontrado, retornar o valor original
    //console.log('⚠️ Usando appId original (não é um _id do MongoDB):', appId)
    return appId
  }

  // Carregar objetos da página
  useEffect(() => {
    if (empresaSelecionada && appId) {
      // Verificar se appId é um ObjectId válido do MongoDB (24 caracteres hex)
      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
      
      if (isValidObjectId) {
        //console.log('✅ AppId é um ObjectId válido:', appId)
        carregarObjetos()
      } else {
        //console.log('⚠️ AppId não é um ObjectId válido:', appId)
        //console.log('📝 Isso é normal se for um fallback. Objetos não serão carregados do backend.')
        setLoading(false)
        setItems([]) // Limpar items se não é um ID válido
      }
    } else {
      setLoading(false)
    }
  }, [empresaSelecionada, appId, pagina])

  const carregarObjetos = async () => {
    try {
      setLoading(true)
      //console.log('🔍 Carregando objetos para:', { empresaSelecionada, appId, pagina })
      
      // Carregar TODOS os objetos ativos do app
      const response = await objetoService.listarObjetosPorApp(
        empresaSelecionada, 
        appId, 
        { ativo: true }
      )

      //console.log('📦 Resposta dos objetos:', response)

      if (response.success && response.data) {
        // Filtrar apenas objetos que estão configurados para aparecer nesta página
        const objetosDaPagina = response.data.filter(obj => {
          // Verificar se o objeto tem configuração para esta página
          return obj.configuracao?.paginas && obj.configuracao.paginas.includes(pagina)
        })

        //console.log(`✅ ${objetosDaPagina.length} objetos encontrados para a página ${pagina}`)

        if (objetosDaPagina.length > 0) {

          const objetosComLayout = objetosDaPagina.map(obj => {
            const layoutSalvo = obj.configuracao?.layouts?.[pagina]
            const defaultW = getDefaultWidth(obj.tipo)
            const defaultH = getDefaultHeight(obj.tipo)
            const minW = getMinWidth(obj.tipo)
            const minH = getMinHeight(obj.tipo)
            
            // Para charts, garantir que sempre respeitam o tamanho mínimo
            let finalW = layoutSalvo?.w || defaultW
            let finalH = layoutSalvo?.h || defaultH
            
            
            if (obj.tipo === 'chart') {
              finalW = Math.max(finalW, minW) // Garantir tamanho mínimo
              finalH = Math.max(finalH, minH) // Garantir tamanho mínimo
            }
            
            return {
              ...obj,
              i: obj._id,
              x: layoutSalvo?.x || 0,
              y: layoutSalvo?.y || 0,
              w: finalW,
              h: finalH,
              minW: minW,
              minH: minH
            }
          })

          setItems(objetosComLayout)
          
          // Gerar layouts responsivos para diferentes breakpoints
          const generatedLayouts = {}
          Object.keys(breakpoints).forEach(bp => {
            //console.log(`📱 Gerando layout para breakpoint: ${bp}`)
            
            generatedLayouts[bp] = objetosComLayout.map((item, index) => {
              // Usar layout salvo se existir para o breakpoint lg, senão calcular responsivo
              const layoutSalvo = item.configuracao?.layouts?.[pagina]
              
              let finalW, finalH, finalX, finalY
              
              if (bp === 'lg' && layoutSalvo) {
                // No breakpoint lg, usar layout salvo se disponível
                finalW = layoutSalvo.w
                finalH = layoutSalvo.h
                finalX = layoutSalvo.x
                finalY = layoutSalvo.y
              } else {
                // Para outros breakpoints ou sem layout salvo, calcular responsivo
                finalW = getResponsiveWidth(item.tipo, bp, item.w)
                finalH = getResponsiveHeight(item.tipo, bp, item.h)
                
                // Organizar em grid automático para breakpoints menores
                if (bp === 'xs' || bp === 'xxs') {
                  // Em mobile, começar todos em x=0 e deixar compactação organizar
                  finalX = 0
                  finalY = 0 // Deixar a compactação vertical organizar
                } else if (bp === 'sm') {
                  // Em tablet, iniciar em posições base e deixar compactação organizar
                  const colsPerRow = 2
                  finalX = (index % colsPerRow) * (cols[bp] / colsPerRow)
                  finalY = 0 // Deixar a compactação vertical organizar
                } else {
                  // Para md e lg sem layout salvo, posição base e compactação organiza
                  const colsPerRow = bp === 'md' ? 2 : 3
                  const colWidth = Math.floor(cols[bp] / colsPerRow)
                  finalX = (index % colsPerRow) * colWidth
                  finalY = 0 // Deixar a compactação vertical organizar automaticamente
                }
              }
              
              const layout = {
                i: item.i,
                x: finalX,
                y: finalY,
                w: finalW,
                h: finalH,
                minW: getMinWidth(item.tipo),
                minH: getMinHeight(item.tipo)
              }
              
              //console.log(`🎯 Layout ${bp} para ${item.nome}:`, layout)
              return layout
            })
          })
          
          //console.log('📐 LAYOUTS_RESPONSIVOS_GERADOS:', generatedLayouts)
          setLayouts(generatedLayouts)
        } else {
          //console.log('ℹ️ Nenhum objeto configurado para esta página')
          setItems([])
          setLayouts({})
        }
      } else {
        setItems([])
        setLayouts({})
      }
    } catch (error) {
      console.error('❌ Erro ao carregar objetos:', error)
      setItems([])
      setLayouts({})
    } finally {
      setLoading(false)
    }
  }

  // Carregar objetos disponíveis para adicionar
  const carregarObjetosDisponiveis = async () => {
    try {
      //console.log('🔍 Carregando objetos disponíveis...')
      
      // Verificar se appId é válido antes de fazer chamada
      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
      if (!isValidObjectId) {
        //console.log('⚠️ AppId inválido para carregar objetos disponíveis:', appId)
        setAvailableObjects([])
        return
      }
      
      // Carregar TODOS os objetos ativos do app
      const response = await objetoService.listarObjetosPorApp(
        empresaSelecionada, 
        appId, 
        { ativo: true }
      )

      //console.log('📋 Objetos disponíveis:', response)

      if (response.success && response.data) {
        
        // Filtrar objetos que NÃO estão na página atual
        const objetosDisponiveis = response.data.filter(obj => {
          // Mostrar objetos que não têm configuração para esta página
          const paginasDoObjeto = obj.configuracao?.paginas || []
          return !paginasDoObjeto.includes(pagina)
        })
      
        setAvailableObjects(objetosDisponiveis)
      }
    } catch (error) {
      console.error('❌ Erro ao carregar objetos disponíveis:', error)
      setAvailableObjects([])
    }
  }

  // Dimensões padrão por tipo de objeto
  const getDefaultWidth = (tipo) => {
    const widths = {
      kpi: 3,
      chart: 6,
      table: 8,
      filter: 4
    }
    return widths[tipo] || 4
  }

  const getDefaultHeight = (tipo) => {
    const heights = {
      kpi: 3,
      chart: 6,
      table: 8,
      filter: 2
    }
    return heights[tipo] || 4
  }

  const getMinWidth = (tipo) => {
    // Nova lógica: tudo que não for KPI precisa de tamanho mínimo maior
    if (tipo === 'kpi') {
      //console.log(`📏 getMinWidth para KPI: 1`)
      return 1 // KPIs podem ser pequenos
    } else if (tipo === 'multiKpi') {
      //console.log(`📏 getMinWidth para MultiKPI: 1`)
      return 2 // MultiKPIs podem ser pequenos
    } else {
      //console.log(`📏 getMinWidth para ${tipo} (não-KPI): 8`)
      return 5 // Gráficos, tabelas, filtros, etc. precisam ser maiores
    }
  }

  const getMinHeight = (tipo) => {
    // Nova lógica: tudo que não for KPI precisa de tamanho mínimo maior
    if (tipo === 'kpi' || tipo === 'filter') {
      //console.log(`📏 getMinHeight para KPI: 1`)
      return 1 // KPIs podem ser pequenos
    } 
    else if (tipo === 'multiKpi' || tipo === 'table') {
      //console.log(`📏 getMinHeight para MultiKPI: 1`)
      return 3 // MultiKPIs podem ser pequenos
    }
    else {
      //console.log(`📏 getMinHeight para ${tipo} (não-KPI): 6`)
      return 4 // Gráficos, tabelas, filtros, etc. precisam ser maiores
    }
  }

  // Manipular mudanças no layout com responsividade
  const handleLayoutChange = async (layout, layouts) => {
    setLayouts(layouts)
    
    // Verificar se appId é válido antes de salvar no backend
    const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
    if (!isValidObjectId) {
      //console.log('⚠️ Não é possível salvar layout - AppId inválido:', appId)
      return
    }
    
    // Detectar breakpoint atual baseado na largura da tela
    const getCurrentBreakpoint = () => {
      const width = window.innerWidth
      if (width >= breakpoints.lg) return 'lg'
      if (width >= breakpoints.md) return 'md'
      if (width >= breakpoints.sm) return 'sm'
      if (width >= breakpoints.xs) return 'xs'
      return 'xxs'
    }
    
    const currentBreakpoint = getCurrentBreakpoint()
    //console.log(`💾 Salvando layout para breakpoint: ${currentBreakpoint}, largura: ${window.innerWidth}px`)
    
    // Salvar layout no backend apenas para lg (desktop) para manter como base
    // Os outros breakpoints são calculados automaticamente
    if (currentBreakpoint === 'lg') {
      try {
        const updates = layout.map(item => {
          const objeto = items.find(obj => obj._id === item.i)
          if (objeto) {
            // Manter layouts existentes e atualizar apenas o da página atual
            const layoutsAtuais = objeto.configuracao?.layouts || {}
            const novosLayouts = {
              ...layoutsAtuais,
              [pagina]: {
                x: item.x,
                y: item.y,
                w: item.w,
                h: item.h
              }
            }

            return {
              _id: objeto._id,
              configuracao: {
                ...objeto.configuracao,
                layouts: novosLayouts
              }
            }
          }
          return null
        }).filter(Boolean)

        //console.log(`💾 Salvando layouts de ${updates.length} objetos para página ${pagina} (breakpoint: ${currentBreakpoint})`)

        // Atualizar objetos no backend
        for (const update of updates) {
          await objetoService.atualizarObjeto(
            empresaSelecionada,
            appId,
            update._id,
            update
          )
        }
        
        //console.log('✅ Layouts salvos com sucesso no backend')
      } catch (error) {
        console.error('❌ Erro ao salvar layout:', error)
      }
    } else {
      //console.log(`ℹ️ Layout não salvo no backend (breakpoint: ${currentBreakpoint} - apenas lg é salvo)`)
    }
  }

  // Handler para quando o resize terminar - NOVA FUNÇÃO
  const handleResizeStop = (layout, oldItem, newItem, placeholder, e, element) => {
    
    // Encontrar o objeto correspondente
    const objeto = items.find(item => item._id === newItem.i)
    
    // Aplicar tamanhos mínimos forçadamente se for chart (não-KPI)
    if (objeto && objeto.tipo !== 'kpi') {
      const minW = getMinWidth(objeto.tipo)
      const minH = getMinHeight(objeto.tipo)
      
      let forçouResize = false
      
      // Se o novo tamanho for menor que o mínimo, forçar o mínimo
      if (newItem.w < minW) {
        newItem.w = minW
        forçouResize = true
      }
      if (newItem.h < minH) {
        newItem.h = minH
        forçouResize = true
      }
      
      if (forçouResize) {
        // Atualizar o layout atual com o tamanho forçado
        const layoutAtualizado = layout.map(item => {
          if (item.i === newItem.i) {
            return { ...item, w: newItem.w, h: newItem.h }
          }
          return item
        })
        
        // Atualizar os layouts state
        const novosLayouts = { ...layouts }
        Object.keys(novosLayouts).forEach(bp => {
          novosLayouts[bp] = novosLayouts[bp].map(item => {
            if (item.i === newItem.i) {
              return { ...item, w: newItem.w, h: newItem.h }
            }
            return item
          })
        })
        
        setLayouts(novosLayouts)
        
        // Salvar no backend
        if (editMode) {
          handleLayoutChange(layoutAtualizado, novosLayouts)
        }
      }
    }
    
    // Forçar re-render dos KPIs após resize
    setKpiRenderKey(prev => prev + 1)
    
    // Salvar layout normalmente se não forçou resize
    if (editMode && (!objeto || objeto.tipo !== 'chart' || !forçouResize)) {
      handleLayoutChange(layout, { ...layouts, [Object.keys(layouts)[0] || 'lg']: layout })
    }
  }

  // Abrir dialog para adicionar objeto
  const handleAddObject = () => {
    //console.log('➕ Abrindo dialog para adicionar objeto')
    carregarObjetosDisponiveis()
    setEditingItem(null)
    
    // Resetar completamente o estado
    setNewItem({
      nome: '',
      objectId: '',
      tipo: 'kpi',
      categoria: pagina
    })
    setDialogOpen(true)
  }

  // Função para calcular próxima posição Y disponível (no final da página)
  const getNextYPosition = () => {
    if (items.length === 0) return 0

    // Encontrar a posição Y máxima considerando todas as posições + alturas dos objetos existentes
    let maxY = 0
    items.forEach(item => {
      const layout = item.configuracao?.layouts?.[pagina]
      if (layout) {
        const bottomY = layout.y + layout.h
        if (bottomY > maxY) {
          maxY = bottomY
        }
      }
    })

    // Adicionar uma margem de 1 linha entre o último objeto e o novo
    return maxY + 1
  }

  // Adicionar objeto existente à página
  const handleAddExistingObject = async () => {
    try {
      const objetoSelecionado = availableObjects.find(obj => obj._id === newItem.objectId)
      if (!objetoSelecionado) return

      //console.log('📌 Adicionando objeto existente à página:', objetoSelecionado)

      // Verificar se appId é válido
      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
      if (!isValidObjectId) {
        //console.log('⚠️ Não é possível adicionar objeto - AppId inválido:', appId)
        alert('Não é possível adicionar objetos sem um app válido configurado.')
        return
      }

      // Adicionar a página atual à lista de páginas do objeto
      const paginasAtuais = objetoSelecionado.configuracao?.paginas || []
      const novasPaginas = [...new Set([...paginasAtuais, pagina])]
      
      // Calcular próxima posição Y disponível (no final da página)
      const nextY = getNextYPosition()
      
      // Manter layouts existentes e criar layout padrão para nova página
      const layoutsAtuais = objetoSelecionado.configuracao?.layouts || {}
      const novosLayouts = {
        ...layoutsAtuais,
        [pagina]: layoutsAtuais[pagina] || {
          x: 0,
          y: nextY, // ✅ Posicionar no final da página
          w: getDefaultWidth(objetoSelecionado.tipo),
          h: getDefaultHeight(objetoSelecionado.tipo)
        }
      }

      // Aplicar configurações padrão se não existirem
      let kpiConfig = objetoSelecionado.configuracao?.kpiConfig || {}
      let multiKpiConfig = objetoSelecionado.configuracao?.multiKpiConfig || {}
      let chartConfig = objetoSelecionado.configuracao?.chartConfig || {}

      // Configurações padrão para Multi KPI se não existirem
      if (objetoSelecionado.tipo === 'multiKpi' && (!objetoSelecionado.configuracao?.multiKpiConfig || Object.keys(objetoSelecionado.configuracao.multiKpiConfig).length === 0)) {
        //console.log('🎨 Aplicando configurações padrão do Multi KPI ao adicionar à página')
        multiKpiConfig = {
          itemsPerRow: 3,
          spacing: 2,
          showTitle: true,
          showIcon: true,
          layout: 'unified',
          elevation: 1,
          cor: '#1976d2',
          corFonte: '#ffffff'
        }
      }

      // Configurações padrão para KPI se não existirem
      if (objetoSelecionado.tipo === 'kpi' && (!objetoSelecionado.configuracao?.kpiConfig || Object.keys(objetoSelecionado.configuracao.kpiConfig).length === 0)) {
        //console.log('🎨 Aplicando configurações padrão do KPI ao adicionar à página')
        kpiConfig = {
          formato: 'numero',
          casasDecimais: 0,
          prefix: '',
          suffix: '',
          showTrend: false,
          cor: '#1976d2',
          corFonte: '#ffffff',
          icone: 'analytics',
          showIcon: true,
          gradiente: false
        }
      }

      const updateData = {
        configuracao: {
          // Manter todas as configurações existentes
          altura: objetoSelecionado.configuracao?.altura || "auto",
          largura: objetoSelecionado.configuracao?.largura || "100%",
          showTitle: objetoSelecionado.configuracao?.showTitle !== false,
          showToolbar: objetoSelecionado.configuracao?.showToolbar !== false,
          allowInteraction: objetoSelecionado.configuracao?.allowInteraction !== false,
          allowExport: objetoSelecionado.configuracao?.allowExport || false,
          // Aplicar configurações com defaults
          kpiConfig: kpiConfig,
          multiKpiConfig: multiKpiConfig,
          chartConfig: chartConfig,
          // Adicionar campos novos
          paginas: novasPaginas,
          layouts: novosLayouts
        }
      }

      await objetoService.atualizarObjeto(
        empresaSelecionada,
        appId,
        objetoSelecionado._id,
        updateData
      )

      //console.log('✅ Objeto adicionado com sucesso!')
      setDialogOpen(false)
      carregarObjetos()
    } catch (error) {
      console.error('❌ Erro ao adicionar objeto:', error)
      console.error('❌ Detalhes do erro:', {
        message: error.message,
        stack: error.stack
      })
    }
  }

  // Remover objeto da página
  const handleRemoveObject = async (itemId) => {
    try {
      const objeto = items.find(item => item._id === itemId)
      if (!objeto) return

      //console.log('🗑️ Removendo objeto da página:', objeto)

      // Verificar se appId é válido
      const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
      if (!isValidObjectId) {
        //console.log('⚠️ Não é possível remover objeto - AppId inválido:', appId)
        // Permitir remoção local mesmo sem backend válido
        setItems(items.filter(item => item._id !== itemId))
        return
      }

      // Remover a página atual da lista de páginas do objeto
      const paginasAtuais = objeto.configuracao?.paginas || []
      const novasPaginas = paginasAtuais.filter(p => p !== pagina)
      
      // Remover layout da página atual também
      const layoutsAtuais = objeto.configuracao?.layouts || {}
      const novosLayouts = { ...layoutsAtuais }
      delete novosLayouts[pagina]

      const updateData = {
        configuracao: {
          ...objeto.configuracao,
          paginas: novasPaginas,
          layouts: novosLayouts
        }
      }

      await objetoService.atualizarObjeto(
        empresaSelecionada,
        appId,
        itemId,
        updateData
      )

      //console.log(`✅ Objeto removido da página ${pagina}. ${novasPaginas.length > 0 ? 'Mantido em outras páginas.' : 'Não aparece mais em nenhuma página.'}`)
      carregarObjetos()
    } catch (error) {
      console.error('❌ Erro ao remover objeto:', error)
    }
  }

  // Renderizar item do grid
  const renderGridItem = (item, kpiRenderKey) => {
    // Determinar se o objeto tem problemas para aplicar classe CSS
    const hasError = !item.objectId || item.objectId === ''
    const noData = !getQlikAppId() && !isDevelopment
    const isChart = item.tipo !== 'kpi' // Tudo que não for KPI precisa de tamanho mínimo
    
    let cardClassName = "grid-item-card"
    if (hasError) cardClassName += " has-error"
    if (noData) cardClassName += " no-data"
    if (isChart) cardClassName += " chart-item" // Classe específica para charts/gráficos
    
    return (
      <Card 
        key={item.i}
        className={cardClassName}
        sx={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          position: 'relative',
          border: 'none',
          boxShadow: 'none',
          background: 'transparent',
        }}
      >
        {/* Controles do item - apenas em modo de edição */}
        {editMode && (
          <Box 
            className="grid-item-controls"
            sx={{
              position: 'absolute',
              top: 4,
              right: 4,
              zIndex: 9999, /* ✅ Aumentado para ficar acima dos elementos dos gráficos */
              display: 'flex',
              gap: '2px'
            }}
          >
            <Tooltip title="Arrastar para mover">
              <IconButton 
                size="small" 
                className="drag-handle grid-control-button"
              >
                <DragIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Remover da página">
              <IconButton 
                size="small"
                className="grid-control-button"
                onClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  handleRemoveObject(item._id)
                }}
                onMouseDown={(e) => {
                  if (!e.target.closest('.drag-handle')) {
                    e.stopPropagation()
                  }
                }}
                onTouchStart={(e) => {
                  if (!e.target.closest('.drag-handle')) {
                    e.stopPropagation()
                  }
                }}
                sx={{ color: 'error.main' }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        )}

        {/* Conteúdo do card - APENAS o objeto Qlik */}
        <CardContent 
          className="no-drag"
          sx={{ 
            flex: 1, 
            p: 0,
            height: '100%',
            '&:last-child': { pb: 0 }
          }}
        >
          <Box className="no-drag" sx={{ height: '100%' }}>
            <QlikObject
              key={`${item.objectId}-${kpiRenderKey}`}
              objectId={item.objectId}
              appId={getQlikAppId()}
              config={{
                tipo: item.tipo,
                nome: item.nome,
                ...item.configuracao
              }}
              onError={(error) => {
                console.error(`Erro no objeto ${item.nome}:`, error)
              }}
            />
          </Box>
        </CardContent>
      </Card>
    )
  }

  // Função para adaptar largura baseada no breakpoint
  const getResponsiveWidth = (tipo, breakpoint, originalWidth) => {
    const colsForBreakpoint = cols[breakpoint] || cols.lg
    const maxCols = colsForBreakpoint
    
    // Proporções base para cada tipo
    const proportions = {
      kpi: { lg: 0.25, md: 0.3, sm: 0.5, xs: 1, xxs: 1 }, // 25% lg, 30% md, 50% sm, 100% xs/xxs
      multiKpi: { lg: 0.5, md: 0.6, sm: 1, xs: 1, xxs: 1 }, // 50% lg, 60% md, 100% sm/xs/xxs
      chart: { lg: 0.5, md: 0.6, sm: 1, xs: 1, xxs: 1 }, // 50% lg, 60% md, 100% sm/xs/xxs
      table: { lg: 1, md: 1, sm: 1, xs: 1, xxs: 1 }, // 100% em todos
      filter: { lg: 0.33, md: 0.4, sm: 0.5, xs: 1, xxs: 1 } // 33% lg, 40% md, 50% sm, 100% xs/xxs
    }
    
    const proportion = proportions[tipo] || proportions.chart
    const calculatedWidth = Math.round(maxCols * proportion[breakpoint])
    
    // Garantir largura mínima baseada no tipo
    const minWidths = {
      kpi: 1,
      multiKpi: 2,
      chart: breakpoint === 'lg' || breakpoint === 'md' ? 4 : 3,
      table: breakpoint === 'lg' || breakpoint === 'md' ? 6 : 4,
      filter: 2
    }
    
    const minWidth = minWidths[tipo] || 2
    const finalWidth = Math.max(calculatedWidth, minWidth)
    
    // Não exceder o máximo de colunas
    return Math.min(finalWidth, maxCols)
  }

  // Função para adaptar altura baseada no breakpoint
  const getResponsiveHeight = (tipo, breakpoint, originalHeight) => {
    // Alturas base otimizadas para cada breakpoint
    const heights = {
      kpi: { lg: 3, md: 3, sm: 4, xs: 4, xxs: 4 },
      multiKpi: { lg: 4, md: 4, sm: 5, xs: 6, xxs: 6 },
      chart: { lg: 6, md: 6, sm: 7, xs: 8, xxs: 8 },
      table: { lg: 8, md: 8, sm: 10, xs: 12, xxs: 12 },
      filter: { lg: 2, md: 2, sm: 3, xs: 3, xxs: 3 }
    }
    
    const baseHeight = heights[tipo] || heights.chart
    return baseHeight[breakpoint] || baseHeight.lg
  }

  // Detectar mudanças de breakpoint e recalcular layouts
  useEffect(() => {
    const detectBreakpoint = () => {
      const width = window.innerWidth
      let newBreakpoint = 'lg'
      
      if (width >= breakpoints.lg) newBreakpoint = 'lg'
      else if (width >= breakpoints.md) newBreakpoint = 'md'
      else if (width >= breakpoints.sm) newBreakpoint = 'sm'
      else if (width >= breakpoints.xs) newBreakpoint = 'xs'
      else newBreakpoint = 'xxs'
      
      if (newBreakpoint !== currentBreakpoint) {
        //console.log(`📱 Breakpoint mudou: ${currentBreakpoint} → ${newBreakpoint} (${width}px)`)
        setCurrentBreakpoint(newBreakpoint)
        
        // Recalcular layouts se há itens carregados
        if (items.length > 0) {
          //console.log('🔄 Recalculando layouts responsivos...')
          recalcularLayoutsResponsivos()
        }
      }
    }

    const recalcularLayoutsResponsivos = () => {
      const generatedLayouts = {}
      Object.keys(breakpoints).forEach(bp => {
        generatedLayouts[bp] = items.map((item, index) => {
          const layoutSalvo = item.configuracao?.layouts?.[pagina]
          
          let finalW, finalH, finalX, finalY
          
          if (bp === 'lg' && layoutSalvo) {
            // No breakpoint lg, usar layout salvo se disponível
            finalW = layoutSalvo.w
            finalH = layoutSalvo.h
            finalX = layoutSalvo.x
            finalY = layoutSalvo.y
          } else {
            // Para outros breakpoints, calcular responsivo
            finalW = getResponsiveWidth(item.tipo, bp, item.w)
            finalH = getResponsiveHeight(item.tipo, bp, item.h)
            
            // Organizar em grid automático para breakpoints menores
            if (bp === 'xs' || bp === 'xxs') {
              // Em mobile, começar todos em x=0 e deixar compactação organizar
              finalX = 0
              finalY = 0 // Deixar a compactação vertical organizar
            } else if (bp === 'sm') {
              // Em tablet, iniciar em posições base e deixar compactação organizar
              const colsPerRow = 2
              finalX = (index % colsPerRow) * (cols[bp] / colsPerRow)
              finalY = 0 // Deixar a compactação vertical organizar
            } else {
              // Para md e lg sem layout salvo, posição base e compactação organiza
              const colsPerRow = bp === 'md' ? 2 : 3
              const colWidth = Math.floor(cols[bp] / colsPerRow)
              finalX = (index % colsPerRow) * colWidth
              finalY = 0 // Deixar a compactação vertical organizar automaticamente
            }
          }
          
          return {
            i: item.i,
            x: finalX,
            y: finalY,
            w: finalW,
            h: finalH,
            minW: getMinWidth(item.tipo),
            minH: getMinHeight(item.tipo)
          }
        })
      })
      
      setLayouts(generatedLayouts)
    }

    // Detectar breakpoint inicial
    detectBreakpoint()

    // Adicionar listener para resize com debounce
    let resizeTimeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(detectBreakpoint, 250) // Debounce de 250ms
    }

    window.addEventListener('resize', handleResize)
    
    return () => {
      window.removeEventListener('resize', handleResize)
      clearTimeout(resizeTimeout)
    }
  }, [items, currentBreakpoint, pagina]) // Dependências para recalcular quando necessário

  if (loading) {
    return (
      <Box className="grid-loading">
        <CircularProgress />
        <Typography variant="body2" sx={{ mt: 2 }}>
          Carregando objetos da página...
        </Typography>
      </Box>
    )
  }

  return (
    <Box>
      {/* Header com botão de adicionar - apenas em modo de edição */}
      {editMode && (
        <Box className="grid-header">
          <Typography variant="h6" className="grid-title">
            Layout da Página - {pagina.charAt(0).toUpperCase() + pagina.slice(1)}
          </Typography>
          
          {/* Verificar se pode adicionar objetos */}
          {(() => {
            const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(appId)
            const canAddObjects = empresaSelecionada && isValidObjectId
            
            return (
              <Tooltip 
                title={
                  !empresaSelecionada 
                    ? "Selecione uma empresa para adicionar objetos"
                    : !isValidObjectId
                    ? "App válido necessário para adicionar objetos"
                    : "Adicionar objeto já cadastrado a esta página"
                }
              >
                <span>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleAddObject}
                    disabled={!canAddObjects}
                    className="add-object-button"
                  >
                    Adicionar Objeto à Página
                  </Button>
                </span>
              </Tooltip>
            )
          })()}
        </Box>
      )}

      {/* Aviso se não tem app válido - apenas em modo de edição */}
      {editMode && empresaSelecionada && !/^[0-9a-fA-F]{24}$/.test(appId) && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>💡 App não configurado:</strong> Esta empresa não possui um app Qlik válido configurado. 
            Configure um app na página de <strong>Configurações</strong> para poder adicionar objetos a esta página.
          </Typography>
        </Alert>
      )}

      {/* Grid Layout */}
      {items.length > 0 ? (
        <ResponsiveGridLayout
          className={`layout ${editMode ? 'edit-mode' : 'view-mode'}`}
          layouts={layouts}
          onLayoutChange={editMode ? handleLayoutChange : () => {}} // Só permite mudança de layout em modo de edição
          onResizeStop={editMode ? handleResizeStop : undefined} // Novo handler para fim do resize
          breakpoints={breakpoints}
          cols={cols}
          rowHeight={60}
          margin={[12, 12]} // Margem moderada
          containerPadding={[8, 8]} // Padding moderado
          isDraggable={editMode} // Só permite arrastar em modo de edição
          isResizable={editMode} // Só permite redimensionar em modo de edição
          dragHandleClassName="drag-handle"
          useCSSTransforms={false} // Desabilitado para melhor compatibilidade
          preventCollision={false} // Permitir sobreposições temporárias durante drag
          compactType="vertical" // HABILITADO: compactação vertical para reorganização automática
          isBounded={false} // Permitir elementos fora dos limites se necessário
          autoSize={true} // Ajuste automático do container
          allowOverlap={false} // NÃO permitir sobreposição final - forçar reorganização
          resizeHandles={['se']} // Apenas handle inferior direito para resize
          draggableCancel=".no-drag" // Elementos que não devem ser arrastáveis
          draggableHandle=".drag-handle" // Apenas o handle permite arrastar
        >
          {items.map((item) => renderGridItem(item, kpiRenderKey))}
        </ResponsiveGridLayout>
      ) : (
        // Só mostrar mensagem de vazio em modo de edição
        editMode && (
          <Alert severity="info" className="grid-empty-state">
            <Typography variant="h6" sx={{ mb: 1 }}>
              📊 Nenhum objeto configurado para esta página
            </Typography>
            <Typography variant="body2">
              {empresaSelecionada && /^[0-9a-fA-F]{24}$/.test(appId)
                ? "Esta página ainda não possui objetos adicionados. Clique em \"Adicionar Objeto à Página\" para escolher dentre os objetos já cadastrados no sistema."
                : "Configure um app Qlik válido na página de Configurações para poder adicionar objetos."
              }
            </Typography>
          </Alert>
        )
      )}

      {/* Dialog para adicionar/editar objeto */}
      <Dialog 
        open={dialogOpen} 
        onClose={() => setDialogOpen(false)} 
        maxWidth="sm" 
        fullWidth
        className="add-object-dialog"
      >
        <DialogTitle>
          {availableObjects.length > 0 ? 
            `Adicionar Objeto à Página ${pagina.charAt(0).toUpperCase() + pagina.slice(1)}` : 
            'Nenhum Objeto Disponível'
          }
        </DialogTitle>
        
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            {/* Escolher objeto existente - só se houver objetos disponíveis */}
            {availableObjects.length > 0 ? (
              <>
                <Typography variant="h6" sx={{ mb: 1, color: 'primary.main' }}>
                  📋 Adicionar Objeto à Página
                </Typography>
                
                <FormControl fullWidth>
                  <InputLabel>Selecionar Objeto</InputLabel>
                  <Select
                    value={newItem.objectId && availableObjects.find(obj => obj._id === newItem.objectId) ? newItem.objectId : ''}
                    onChange={(e) => {
                      const selectedObj = availableObjects.find(obj => obj._id === e.target.value)
                      if (selectedObj) {
                        setNewItem({
                          ...selectedObj,
                          objectId: e.target.value, // Manter o ID para indicar seleção
                          categoria: pagina
                        })
                      }
                    }}
                    label="Selecionar Objeto"
                    MenuProps={{
                      PaperProps: {
                        sx: {
                          maxHeight: 350,
                          '& .MuiMenuItem-root': {
                            fontSize: '0.875rem'
                          }
                        }
                      }
                    }}
                  >
                    <MenuItem value="">
                      <em>Selecione um objeto...</em>
                    </MenuItem>
                    {availableObjects.map((obj) => (
                      <MenuItem key={obj._id} value={obj._id}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                            {obj.nome}
                          </Typography>
                          <Chip 
                            label={obj.tipo.toUpperCase()} 
                            size="small" 
                            color="primary" 
                            variant="outlined" 
                          />
                          <Typography variant="caption" color="text.secondary">
                            ID: {obj.objectId || 'Simulado'}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                {/* Mostrar detalhes do objeto selecionado */}
                {newItem.objectId && availableObjects.find(obj => obj._id === newItem.objectId) && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <Typography variant="body2">
                      <strong>Objeto selecionado:</strong> {newItem.nome}<br />
                      <strong>Tipo:</strong> {newItem.tipo}<br />
                      <strong>Descrição:</strong> {newItem.descricao || 'Sem descrição'}<br />
                      <strong>Object ID:</strong> {newItem.objectId || 'Modo simulado'}
                    </Typography>
                  </Alert>
                )}
              </>
            ) : (
              /* Nenhum objeto disponível */
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <ObjectIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  Nenhum objeto disponível
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Todos os objetos já estão adicionados a esta página ou não há objetos cadastrados para este app.
                </Typography>
                <Typography variant="body2" color="primary.main" sx={{ fontStyle: 'italic' }}>
                  💡 Vá até <strong>Configurações</strong> para criar novos objetos para este app.
                </Typography>
              </Box>
            )}
          </Box>
        </DialogContent>
        
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Cancelar
          </Button>
          {availableObjects.length > 0 && (
            <Button 
              onClick={handleAddExistingObject}
              variant="contained"
              disabled={!newItem.objectId || !availableObjects.find(obj => obj._id === newItem.objectId)}
            >
              📋 Adicionar à Página
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default GridLayout 