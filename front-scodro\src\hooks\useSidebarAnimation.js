import { useState, useEffect, useCallback } from 'react'

export const useSidebarAnimation = (sidebarCollapsed) => {
  const [isAnimating, setIsAnimating] = useState(false)
  const [animationPhase, setAnimationPhase] = useState('idle')

  const startAnimation = useCallback(() => {
    setIsAnimating(true)
    setAnimationPhase('start')
    
    // Fase de transição
    setTimeout(() => {
      setAnimationPhase('transition')
    }, 50)
    
    // Fase de finalização
    setTimeout(() => {
      setAnimationPhase('end')
      setIsAnimating(false)
    }, 400)
  }, [])

  useEffect(() => {
    startAnimation()
  }, [sidebarCollapsed, startAnimation])

  return {
    isAnimating,
    animationPhase,
    animationDuration: 400,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
}

export default useSidebarAnimation 