const Objeto = require('../models/Objeto');
const Empresa = require('../models/Empresa');
const App = require('../models/App');
const { validarObjeto, validarObjetoUpdate } = require('../validators/objetoValidator');

class ObjetoController {
  // Listar objetos por empresa
  async listarObjetosPorEmpresa(req, res) {
    try {
      const { empresaId } = req.params;
      const { categoria, tipo, ativo } = req.query;

      // Verificar se a empresa existe
      const empresa = await Empresa.findByCustomId(empresaId);
      if (!empresa) {
        return res.status(404).json({
          success: false,
          error: 'Empresa não encontrada'
        });
      }

      let query = { empresaId };
      
      if (categoria) query.categoria = categoria;
      if (tipo) query.tipo = tipo;
      if (ativo !== undefined) query.ativo = ativo === 'true';

      const objetos = await Objeto.find(query).sort({ categoria: 1, ordem: 1 });

      res.json({
        success: true,
        data: objetos,
        total: objetos.length,
        empresa: {
          id: empresa.id,
          nome: empresa.nome
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Erro ao listar objetos',
        message: error.message
      });
    }
  }

  // Listar objetos por app
  async listarObjetosPorApp(req, res) {
    try {
      const { empresaId, appId } = req.params;
      const { tipo, categoria, ativo } = req.query;

      const filtros = {};
      if (tipo) filtros.tipo = tipo;
      if (categoria) filtros.categoria = categoria;
      if (ativo !== undefined) filtros.ativo = ativo === 'true';

      const objetos = await Objeto.findByApp(empresaId, appId, filtros);

      res.json({
        success: true,
        data: objetos,
        total: objetos.length
      });
    } catch (error) {
      console.error('Erro ao listar objetos:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }

  // Obter objeto específico
  async obterObjeto(req, res) {
    try {
      const { empresaId, appId, objetoId } = req.params;

      const objeto = await Objeto.findOne({ 
        _id: objetoId, 
        empresaId, 
        appId 
      });

      if (!objeto) {
        return res.status(404).json({
          success: false,
          error: 'Objeto não encontrado'
        });
      }

      res.json({
        success: true,
        data: objeto
      });
    } catch (error) {
      console.error('Erro ao obter objeto:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }

  // Criar novo objeto
  async criarObjeto(req, res) {
    try {
      const { empresaId, appId } = req.params;
      const dadosObjeto = req.body;

      // Verificar se o app existe
      const app = await App.findOne({ _id: appId, empresaId });
      if (!app) {
        return res.status(404).json({
          success: false,
          error: 'App não encontrado'
        });
      }

      // Verificar se já existe objeto com a mesma chave no app
      const objetoExistente = await Objeto.findByChave(empresaId, appId, dadosObjeto.chave);
      if (objetoExistente) {
        return res.status(400).json({
          success: false,
          error: 'Já existe um objeto com esta chave neste app'
        });
      }

      // Aplicar configurações padrão se não fornecidas
      if (!dadosObjeto.configuracao) {
        dadosObjeto.configuracao = {};
      }

      // Configurações padrão para Multi KPI
      if (dadosObjeto.tipo === 'multiKpi' && !dadosObjeto.configuracao.multiKpiConfig) {
        dadosObjeto.configuracao.multiKpiConfig = {
          itemsPerRow: 3,
          spacing: 2,
          showTitle: true,
          showIcon: true,
          layout: 'unified',
          elevation: 1,
          cor: '#1976d2',
          corFonte: '#ffffff'
        };
      }

      // Configurações padrão para KPI
      if (dadosObjeto.tipo === 'kpi' && !dadosObjeto.configuracao.kpiConfig) {
        dadosObjeto.configuracao.kpiConfig = {
          formato: 'numero',
          casasDecimais: 0,
          prefix: '',
          suffix: '',
          showTrend: false,
          cor: '#1976d2',
          corFonte: '#ffffff',
          icone: 'analytics',
          showIcon: true,
          gradiente: false
        };
      }

      // ✅ NOVO: Configurações padrão para Gráficos (incluindo novos tipos)
      const isChartType = ['bar', 'line', 'area', 'pie', 'donut', 'column', 'scatter', 'mixed', 'gauge'].includes(dadosObjeto.tipo);
      if (isChartType && !dadosObjeto.configuracao.chartConfig) {
        dadosObjeto.configuracao.chartConfig = {
          tipoGrafico: dadosObjeto.tipo,
          showLegend: true,
          showDataLabels: false,
          showTitle: true,
          animacao: true,
          color: ['#667eea', '#764ba2'],
          // Configurações de DataZoom
          dataZoom: {
            enabled: false,
            type: 'slider',
            start: 0,
            end: 100,
            showDetail: true,
            realtime: true
          },
          // Configurações para Gráfico Misto
          mixedConfig: {
            barSeries: [0],
            lineSeries: [1]
          }
        };
      }



      // Obter próxima ordem
      const proximaOrdem = await Objeto.getProximaOrdem(empresaId, appId);

      const novoObjeto = new Objeto({
        empresaId,
        appId,
        ...dadosObjeto,
        ordem: proximaOrdem
      });

      await novoObjeto.save();

      res.status(201).json({
        success: true,
        data: novoObjeto,
        message: 'Objeto criado com sucesso'
      });
    } catch (error) {
      console.error('Erro ao criar objeto:', error);
      
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          error: 'Dados inválidos',
          detalhes: Object.values(error.errors).map(err => err.message)
        });
      }

      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }



  // Atualizar objeto
  async atualizarObjeto(req, res) {
    try {
      const { empresaId, appId, objetoId } = req.params;
      const dadosObjeto = req.body;

      const objeto = await Objeto.findOne({ 
        _id: objetoId, 
        empresaId, 
        appId 
      });

      if (!objeto) {
        return res.status(404).json({
          success: false,
          error: 'Objeto não encontrado'
        });
      }

      // Se mudou a chave, verificar se não existe outra com o mesmo nome
      if (dadosObjeto.chave && dadosObjeto.chave !== objeto.chave) {
        const objetoExistente = await Objeto.findByChave(empresaId, appId, dadosObjeto.chave);
        if (objetoExistente) {
          return res.status(400).json({
            success: false,
            error: 'Já existe um objeto com esta chave neste app'
          });
        }
      }



      Object.assign(objeto, dadosObjeto);
      await objeto.save();

      res.json({
        success: true,
        data: objeto,
        message: 'Objeto atualizado com sucesso'
      });
    } catch (error) {
      console.error('Erro ao atualizar objeto:', error);
      
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          error: 'Dados inválidos',
          detalhes: Object.values(error.errors).map(err => err.message)
        });
      }

      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }

  // Excluir objeto
  async excluirObjeto(req, res) {
    try {
      const { empresaId, appId, objetoId } = req.params;
      const { forceDelete = 'false' } = req.query;

      const objeto = await Objeto.findOne({ 
        _id: objetoId, 
        empresaId, 
        appId 
      });

      if (!objeto) {
        return res.status(404).json({
          success: false,
          error: 'Objeto não encontrado'
        });
      }

      if (forceDelete === 'true') {
        await Objeto.deleteOne({ _id: objetoId, empresaId, appId });
        res.json({
          success: true,
          message: 'Objeto excluído permanentemente'
        });
      } else {
        objeto.ativo = false;
        await objeto.save();
        res.json({
          success: true,
          message: 'Objeto desativado com sucesso'
        });
      }
    } catch (error) {
      console.error('Erro ao excluir objeto:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }

  // Criar múltiplos objetos
  async criarMultiplosObjetos(req, res) {
    try {
      const { empresaId, appId } = req.params;
      const { objetos } = req.body;

      if (!Array.isArray(objetos)) {
        return res.status(400).json({
          success: false,
          error: 'Lista de objetos deve ser um array'
        });
      }

      // Verificar se o app existe
      const app = await App.findOne({ _id: appId, empresaId });
      if (!app) {
        return res.status(404).json({
          success: false,
          error: 'App não encontrado'
        });
      }

      const objetosCriados = [];
      const erros = [];

      for (let i = 0; i < objetos.length; i++) {
        try {
          const dadosObjeto = objetos[i];
          
          // Verificar se já existe objeto com a mesma chave
          const objetoExistente = await Objeto.findByChave(empresaId, appId, dadosObjeto.chave);
          if (objetoExistente) {
            erros.push(`Objeto ${dadosObjeto.chave}: já existe com esta chave`);
            continue;
          }

          // Obter próxima ordem
          const proximaOrdem = await Objeto.getProximaOrdem(empresaId, appId);

          const novoObjeto = new Objeto({
            empresaId,
            appId,
            ...dadosObjeto,
            ordem: proximaOrdem
          });

          await novoObjeto.save();
          objetosCriados.push(novoObjeto);
        } catch (error) {
          erros.push(`Objeto ${objetos[i].chave || i}: ${error.message}`);
        }
      }

      res.status(201).json({
        success: true,
        data: {
          objetosCriados,
          totalCriados: objetosCriados.length,
          erros
        },
        message: `${objetosCriados.length} objetos criados com sucesso`
      });
    } catch (error) {
      console.error('Erro ao criar múltiplos objetos:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }

  // Obter objetos por categoria
  async obterObjetosPorCategoria(req, res) {
    try {
      const { empresaId, appId, categoria } = req.params;

      const objetos = await Objeto.findByApp(empresaId, appId, { categoria, ativo: true });

      res.json({
        success: true,
        data: objetos,
        total: objetos.length
      });
    } catch (error) {
      console.error('Erro ao obter objetos por categoria:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }

  // Reordenar objetos
  async reordenarObjetos(req, res) {
    try {
      const { empresaId, appId } = req.params;
      const { objetos } = req.body;

      if (!Array.isArray(objetos)) {
        return res.status(400).json({
          success: false,
          error: 'Lista de objetos deve ser um array'
        });
      }

      await Objeto.reordenarObjetos(empresaId, appId, objetos);

      res.json({
        success: true,
        message: 'Objetos reordenados com sucesso'
      });
    } catch (error) {
      console.error('Erro ao reordenar objetos:', error);
      res.status(500).json({
        success: false,
        error: 'Erro interno do servidor',
        message: error.message
      });
    }
  }
}

module.exports = new ObjetoController(); 