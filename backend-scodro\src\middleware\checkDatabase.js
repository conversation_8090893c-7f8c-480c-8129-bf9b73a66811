const mongoose = require('mongoose');

const checkDatabase = (req, res, next) => {
  // Verificar se o MongoDB está conectado
  if (mongoose.connection.readyState !== 1) {
    return res.status(503).json({
      success: false,
      error: 'Banco de dados não disponível',
      message: 'MongoDB não está conectado. Verifique a configuração.',
      details: {
        readyState: mongoose.connection.readyState,
        states: {
          0: 'disconnected',
          1: 'connected',
          2: 'connecting',
          3: 'disconnecting'
        }
      }
    });
  }
  
  next();
};

module.exports = checkDatabase; 