import React from 'react'
import { Box, Paper, Typography, Alert, Divider } from '@mui/material'
import QlikObject from '../QlikObject/QlikObject'

const TestMixed = () => {
  // Configurações de teste para gráfico misto
  const configMixed = {
    tipo: 'mixed',
    chartConfig: {
      tipoGrafico: 'mixed',
      showTitle: true,
      showLegend: true,
      showAxes: true,
      showDataLabels: false,
      animacao: true,
      mixedConfig: {
        barSeries: [0],        // Primeira série como barras
        lineSeries: [1, 2]     // Segunda e terceira séries como linhas
      },
      dataZoom: {
        enabled: true,
        type: 'slider',
        start: 0,
        end: 100,
        showDetail: true,
        realtime: true
      }
    }
  }

  // Configuração de teste para barLine alias
  const configBarLine = {
    tipo: 'barLine',
    chartConfig: {
      tipoGrafico: 'barLine',
      showTitle: true,
      showLegend: true,
      showAxes: true,
      showDataLabels: false,
      animacao: true,
      mixedConfig: {
        barSeries: [0],
        lineSeries: [1, 2]
      }
    }
  }

  // Configuração de teste básica (para comparar)
  const configBar = {
    tipo: 'bar',
    chartConfig: {
      tipoGrafico: 'bar',
      showTitle: true,
      showLegend: true,
      showAxes: true,
      showDataLabels: false,
      animacao: true
    }
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom color="primary.main">
        🧪 Teste de Gráficos Mistos - Simulação
      </Typography>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          <strong>🔍 Este teste verifica:</strong><br/>
          1. ✅ Se o tipo 'mixed' está sendo reconhecido corretamente<br/>
          2. ✅ Se dados simulados têm múltiplas séries para gráficos mistos<br/>
          3. ✅ Se a configuração chartConfig.tipoGrafico está sendo passada corretamente<br/>
          4. ✅ Se aliases como 'barLine' funcionam<br/>
          <strong>👀 Verifique os logs do console para debug detalhado</strong>
        </Typography>
      </Alert>

      <Box sx={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: 3
      }}>
        
        {/* Teste 1: Gráfico Misto (mixed) */}
        <Paper sx={{ p: 3, height: 500 }}>
          <Typography variant="h6" gutterBottom color="success.main">
            🔥 Teste 1: Gráfico Misto (tipo: 'mixed')
          </Typography>
          <Typography variant="caption" display="block" sx={{ mb: 2 }}>
            <strong>Configuração:</strong> tipo='mixed', tipoGrafico='mixed'<br/>
            <strong>Esperado:</strong> 3 séries, barras + linhas, dataZoom habilitado
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Box sx={{ height: 380 }}>
            <QlikObject
              objectId="test-mixed-001"
              appId="test-app"
              config={configMixed}
            />
          </Box>
        </Paper>

        {/* Teste 2: Alias barLine */}
        <Paper sx={{ p: 3, height: 500 }}>
          <Typography variant="h6" gutterBottom color="warning.main">
            🔥 Teste 2: Alias barLine
          </Typography>
          <Typography variant="caption" display="block" sx={{ mb: 2 }}>
            <strong>Configuração:</strong> tipo='barLine', tipoGrafico='barLine'<br/>
            <strong>Esperado:</strong> Deve funcionar como 'mixed'
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Box sx={{ height: 380 }}>
            <QlikObject
              objectId="test-barline-002"
              appId="test-app"
              config={configBarLine}
            />
          </Box>
        </Paper>

        {/* Teste 3: Gráfico de Barras Normal (para comparar) */}
        <Paper sx={{ p: 3, height: 500 }}>
          <Typography variant="h6" gutterBottom color="info.main">
            📊 Teste 3: Barras Normal (comparação)
          </Typography>
          <Typography variant="caption" display="block" sx={{ mb: 2 }}>
            <strong>Configuração:</strong> tipo='bar', tipoGrafico='bar'<br/>
            <strong>Esperado:</strong> Só barras, 1-2 séries
          </Typography>
          <Divider sx={{ mb: 2 }} />
          <Box sx={{ height: 380 }}>
            <QlikObject
              objectId="test-bar-003"
              appId="test-app"
              config={configBar}
            />
          </Box>
        </Paper>

      </Box>

      <Alert severity="success" sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom color="success.dark">
          ✅ O que deve aparecer nos logs:
        </Typography>
        <Typography variant="body2" component="div">
          <strong>1. 🎲 Gerando dados simulados para gráfico tipo: mixed</strong><br/>
          <strong>2. 📈 DEBUG_RENDER_CHART:</strong> com tipoDetectado='mixed'<br/>
          <strong>3. 🔍 DEBUG_CHART_CONFIG:</strong> com tipoGrafico='mixed'<br/>
          <strong>4. 🔍 DEBUG_getEchartsType:</strong> Recebido tipo "mixed"<br/>
          <strong>5. 📊 DEBUG_CUSTOMCHART_PROCESSED:</strong> com 3 séries<br/>
          <strong>6. isMixed: true</strong> (ao invés de false)
        </Typography>
      </Alert>

      <Alert severity="error" sx={{ mt: 2 }}>
        <Typography variant="h6" gutterBottom color="error.dark">
          ❌ Se ainda aparecer nos logs:
        </Typography>
        <Typography variant="body2">
          <strong>• tipoGrafico: 'bar'</strong> ao invés de 'mixed'<br/>
          <strong>• isMixed: false</strong><br/>
          <strong>• Apenas 1 série</strong> nos dados simulados<br/>
          <strong>• DEBUG_getEchartsType: Recebido tipo "bar"</strong><br/>
          → Então ainda há problema na configuração
        </Typography>
      </Alert>
    </Box>
  )
}

export default TestMixed 