# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
build/
dist/
mashup-cloud/
mashup-enterprise/

# Cache
.cache/
.parcel-cache/

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/

# OS generated files
Thumbs.db
.DS_Store

# IDE
.vscode/
.idea/
*.swp
*.swo

# Qlik specific
*.qvf
*.qvw 